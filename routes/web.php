<?php

use App\Classes\Proxy;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\DomeinenController;
use App\Http\Controllers\GebruikersController;
use App\Http\Controllers\StatistiekenController;
use App\Http\Controllers\SupervisionController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AdressenController;
use App\Http\Controllers\ComplexenController;
use App\Http\Controllers\DataController;
use App\Http\Controllers\DocumentatieController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\InstellingenController;
use App\Http\Controllers\MailsController;
use App\Http\Controllers\MeterkastkaartController;
use App\Http\Controllers\NieuwsController;
use App\Http\Controllers\ProjectenController;
use App\Http\Controllers\HistorischebronnenimportController;
use App\Http\Controllers\BedrijvenController;
use App\Http\Controllers\SearchController;
use App\Http\Controllers\IframeController;
use App\Http\Controllers\DebugController;
use App\Http\Controllers\ApiController;
use App\Http\Controllers\ContactenController;
use App\Http\Controllers\Api\FilesController;
use App\Http\Controllers\CorporatiesController;
use App\Http\Controllers\RollenController;
use App\Http\Controllers\AsbestBronnenController;
use App\Http\Controllers\KoppelingenController;
use App\Http\Controllers\PasswordsController;
use App\Http\Controllers\LogsController;
use App\Http\Controllers\LAVSController;

use Livewire\Controllers\FileUploadHandler;
Route::post('/dashboard/livewire/upload-file', [FileUploadHandler::class, 'handle'])->name('livewire.upload-file');

Route::prefix('/dashboard')->group( function (){



    //Auth Routes
    Auth::routes(['register' => false, 'reset' => false]);
    Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [LoginController::class, 'login']);
    Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

    //Api
    Route::prefix('/api')->group(function(){

        //Non Auth GUID API
        Route::middleware(['domain.guid.setup'])->group(function(){

            Route::prefix('/file')->group(function(){
                Route::get('/get/{guid}', [FilesController::class, 'get']);
            });
            Route::prefix('/lavs')->group(function(){
                Route::post('/complete/sanering-opleveren', [LAVSController::class, 'saneringOpleverenBevestigen']);
            });

        });

        //Auth API
        Route::middleware(['domain.setup', 'auth'])->group(function(){

            //Utilities
            Route::post('/location', [ApiController::class, 'location']);
            Route::post('/search/{model}', [SearchController::class, 'search']);

            Route::prefix('/file')->group(function(){
                Route::post('/upload', [FilesController::class, 'upload']);
            });
            Route::prefix('/projecten')->group(function(){
                Route::post('/get', [ProjectenController::class, 'get']);
                Route::prefix('/set')->group(function(){
                    Route::post('/status', [ProjectenController::class, 'setStatus'])->middleware('has.permission:projecten_change_status');
                });
            });
            Route::prefix('/complexen')->group(function(){
                Route::post('/get', [ComplexenController::class, 'get']);

                Route::prefix('/asbest/')->group(function(){
                    Route::post('/get/uitvoerkosten', [ComplexenController::class, 'getAsbestUitvoerkosten']);
                });

            });
            Route::prefix('/adressen')->group(function(){
                Route::post('/get', [AdressenController::class, 'get']);
            });
            Route::prefix('/bedrijven')->group(function(){
                Route::post('/get', [BedrijvenController::class, 'get']);
            });
            Route::prefix('/corporaties')->group(function(){
                Route::post('/get', [CorporatiesController::class, 'get']);
            });
            Route::prefix('/gebruikers')->group(function(){
                Route::post('/get', [GebruikersController::class, 'get']);
            });
            Route::prefix('/contacten')->group(function(){
                Route::post('/get', [ContactenController::class, 'get']);
            });
            Route::prefix('/rollen')->group(function(){
                Route::post('/get', [RollenController::class, 'get']);
            });
            Route::prefix('/asbest')->group(function(){
                Route::prefix('/bronnen')->group(function(){
                    Route::post('get', [AsbestBronnenController::class, 'get']);
                });
            });
            Route::prefix('/mails')->group(function(){
                Route::post('get', [MailsController::class, 'get']);
            });
            Route::prefix('/koppelingen')->group(function(){
                Route::post('/get', [KoppelingenController::class, 'get']);
            });
            Route::prefix('/logs')->middleware('has.permission:logs_inzien')->group(function(){
                Route::post('/get', [LogsController::class, 'get']);
            });

        });

        //Koppelingen API
        Route::middleware(['stappen.api.inbound'])->group(function(){
            Route::post('koppelingen/{method}', [KoppelingenController::class, 'inbound']);
        });

    });

    //Auth
    Route::middleware(['auth', 'domain.setup', 'password.reset'])->group(function(){

        //Dashboard
        Route::get('/', [HomeController::class, 'home']);
        Route::get('/home', [HomeController::class, 'home']);
        Route::get('/welcome', [HomeController::class, 'welcome']);
        Route::get('/test', [DebugController::class, 'test']);

        Route::prefix('password')->group(function(){
            Route::get('/reset', [PasswordsController::class, 'reset']);
            Route::post('/reset', [PasswordsController::class, 'update']);
        });
        Route::prefix('domeinen')->group(function(){
            Route::get('/switch/{id}', [DomeinenController::class, 'switch']);
        });
        Route::prefix('instellingen')->middleware('has.permission:instellingen_beheren')->group(function(){
            Route::prefix('projecten')->group(function(){
                Route::get('/', [InstellingenController::class, 'projecten']);
            });
            Route::prefix('adressen')->group(function(){
                Route::get('/', [InstellingenController::class, 'adressen']);
            });
            Route::prefix('processen')->group(function(){
                Route::get('/', [InstellingenController::class, 'processen']);
                Route::get('/settings/{id}', [InstellingenController::class, 'procesSEttings']);
                Route::get('/stappen/{id}', [InstellingenController::class, 'procesStappen']);
                Route::get('/bestanden/{id}', [InstellingenController::class, 'procesBestanden']);
            });
            Route::prefix('stappen')->group(function(){
                Route::get('/{id}', [InstellingenController::class, 'stap']);
            });
            Route::prefix('lijsten')->group(function(){
                Route::get('/', [InstellingenController::class, 'lijsten']);
                Route::get('/locaties', [InstellingenController::class, 'locaties']);
                Route::get('/bronnen', [InstellingenController::class, 'bronnen']);
                Route::get('/risicoklassen', [InstellingenController::class, 'risicoklassen']);
            });
        });
        Route::prefix('projecten')->group(function(){

            Route::get('/', [ProjectenController::class, 'index'])->middleware('has.permission:projecten_index');
            Route::get('/new/{proces}', [ProjectenController::class, 'create'])->middleware('has.permission:projecten_create');
            Route::get('/project/{id}', [ProjectenController::class, 'stap']);

            Route::prefix('opdrachtformulier')->group(function(){
                Route::get('/{guid}', [ProjectenController::class, 'opdrachtformulier']);
            });
        });
        Route::prefix('adressen')->group(function(){
            Route::get('/', [AdressenController::class, 'index'])->middleware('has.permission:adressen_index');
            Route::get('/adres/{id}', [AdressenController::class, 'adres'])->middleware('has.permission:adressen_info');
            Route::get('/meterkastkaart/{id}', [AdressenController::class, 'meterkastkaart']);
            Route::get('/qrkaart/{id}', [AdressenController::class, 'qrkaart']);
        });
        Route::prefix('bedrijven')->group(function(){
            Route::get('/', [BedrijvenController::class, 'index']);
            Route::get('/create', [BedrijvenController::class, 'bedrijf']);
            Route::get('/edit/{id?}', [BedrijvenController::class, 'bedrijf']);
        });
        Route::prefix('corporaties')->group(function(){
            Route::get('/', [CorporatiesController::class, 'index']);
            Route::get('/create', [CorporatiesController::class, 'corporatie']);
            Route::get('/edit/{id?}', [CorporatiesController::class, 'corporatie']);
        });
        Route::prefix('gebruikers')->middleware('has.permission:gebruikers_beheren')->group(function(){
            Route::get('/', [GebruikersController::class, 'index']);
            Route::get('/create', [GebruikersController::class, 'gebruiker']);
            Route::get('/edit/{id}', [GebruikersController::class, 'gebruiker']);
        });
        Route::prefix('contacten')->middleware('has.permission:contacten_beheren')->group(function(){
            Route::get('/', [ContactenController::class, 'index']);
            Route::get('/create', [ContactenController::class, 'contact']);
            Route::get('/edit/{id}', [ContactenController::class, 'contact']);
        });
        Route::prefix('rollen')->middleware('has.permission:rollen_beheren adressen_info')->group(function(){
            Route::get('/', [RollenController::class, 'index']);
            Route::get('/edit/{id}', [RollenController::class, 'role']);
        });
        Route::prefix('complexen')->group(function(){
            Route::prefix('asbest')->group(function(){
                Route::get('/matrixen', [ComplexenController::class, 'matrix']);
            });

            Route::get('/', [ComplexenController::class, 'index'])->middleware('has.permission:complexen_index');
            Route::get('/{id}/', [ComplexenController::class, 'complex'])->middleware('has.permission:complexen_info');
        });
        Route::prefix('statistieken')->group(function(){
            Route::get('/asbest', [StatistiekenController::class, 'asbest']);
        });
        Route::prefix('supervision')->group(function(){
            Route::get('/', [SupervisionController::class, 'index']);
            Route::get('/user/{id}', [SupervisionController::class, 'user']);
        });
        Route::prefix('/asbest')->group(function(){
            Route::get('/bronnen', [AsbestBronnenController::class, 'bronnen']);
        });
        Route::prefix('/mails')->group(function(){
            Route::get('/sent', [MailsController::class, 'sent'])->middleware('has.permission:emails_verzonden_inzien');
            Route::get('/templates', [MailsController::class, 'templates'])->middleware('has.permission:emails_templates_beheren');;
        });
        Route::prefix('/koppelingen')->group(function(){
            Route::get('/', [KoppelingenController::class, 'index'])->middleware('has.permission:koppelingen_requests_inzien');
        });
        Route::prefix('/logs')->middleware('has.permission:logs_inzien')->group(function(){
            Route::get('/', [LogsController::class, 'index']);
        });

        //Iframe
        Route::prefix('/iframe')->group(function(){
            Route::get('/editor', [IframeController::class, 'editor']);
        });

    });

    //Non Auth
    Route::prefix('login')->group(function(){
        Route::prefix('token')->group(function(){
            Route::get('/{token}', [GebruikersController::class, 'loginUsingToken']);
        });
        Route::prefix('sso')->group(function(){
            Route::get('/redirect', [GebruikersController::class, 'redirectSSO']);
            Route::get('/{domain_key}', [GebruikersController::class, 'loginUsingSSO']);
        });
    });

    //Non Auth GUID
    Route::middleware(['domain.guid.setup'])->group(function(){
        Route::prefix('/lavs')->group(function(){
            Route::get('/stap/sanering-opleveren/{guid}', [LAVSController::class, 'saneringOpleveren']);
        });
    });

    //Proxies
    Proxy::get('/sso.php', '/dashboard/login/sso/redirect');
    Proxy::get('/meterkastkaart.php', '/dashboard/meterkastkaart');

});

//TODO: Clean up / refactor

//Auth Old
Route::middleware([ 'auth', 'domain.setup', 'password.reset' ])->group( function () {
    
    //TODO change to Main page
    Route::get('/', [HomeController::class, 'home']);
    Route::get('/home', [HomeController::class, 'home']);

    Route::prefix('/dashboard')->middleware([ 'auth', 'domain.setup' ])->group( function () {


        // Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');

        Route::get('/switch/{id}', [HomeController::class, 'switch'])->name('switch');
        Route::prefix('instellingen')->group(function () {

            Route::get('/', [InstellingenController::class, 'index']);

            Route::get('/redenen', [InstellingenController::class, 'redenen']);
            Route::get('/redenen/new/{onderdeel}', [InstellingenController::class, 'newreden']);
            Route::post('/redenen/new/{onderdeel}', [InstellingenController::class, 'storereden']);
            Route::get('/redenen/remove/{id}', [InstellingenController::class, 'removereden']);
        });
        Route::prefix('data')->group(function () {
            Route::get('/statistieken', [DataController::class, 'statistieken']);
            Route::get('/adressen', [DataController::class, 'adressen']);
            Route::get('/bronnen', [DataController::class, 'bronnen']);
            Route::get('/projecten', [DataController::class, 'projecten']);
            Route::get('/volgtraject', [DataController::class, 'volgtraject']);
        });
        Route::prefix('nieuws')->group(function () {
            Route::get('/', [NieuwsController::class, 'index']);
            Route::get('/{id}', [NieuwsController::class, 'bericht']);
        });
        Route::prefix('documentatie')->group(function () {
            Route::get('/', [DocumentatieController::class, 'index'])->name('documentatie');
            Route::post('/{id}', [AdressenController::class, 'storefile']);
            Route::get('/remove/{id}', [InstellingenController::class, 'removereden']);
        });
        Route::get('/qrkaart', [MeterkastkaartController::class, 'qrkaart'])->name('qrkaart');
        Route::post('api/statistiek', [DataController::class, 'statistiek']);


        // Doel: Inladen en opschonen (/gelijktrekken met Database) historische data
        // Menu voor tijdelijk importeren historische data
        Route::prefix('tempimport')->group(function () {
            Route::get('/', [HistorischebronnenimportController::class, 'menu']);
        });

        // Stap 1: Splitsen van adressen
        Route::prefix('historischebronnenimport')->group(function () {
            Route::get('/', [HistorischebronnenimportController::class, 'index']);
        });
        Route::prefix('historischebronnenimportsaneerder')->group(function () {
            Route::get('/', [HistorischebronnenimportController::class, 'indexsaneerder']);
        });

        // Stap 2: Vervangen van waardes zodat ze overeenkomen met Database (ID's)
        Route::prefix('replace')->group(function () {
            Route::get('/risicoklasse', [HistorischebronnenimportController::class, 'replacerisicoklasse']);
            Route::get('/risicoklasse/{saneerder}', [HistorischebronnenimportController::class, 'replacerisicoklasse']);
            Route::get('/locatie', [HistorischebronnenimportController::class, 'replacelocatie']);
            Route::get('/locatie/{saneerder}', [HistorischebronnenimportController::class, 'replacelocatie']);
            Route::get('/omschrijving', [HistorischebronnenimportController::class, 'replaceomschrijving']);
            Route::get('/omschrijving/{saneerder}', [HistorischebronnenimportController::class, 'replaceomschrijving']);
        });

        // Stap 3: Gesaneerd > Aangeven als een bron wel/niet gesaneerd is
        Route::prefix('gesaneerd')->group(function () {
            Route::get('/', [HistorischebronnenimportController::class, 'indexgesaneerd']);
        });

        // Stap 4: Aanmaken projecten
        // (Toevoegen rapporten?)
    });

});

//Non Auth
Route::middleware(['domain.setup'])->group( function () {
    Route::get('/meterkastkaart.php', [MeterkastkaartController::class, 'meterkastkaart']);
});

// !!!!!!!! METERKASTKAARTEN, KOPPELINGEN, WACHTWOORD VERGETEN, PROJECTSTAP !!!!!!!!!!!
