<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

use App\Http\Controllers\AdressenController;
use App\Http\Controllers\ComplexenController;
use App\Http\Controllers\DataController;
use App\Http\Controllers\DocumentatieController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\InstellingenController;
use App\Http\Controllers\MailsController;
use App\Http\Controllers\MeterkastkaartController;
use App\Http\Controllers\NieuwsController;
use App\Http\Controllers\ProjectenController;
use App\Http\Controllers\HistorischebronnenimportController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::middleware([ 'auth', 'domain.setup' ])->group( function () {

    // Adressen data
    Route::get('getcomplexadressen/{complexnummer}', [HistorischebronnenimportController::class, 'getcomplexadressen']);

    //Stap 1: Opsplitsen adressen
    // Inventariseerder data
    Route::post('get100tempbronnen', [HistorischebronnenimportController::class, 'get100bronnen']);
    Route::post('addtempbronnen', [HistorischebronnenimportController::class, 'addbronnen']);

    // Saneerder data
    Route::post('get100tempbronnensaneerder', [HistorischebronnenimportController::class, 'get100bronnensaneerder']);
    Route::post('addtempbronnensaneerder', [HistorischebronnenimportController::class, 'addbronnensaneerder']);

    //Stap 2: Vervangen ID's met ID's in DB
    // Risicoklassen
    Route::post('updaterisicoklasse', [HistorischebronnenimportController::class, 'updaterisicoklasse']);
    Route::post('updaterisicoklasse/{saneerder}', [HistorischebronnenimportController::class, 'updaterisicoklasse']);
    Route::post('addrisicoklasse', [HistorischebronnenimportController::class, 'addrisicoklasse']);
    Route::post('addrisicoklasse/{saneerder}', [HistorischebronnenimportController::class, 'addrisicoklasse']);

    // Locatie
    Route::post('updatelocatie', [HistorischebronnenimportController::class, 'updatelocatie']);
    Route::post('updatelocatie/{saneerder}', [HistorischebronnenimportController::class, 'updatelocatie']);
    Route::post('addlocatie', [HistorischebronnenimportController::class, 'addlocatie']);
    Route::post('addlocatie/{saneerder}', [HistorischebronnenimportController::class, 'addlocatie']);

    // Omschrijving
    Route::post('updateomschrijving', [HistorischebronnenimportController::class, 'updateomschrijving']);
    Route::post('updateomschrijving/{saneerder}', [HistorischebronnenimportController::class, 'updateomschrijving']);
    Route::post('addomschrijving', [HistorischebronnenimportController::class, 'addomschrijving']);
    Route::post('addomschrijving/{saneerder}', [HistorischebronnenimportController::class, 'addomschrijving']);

    // Stap 3: Aangeven als bronnen wel/niet gesaneerd zijn
    //Gesaneerd
    Route::post('get100adressen', [HistorischebronnenimportController::class, 'get100adressen']);
    Route::post('setgesaneerd', [HistorischebronnenimportController::class, 'setgesaneerd']);
    Route::post('setgeinventariseerd', [HistorischebronnenimportController::class, 'setgeinventariseerd']);

    // Stap 4: Toewijzen projectnummers
    Route::post('insertproject', [HistorischebronnenimportController::class, 'insertproject']);


});
