# Notes

### Additional configuration directives
`max_input_vars = 50000`
### Supervision Subdomain
`supervision.asbestregisseur.nl`

# Examples

### Stappen Velden

#### Type `Select` options
```json
{
  "options": [

    {
      "value": "ja",
      "name": "ja"
    },
    {
      "value": "nee",
      "name": "nee"
    },
    {
      "value": "beslissing beheerder",
      "name": "beslissing beheerder",
      "disabled": true
    }
  ]
}
```

#### Type `Select` with database records as options
```json
{
  "database": {
    "table": "projecten_bronomschrijvingen",
    "value": "id",
    "name": "omschrijving",
    "where": {
      "bronnen": 1,
      "active": 1
    }
  }
}
```

### Stappen Velden Data

`GLOBAL`
```json
{
  "prefill": "project.bewoond",
  "visible": {
    "parent": "opdrachtformulier.woning_in_gebruik",
    "equals": "nee"
  }
}
```

`select`
```json
//Fixed values
{
  "options": [
    {"value": 1, "name": "Ja"},
    {"value": 0, "name": "Nee"}
  ]
}

//Values from the database
{
  "database": {
    "table": "projecten_bronomschrijvingen",
    "value": "id",
    "name": "omschrijving",
    "where": {
      "bronnen": 1,
      "active": 1
    }
  }
}
```

`files`
```json
{
  "multiple": true
}
```

`asbest_bronnen`
```json
{
  "edit": true,
  "deelsaneren": true
}
```

`historische_bronnen_toevoegen`
```json
{
  "valid_report": true,
  "validity_in_years": 3
}
```

### Stappen Velden Settings

#### Possible input types: `checkbox`

#### `FIELD_OPMERKING` Extra textarea for opmerkingen
```json
{}
```

#### `STEP_REJECT` Option to reject a step
```json
{
 "back_to_step_id": 3
}
```

#### `SKIP_STEPS` Option to skip to a specific step based on conditions
```json
{
  "skip_to": 14,
  "conditions": [
    {
      "attribute": "asbest_bronnen.*.asbesthoudend",
      "operator": "==",
      "value": "0"
    }
  ]
}

//Possible operators: "==",
```

### Statistieken

#### StatistiekenComponent
```injectablephp
class Asbest extends statistiekenBaseComponent{

    //Abstract implementations
    function initCharts(){}
    function defineProcessen(){}
    function defineFilters(){}

}
```

#### Add statistieken
```injectablephp
class Asbest extends statistiekenBaseComponent{

        public function defineAsbestCharts(){
            $group_key = $this->addGroup(['name' => 'Inventarisatie']);
    
            $this->addChart($group_key, [
                'name' => 'Woningbezit geïnventariseerd',
                'type' => 'doughnut',
                'legend' => true, // Default: false
                'legend_position' => 'right', //Default top
            ]);
        }

}
```
