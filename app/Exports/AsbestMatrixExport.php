<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class AsbestMatrixExport implements FromView{

    public $complex;
    public $data;

    public function __construct($complex, $data) {
        $this->complex = $complex;
        $this->data = $data;
    }
    public function view(): View{
        return view('exports.excel.complexen.asbest-bronnen-matrix', array_merge([
            'complex' => $this->complex,
        ], $this->data));
    }
}
