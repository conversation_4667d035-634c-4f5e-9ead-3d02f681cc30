<?php

namespace App\Exports;

use App\Models\Complexen;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;

class AsbestKostenExport implements fromView{

    public $data;

    public function __construct($data){
        $data['complexen'] = Complexen::getAll();
        $data['uitvoerkosten'] = Complexen::asbestUitvoerkosten($data['settings'] ?? []);

        $this->data = $data;
    }

    public function view(): View{

        return view('exports.excel.complexen.asbest-kosten', $this->data);
    }
}
