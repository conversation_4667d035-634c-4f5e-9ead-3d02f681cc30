<?php

namespace App\Providers;

use App\Http\Middleware\DomainSetup;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Route;
use Livewire\Livewire;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind('path.public', function() {
            return base_path('public');
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot(){

        Paginator::useBootstrap();

        //Livewire
        Livewire::addPersistentMiddleware([ DomainSetup::class, ]);

        //Blade icons
        Blade::directive('chart_loader', function() {return '<div class="chart-loader loader" ></div>';});

        Blade::directive('dots_loader', function() {return '<div data-loader class="dots-loader" > <div></div><div></div><div></div> </div>';});
        Blade::directive('page_loader', function() {return '<div data-page-loader class="py-5"><div class="text-center"><div class="dots-loader"><div></div><div></div><div></div><div></div></div></div></div>';});
        
        Blade::directive('spinner', function() {return '<div data-loader class="spinner-border text-primary" role="status"><span class="sr-only"></span></div>';});
        Blade::directive('spinner_success', function() {return '<div data-loader class="spinner-border text-success" role="status"><span class="sr-only"></span></div>';});
        Blade::directive('spinner_danger', function() {return '<div data-loader class="spinner-border text-danger" role="status"><span class="sr-only"></span></div>';});
        Blade::directive('spinner_large', function() {return '<div data-loader class="spinner-border text-primary" role="status" style="width: 50px;height: 50px;"><span class="sr-only"></span></div>';});
        Blade::directive('spinner_large', function() {return '<div data-loader class="spinner-border text-primary" role="status" style="width: 50px;height: 50px;"><span class="sr-only"></span></div>';});
        Blade::directive('spinner_small', function() {return '<div data-loader class="spinner-border text-primary" role="status" style="width: 20px;height: 20px;font-size: .75rem;"><span class="sr-only"></span></div>';});

        Blade::directive('icon_menu', function() {return '<i class="fa-solid fa-bars m-0"></i>';});
        Blade::directive('icon_plus', function() {return '<i class="fas fa-plus m-0"></i>';});
        Blade::directive('icon_minus', function() {return '<i class="fas fa-minus m-0"></i>';});
        Blade::directive('icon_trash', function() {return '<i class="far fa-trash-alt m-0"></i>';});
        Blade::directive('icon_edit', function() {return '<i class="far fa-edit m-0"></i>';});
        Blade::directive('icon_show', function() {return '<i class="fa-solid fa-eye m-0"></i>';});
        Blade::directive('icon_hide', function() {return '<i class="fa-solid fa-eye-slash m-0"></i>';});
        Blade::directive('icon_confirm', function() {return '<i class="fas fa-check m-0"></i>';});
        Blade::directive('icon_redo', function() {return '<i class="fas fa-redo-alt m-0"></i>';});
        Blade::directive('icon_toggle_vertical', function() {return '<i class="fas fa-arrows-alt-v m-0"></i>';});
        Blade::directive('icon_question', function() {return '<i class="far fa-question-circle m-0"></i>';});
        Blade::directive('icon_up', function() {return '<i class="fa fa-chevron-up transition-025 m-0"></i>';});
        Blade::directive('icon_down', function() {return '<i class="fa fa-chevron-down transition-025 m-0"></i>';});
        Blade::directive('icon_left', function() {return '<i class="fa fa-chevron-left transition-025 m-0"></i>';});
        Blade::directive('icon_right', function() {return '<i class="fa fa-chevron-right transition-025 m-0"></i>';});
        Blade::directive('icon_arrow_up', function() {return '<i class="fa fa-arrow-up transition-025 m-0"></i>';});
        Blade::directive('icon_arrow_down', function() {return '<i class="fa fa-arrow-down transition-025 m-0"></i>';});
        Blade::directive('icon_arrow_left', function() {return '<i class="fa fa-arrow-left transition-025 m-0"></i>';});
        Blade::directive('icon_arrow_right', function() {return '<i class="fa fa-arrow-right transition-025 m-0"></i>';});
        Blade::directive('icon_arrows_split_up_and_left', function() {return '<i class="fa fa-arrows-split-up-and-left transition-025 m-0"></i>';});
        Blade::directive('icon_arrows_split_up_and_right', function() {return '<i class="fa fa-arrows-split-up-and-left transition-025 m-0" style="transform: rotateY(180deg)" ></i>';});
        Blade::directive('icon_arrows_split_right_and_up', function() {return '<i class="fa fa-arrows-split-up-and-left transition-025 m-0" style="transform: rotate(90deg)" ></i>';});
        Blade::directive('icon_arrows_split_right_and_down', function() {return '<i class="fa fa-arrows-split-up-and-left transition-025 m-0" style="transform: rotate(90deg) rotateY(180deg)" ></i>';});
        Blade::directive('icon_arrows_split_down_and_right', function() {return '<i class="fa fa-arrows-split-up-and-left transition-025 m-0" style="transform: rotate(180deg)" ></i>';});
        Blade::directive('icon_arrows_split_right_and_left', function() {return '<i class="fa fa-arrows-split-up-and-left transition-025 m-0" style="transform: rotate(180deg) rotateY(180deg)" ></i>';});
        Blade::directive('icon_arrows_split_left_and_down', function() {return '<i class="fa fa-arrows-split-up-and-left transition-025 m-0" style="transform: rotate(270deg)" ></i>';});
        Blade::directive('icon_arrows_split_left_and_up', function() {return '<i class="fa fa-arrows-split-up-and-left transition-025 m-0" style="transform: rotate(270deg) rotateY(180deg)" ></i>';});
        Blade::directive('icon_arrows_left_right', function() {return '<i class="fa fa-arrow-right-arrow-left m-0" ></i>';});
        Blade::directive('icon_arrows_bottom_left_top_right', function() {return '<i class="fa fa-arrow-right-arrow-left m-0" style="transform: rotate(135deg)" ></i>';});
        Blade::directive('icon_file', function() {return '<i class="fas fa-folder-open m-0"></i>';});
        Blade::directive('icon_file_lines', function() {return '<i class="fa-solid fa-file-lines m-0"></i>';});
        Blade::directive('icon_download', function() {return '<i class="fas fa-download m-0"></i>';});
        Blade::directive('icon_cloud_download', function() {return '<i class="fa-solid fa-cloud-arrow-down m-0"></i>';});
        Blade::directive('icon_cloud_upload', function() {return '<i class="fa-solid fa-cloud-arrow-up m-0"></i>';});
        Blade::directive('icon_cloud', function() {return '<i class="fa fa-cloud m-0"></i>';});
        Blade::directive('icon_upload', function() {return '<i class="fa-solid fa-arrow-up-from-bracket m-0"></i>';});
        Blade::directive('icon_external_link', function() {return '<i class="fa-solid fa-arrow-up-right-from-square m-0"></i>';});
        Blade::directive('icon_close', function() {return '<i class="fa fa-xmark m-0"></i>';});
        Blade::directive('icon_zoom', function() {return '<i class="fa fa-magnifying-glass m-0"></i>';});
        Blade::directive('icon_list', function() {return '<i class="fa fa-list m-0"></i>';});
        Blade::directive('icon_bars', function() {return '<i class="fa fa-bars m-0"></i>';});
        Blade::directive('icon_check', function() {return '<i class="fa fa-check m-0"></i>';});
        Blade::directive('icon_circle_check', function() {return '<i class="fa fa-circle-check m-0"></i>';});
        Blade::directive('icon_circle_check_regular', function() {return '<i class="fa-regular fa-circle-check m-0"></i>';});
        Blade::directive('icon_expand', function() {return '<i class="bi bi-arrows-angle-expand m-0"></i>';});
        Blade::directive('icon_collapse', function() {return '<i class="bi bi-arrows-angle-contract m-0"></i>';});
        Blade::directive('icon_map_dot', function() {return '<i class="fa fa-map-location-dot m-0"></i>';});
        Blade::directive('icon_wrench', function() {return '<i class="fa fa-wrench m-0"></i>';});
        Blade::directive('icon_save', function() {return '<i class="fa fa-save m-0"></i>';});
        Blade::directive('icon_copy', function() {return '<i class="fa fa-copy m-0"></i>';});
        Blade::directive('icon_font', function() {return '<i class="fa-solid fa-font m-0"></i>';});
        Blade::directive('icon_graph', function() {return '<i class="fa fa-chart-column m-0"></i>';});
        Blade::directive('icon_img', function() {return '<i class="fa fa-image m-0"></i>';});
        Blade::directive('icon_color_fill', function() {return '<i class="fa fa-fill-drip m-0"></i>';});
        Blade::directive('icon_text', function() {return '<i class="fa fa-align-left m-0"></i>';});
        Blade::directive('icon_page_break', function() {return '<i class="fa fa-arrow-down-wide-short m-0"></i>';});
        Blade::directive('icon_shuffle', function() {return '<i class="fa fa-shuffle m-0"></i>';});
        Blade::directive('icon_random', function() {return '<i class="fa fa-dice m-0"></i>';});
        Blade::directive('icon_pencil', function() {return '<i class="fa fa-pen m-0"></i>';});
        Blade::directive('icon_comment', function() {return '<i class="fa fa-comment m-0"></i>';});
        Blade::directive('icon_comment_dots', function() {return '<i class="fa fa-comment-dots m-0"></i>';});
        Blade::directive('icon_signature', function() {return '<i class="fa fa-signature"></i>';});
        Blade::directive('icon_off', function() {return '<i class="fa-solid fa-power-off m-0"></i>';});
        Blade::directive('icon_calculator', function() {return '<i class="fa-solid fa-calculator m-0"></i>';});
        Blade::directive('icon_share', function() {return '<i class="fa-solid fa-share m-0"></i>';});
        Blade::directive('icon_grip_dots', function() {return '<i class="fa-solid fa-grip m-0"></i>';});
        Blade::directive('icon_exclamation', function() {return '<i class="fa-solid fa-exclamation m-0"></i>';});
        Blade::directive('icon_users', function() {return '<i class="fa-solid fa-users m-0"></i>';});
        Blade::directive('icon_house', function() {return '<i class="fa-solid fa-house-chimney m-0"></i>';});
        Blade::directive('icon_tasks', function() {return '<i class="fa-solid fa-list-check m-0"></i>';});
        Blade::directive('icon_warehouse', function() {return '<i class="fa-solid fa-warehouse m-0"></i>';});
        Blade::directive('icon_asc', function() {return '<i class="fa-solid fa-arrow-down-short-wide m-0"></i>';});
        Blade::directive('icon_desc', function() {return '<i class="fa-solid fa-arrow-down-wide-short m-0"></i>';});
        Blade::directive('icon_qr', function() {return '<i class="fa-solid fa-qrcode m-0"></i>';});
        Blade::directive('icon_play', function() {return '<i class="fa-solid fa-play m-0"></i>';});
        Blade::directive('icon_gear', function() {return '<i class="fa-solid fa-gear m-0"></i>';});
        Blade::directive('icon_clock_rotate_left', function() {return '<i class="fa-solid fa-clock-rotate-left m-0"></i>';});
        Blade::directive('icon_hourglass_half', function() {return '<i class="fa-solid fa-hourglass-half"></i>';});
    }
}
