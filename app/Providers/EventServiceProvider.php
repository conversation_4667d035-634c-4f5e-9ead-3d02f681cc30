<?php

namespace App\Providers;

use App\Listeners\LogAccountLockout;
use App\Listeners\LogFailedLogin;
use App\Listeners\LogLogout;
use App\Listeners\LogSuccesfullLogin;
use App\Listeners\SetSessionIP;
use Illuminate\Auth\Events\Failed;
use Illuminate\Auth\Events\Lockout;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Logout;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;
use SocialiteProviders\Manager\SocialiteWasCalled;
use SocialiteProviders\Microsoft\MicrosoftExtendSocialite;
use Illuminate\Auth\Events\Authenticated;

use Log;
use DB;
use Throwable;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        Authenticated::class => [
        ],
        Login::class => [
            LogSuccesfullLogin::class,
            SetSessionIP::class,
        ],
        Logout::class => [
            LogLogout::class,
        ],
        Failed::class => [
            LogFailedLogin::class,
        ],
        Lockout::class => [
            LogAccountLockout::class,
        ]
    ];
    private $blacklisted_log_tables = [
        'logs',
        'errors',
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot(){
        app('events')->listen(SocialiteWasCalled::class, MicrosoftExtendSocialite::class.'@handle');

        $this->logDataChange();
    }

    private function logDataChange(){
        DB::listen(function ($query) {
            try{
                if($this->isTableLogBlacklisted($query)){ return; }
                if (str_contains($query->sql, 'logs')) { return; }
                if (str_starts_with(strtolower($query->sql), 'select')) { return; }

                $domain = _cachedDomainName() ?? 'Onbekend';

                if (str_starts_with(strtolower($query->sql), 'insert')) {
                    preg_match('/insert into `?(\w+)`? \((.+?)\) values/i', $query->sql, $matches);
                    if (count($matches) >= 3) {
                        $table = $matches[1];
                        $instance_name = ucfirst(snakeToCamelCase($table));
                        $columns = array_map('trim', explode(',', str_replace('`', '', $matches[2])));
                        $values = $query->bindings;
                        $data = array_combine($columns, $values);

                        Log::channel('daily')->debug("({$domain}) Instance {$instance_name} aangemaakt.", [
                            'data' => [
                                'table' => $table,
                                'insert' => $data,
                            ],
                        ]);
                    }
                    return;
                }
                if (str_starts_with(strtolower($query->sql), 'update')) {
                    preg_match('/update `?(\w+)`? set (.+?) where (.+)/i', $query->sql, $matches);
                    if (count($matches) >= 4) {
                        $table = $matches[1];
                        $instance_name = ucfirst(snakeToCamelCase($table));
                        $setClause = $matches[2];
                        $whereClause = $matches[3];
                        $setParts = explode(',', $setClause);
                        $data = [];

                        foreach ($setParts as $part) {
                            list($column, $value) = array_map('trim', explode('=', $part));
                            $data[str_replace('`', '', $column)] = $query->bindings[array_search($value, $query->bindings)];
                        }

                        // Extract WHERE conditions
                        $where = [];
                        preg_match_all('/`?(\w+)`?\s*=\s*\?/', $whereClause, $whereMatches, PREG_SET_ORDER);
                        foreach ($whereMatches as $index => $condition) {
                            $column = $condition[1];
                            $where[$column] = $query->bindings[$index + count($setParts)]; // Adjust index for where bindings
                        }

                        Log::channel('daily')->debug("({$domain}) Instance {$instance_name} gewijzigd.", [
                            'data' => [
                                'table' => $table,
                                'update' => $data,
                                'where' => $where,
                            ],
                        ]);
                    }
                    return;
                }
                if (str_starts_with(strtolower($query->sql), 'delete')) {
                    preg_match('/delete from `?(\w+)`? where (.+)/i', $query->sql, $matches);
                    if (count($matches) >= 3) {
                        $table = $matches[1];
                        $instance_name = ucfirst(snakeToCamelCase($table));
                        $whereClause = $matches[2];

                        $where = [];
                        preg_match_all('/`?(\w+)`?\s*=\s*\?/', $whereClause, $whereMatches, PREG_SET_ORDER);

                        foreach ($whereMatches as $index => $condition) {
                            $column = $condition[1];
                            $where[$column] = $query->bindings[$index];
                        }

                        Log::channel('daily')->debug("({$domain}) Instance {$instance_name} verwijderd.", [
                            'data' => [
                                'table' => $table,
                                'where' => $where
                            ],
                        ]);
                    }
                }
            }
            catch(Throwable $e) {}
        });
    }
    private function isTableLogBlacklisted($query){
        foreach ($this->blacklisted_log_tables as $table) {
            if (str_contains($query->sql, $table)) {
                return true;
            }
        }

        return false;
    }


}
