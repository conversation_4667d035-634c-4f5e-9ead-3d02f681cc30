<?php

namespace App\Listeners;

use App\Classes\IPLocation;
use App\Http\Middleware\DomainSetup;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Log;
use Auth;

class LogSuccesfullLogin
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event){
        $middleware = new DomainSetup();
        $middleware->setDomain();


        $location = new IPLocation(getIP());
        $country = ($location->country_code ?? '') !== 'NL'
            ? " vanuit {$location->country}"
            : '';

        Log::notice("Gebruiker succesvol ingelogd{$country}.", [
            'user_id' => $event->user->id
        ]);

    }
}
