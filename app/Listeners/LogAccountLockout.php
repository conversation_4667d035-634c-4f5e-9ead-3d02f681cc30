<?php

namespace App\Listeners;

use App\Classes\IPLocation;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Log;

class LogAccountLockout{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(){
        //
    }

    /**
     * Handle the event.
     *
     * @param object $event
     * @return void
     */
    public function handle($event){
        $username = $event->request->input('username');
        $user = User::where('username', $username)->first();

        $location = new IPLocation(getIP());
        $country = ($location->country_code ?? '') !== 'NL'
            ? " vanuit {$location->country}"
            : '';

        Log::alert("Account ({$username}) tij<PERSON>ijk vergrendeld, Te veel mislukte inlogpogingen{$country}.", [
            'user_id' => optional($user)->id,
        ]);

    }
}
