<?php

namespace App\Listeners;

use App\Http\Middleware\DomainSetup;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Log;

class LogLogout
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event){
        $middleware = new DomainSetup();
        $middleware->setDomain();

        Log::info('Gebruiker uitgelogd.', ['user_id' => $event->user->id]).'.';
    }
}
