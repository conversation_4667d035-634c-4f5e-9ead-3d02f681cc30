<?php

namespace App\Listeners;

use App\Classes\IPLocation;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Log;

class LogFailedLogin{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(){
        //
    }

    /**
     * Handle the event.
     *
     * @param object $event
     * @return void
     */
    public function handle($event){
        $location = new IPLocation(getIP());
        $country = ($location->country_code ?? '') !== 'NL'
            ? " vanuit {$location->country}"
            : '';

        Log::warning("Mislukte inlogpoging{$country}.", [
            'user_id' => optional($event->user)->id
        ]);
    }
}
