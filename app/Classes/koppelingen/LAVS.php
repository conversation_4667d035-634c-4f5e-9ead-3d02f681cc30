<?php

namespace App\Classes\koppelingen;

use App\Mail\LAVSBronnen;
use App\Models\Files;
use App\Models\Projecten;
use App\Models\ProjectenAsbestSanering;
use App\Models\ProjectenFiles;
use App\Models\Roles;
use App\Models\User;

use Carbon\Carbon;
use Mail;

class LAVS{

    CONST BRONNEN_MAIL_DAYS_DIFF = 5;

    private Projecten $project;
    private ProjectenAsbestSanering $asbest_sanering;

    private $lavs_project_id;
    private $entity_id;
    private $sub_entity_id;

    private $xml = null;

    public function __construct($project_id, $entity_id, $sub_entity_id){
        $this->entity_id = $entity_id;
        $this->sub_entity_id = $sub_entity_id;

        $this->defineInstances($project_id);
        $this->getProject();
        $this->handleFiles();
    }

    //Definitions
    private function defineInstances($project_id){
        $this->project = Projecten::find($project_id);
        $this->asbest_sanering = $this->project->asbest_sanering;

        $this->lavs_project_id = $this->asbest_sanering->lavs_project_id;
    }

    //Curl
    private function post($endpoint, $data = [], $headers = []){
        $curl = curl_init();

        curl_setopt($curl, CURLOPT_URL, $endpoint);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array_merge([
            'Content-Type: multipart/form-data',
        ], $headers));
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_POST, true);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);

        $raw_response = curl_exec($curl);
        $code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $response = json_decode($raw_response);
        $error = curl_error($curl);
        curl_close($curl);

        return [
          'code' => $code,
          'response' => $response,
          'error' => $error,
          'raw_response' => $raw_response,
        ];

    }

    //Project
    private function getProject(){
        $response = $this->post('https://www.asbestregisseur.nl/dashboard/json/lavs_api.php', [
            'method' => 'get',
            'id' => $this->lavs_project_id,
            'entity' => $this->entity_id,
            'sub_entity' => $this->sub_entity_id,
        ]);

        $this->xml = $response['response']->xml;
    }

    //Stappen
    public function handleAannemenSanering(){
        $verwijderzaken = $this->xml->verwijderzaken ?? [];
        if(!is_array($verwijderzaken)){ $verwijderzaken = [ $verwijderzaken ]; }

        //Fetch data from the xml
        foreach ($verwijderzaken as $verwijderzaak){

            if(isset($verwijderzaak->meldingen->start)){
                $startsanering = $verwijderzaak->meldingen->start;
            }
            if(isset($verwijderzaak->meldingen->eind)){
                $eindsanering = $verwijderzaak->meldingen->eind;
            }
            if(isset($verwijderzaak->eindmetingZaken->startdatum)){
                $eindcontrole = $verwijderzaak->eindmetingZaken->startdatum;
            }
            if(isset($verwijderzaak->eindmetingZaken->opdrachtnemer->id)){
                $eindcontrolebureau_lavs_id = $verwijderzaak->eindmetingZaken->opdrachtnemer->id;
            }

        }

        if(!isset($eindcontrole) || !isset($startsanering) || !isset($eindsanering) || !isset($eindcontrolebureau_lavs_id)){ return; }


        //Set Project Users
        $eindcontrolebureau = User::byLavsId($eindcontrolebureau_lavs_id);
        $role = Roles::byKey('eindcontrolebureau');
        $this->project->setGebruiker( optional($role)->id, optional($eindcontrolebureau)->id );

        //Set AsbestSanering data
        ProjectenAsbestSanering::where('id', $this->asbest_sanering->id)->update([
            'verwachte_start_sanering' => $startsanering,
            'verwachte_eind_sanering' => $eindsanering,
            'verwachte_eindcontrole' => $eindcontrole,
            'start_sanering' => $startsanering,
            'eind_sanering' => $eindsanering,
            'eindcontrole' => $eindcontrole,
        ]);

        $this->project->completeStap(medium: 'LAVS');

        //Send Bronnen mail if possible
        $this->handleSaneringOpleveren();
    }
    public function handleSaneringOpleveren(){
        if(!$this->project->assertStap('sanering_opleveren') || !$this->project->getFile('vrijgavedocument') || !$this->project->getFile('begeleidingsbrief')){ return; }

        $saneerder = $this->project->getGebruiker('saneerder');

        if(!$this->asbest_sanering->lavs_bronnen_mail_sent_at || Carbon::now()->diffInDays( Carbon::parse($this->asbest_sanering->lavs_bronnen_mail_sent_at) ) > self::BRONNEN_MAIL_DAYS_DIFF){

            $this->asbest_sanering->lavs_bronnen_mail_sent_at = Carbon::now();
            $this->asbest_sanering->save();

            Mail::to($saneerder->email)->send( new LAVSBronnen($this->project) );
        }
    }
    public function handleStortbon(){
        if(!$this->project->getFile('stortbon')){ return; }

        $this->project->completeStap(medium: 'LAVS');
    }

    //Files
    private function handleFiles(){
        $verwijderzaken = $this->xml->verwijderzaken ?? [];
        if(!is_array($verwijderzaken)){ $verwijderzaken = [ $verwijderzaken ]; }

        //Fetch files from xml
        foreach ($verwijderzaken as $verwijderzaak){

            //Begeleidingsbrief && stortbon
            if(isset($verwijderzaak->bijlages)){
                $bijlages = $verwijderzaak->bijlages;
                if(!is_array($bijlages)){ $bijlages = [ $bijlages ]; }

                foreach ($bijlages as $bijlage){
                    switch($bijlage->documentTypeNaam){
                        case 'GELEIDEBILJET': $this->appendFile('begeleidingsbrief', $bijlage->id); break;
                        case 'BEWIJS_VAN_AFVOER/GELEIDEBILJET':
                        case 'BEWIJS_VAN_AFVOER': $this->appendFile('stortbon', $bijlage->id); break;
                    }
                }
            }

            //Vrijgavedocument
            if (isset($verwijderzaak->eindmetingZaken->bijlages)){
                $bijlages = $verwijderzaak->eindmetingZaken->bijlages;
                if(!is_array($bijlages)){ $bijlages = [ $bijlages ]; }

                foreach ($bijlages as $bijlage){
                    switch($bijlage->documentTypeNaam){
                        case 'VRIJGAVECERTIFICAAT': $this->appendFile('vrijgavedocument', $bijlage->id); break;
                    }
                }
            }

        }
    }
    private function appendFile($key, $id){
        $exists = ProjectenFiles::where([
            'project_id' => $this->project->id,
            'lavs_id' => $id
        ])->exists();
        if($exists){ return; }

        $file = Files::downloadAndStoreFile("https://www.asbestregisseur.nl/dashboard/files/lavs/{$this->lavs_project_id}/{$id}", [
            'path' => "/{$key}"
        ]);

        ProjectenFiles::insert([
            'project_id' => $this->project->id,
            'file_id' => $file->id,
            'lavs_id' => $id,
            'file_key' => $key,
        ]);
    }

}
