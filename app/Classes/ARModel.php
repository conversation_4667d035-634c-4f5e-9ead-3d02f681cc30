<?php

namespace App\Classes;
use Illuminate\Database\Eloquent\Model;

class ARModel extends Model{

    public $cache = [];

    const ORDERABLY_BY = [ 'created_at' ];

    //Cache
    protected function setCache($key, $value){
        $this->cache[$key] = $value;
        return $value;
    }
    protected function getCache($key, $default = null){
        return $this->cache[$key] ?? $default;
    }
    public function confirmCache(){
        foreach($this->cache as $attr => $value){
            $this->setAttribute($attr, $value);
        }
    }

    //Static functions
    public static function sanitizeOrderBy($order_by){
        return in_array($order_by, static::ORDERABLY_BY) ? $order_by : 'created_at';
    }

}
