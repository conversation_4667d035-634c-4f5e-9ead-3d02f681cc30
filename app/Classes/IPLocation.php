<?php

namespace App\Classes;

class IPLocation{

    public $ip;

    public $hostname = null;
    public $country_code = null;
    public $country = null;
    public $region = null;

    public function __construct($ip){
        $this->ip = $ip;

        $this->fetchLocation();
    }

    private function fetchLocation(){
        if(!$this->ip){ return; }

        try{
            $location =  json_decode(file_get_contents("https://ipinfo.io/{$this->ip}/json"));

            $this->hostname = $location->hostname ?? null;
            $this->region = $location->region ?? null;
            $this->country_code = $location->country ?? null;
            $this->country = \Locale::getDisplayRegion('-' . $this->country_code, 'nl');
        }
        catch(\Throwable $e){ }
    }


}
