<?php

namespace App\Classes\search;

class SearchIndexes{

    protected array $users_columns = [
        'users.username',
        'users.name',
        'users.email',
        'users.telefoon',
    ];
    protected array $adressen_columns = [
        'adressen.vhenummer',
        'adressen.straat',
        'adressen.huisnummer',
        'adressen.toevoeging',
        'adressen.postcode',
        'adressen.plaats',
        'adressen.complexnummer',
        'adressen.subcomplexnummer',
        'adressen.bouwjaar',
        'adressen.wijk',
        'adressen.buurt',
        'adressen.woningtype',
    ];
    protected array $projecten_columns = [
        'projecten.projectnummer',
        'projecten.opdrachtnummer',
    ];
    protected array $bedrijven_columns = [
        'bedrijven.naam',
        'bedrijven.email',
        'bedrijven.telefoon',
    ];
    protected array $corporatie_columns = [
        'domeinen.id',
        'domeinen.domein',
        'domeinen.user',
    ];
    protected array $gebruikers_columns = [
        'users.name',
        'users.username',
        'users.email',
        'users.telefoon',
    ];
    protected array $contacten_columns = [
        'contacten.name',
        'contacten.email',
        'contacten.telefoon',
        'contacten.partijnaam',
    ];
    protected array $rollen_columns = [
        'user_roles.name'
    ];
    protected array $asbest_bronnen_columns = [
        'asbest_bronnen.bronnummer',
        'asbest_bronnen.eenheid',
        'asbest_bronnen.aantal',
        'projecten_bronlocaties.locatie',
        'projecten_bronomschrijvingen.omschrijving',
        'projecten_risicoklassen.klasse',
    ];
    protected array $mails_columns = [
        'emails.subject',
        'emails.content',
    ];
    protected array $stappen_columns = [
      'stappen.stapnaam',
    ];
    protected array $stappen_api_columns = [
        'stappen_api.type',
        'stappen_api.endpoint',
        'stappen_api.koppeling',
        'stappen_api.opmerking',
    ];
    protected array $stappen_api_log_columns = [
        'stappen_api_log.url',
        'stappen_api_log.code',
        'stappen_api_log.data',
        'stappen_api_log.response',
        'stappen_api_log.error',
    ];
    protected array $logs_columns = [
        'logs.route',
        'logs.method',
        'logs.message',
        'logs.level_name',
        'logs.platform',
        'logs.browser',
        'logs.country',
        'logs.region',
        'logs.data',
        'logs.project_id',
    ];

    public function __construct(){

    }

    public function merge(...$columns){
        $merged = [];
        foreach($columns as $column){
            $merged = array_merge($merged, $column);
        }

        return $merged;
    }
    
}
