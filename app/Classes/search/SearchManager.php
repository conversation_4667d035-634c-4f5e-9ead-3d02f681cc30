<?php

namespace App\Classes\search;

use App\Classes\ProjectSearch;
use App\Models\Adressen;
use App\Models\DomeinKoppel;
use DB;

class SearchManager extends SearchIndexes{
    
    public $string;
    public $string_exploded;
    public $ids;
    
    public function __construct($string, $params = []){
        parent::__construct();
        $this->string = $string;
        $this->string_exploded = explode(' ', $string);
        
        $this->ids = $params['ids'] ?? null;
    }
    
    
    public function adres(){
        $where = $this->generateWhere($this->adressen_columns);
        $ids = $this->generateIDSIn('adressen');
        
        if(!$where){
            return [];
        }
        
        return DB::connection('client_db')->select("
            SELECT adressen.id, adressen.straat, adressen.huisnummer, adressen.toevoeging, adressen.plaats, adressen.postcode, adressen.vhenummer, adressen.complexnummer
            FROM adressen
            WHERE $ids ( $where )
            LIMIT 100
        ");
    }
    public function project(){
        $where = $this->generateWhere($this->merge($this->projecten_columns, $this->adressen_columns));
        $ids = $this->generateIDSIn('projecten');
        
        if(!$where){
            return [];
        }
        
        return DB::connection('client_db')->select("
            SELECT projecten.id, projecten.projectnummer, projecten.opdrachtnummer, adressen.straat, adressen.huisnummer, adressen.toevoeging, adressen.id AS adressen
            FROM projecten
            LEFT JOIN adressen ON adres_id = adressen.id
            WHERE $ids ( $where )
            ORDER BY projecten.created_at DESC
            LIMIT 100
        ");
    }
    public function complex(){
        //complexnummer is not a tables and has not id
        //column complexnummer is handled as primary key
        
        $where = $this->generateWhere($this->adressen_columns);
        $ids = $this->generateIDSIn('adressen', 'complexnummer');
        
        if(!$where){
            return [];
        }
        
        return DB::connection('client_db')->select("
            SELECT DISTINCT adressen.complexnummer,
                   adressen.plaats,
                   adressen.complexnummer AS id
            FROM adressen
            WHERE $ids ( $where )
            ORDER BY adressen.complexnummer DESC
            LIMIT 100
        ");
    }
    public function bedrijf(){
        $where = $this->generateWhere($this->bedrijven_columns);
        $ids = $this->generateIDSIn('bedrijven');
        
        if(!$where){
            return [];
        }
        
        return DB::connection('main_db')->select("
            SELECT bedrijven.id, bedrijven.naam, bedrijven.email
            FROM bedrijven
            WHERE $ids ( $where )
            LIMIT 100
        ");
    }
    public function corporatie(){
        $where = $this->generateWhere($this->corporatie_columns);
        $ids = $this->generateIDSIn('domeinen');
        
        if(!$where){
            return [];
        }
        
        return DB::connection('main_db')->select("
            SELECT domeinen.id, domeinen.domein, domeinen.user
            FROM domeinen
            WHERE $ids ( $where )
            LIMIT 100
        ");
    }
    public function gebruiker(){
        $where = $this->generateWhere($this->gebruikers_columns);
        $ids = $this->generateIDSIn('users');
        
        $connected_ids = DomeinKoppel::where(['domain_id' => _domain()->id])->pluck('user_id')->toArray();
        $connected_ids = $this->arrayToList($connected_ids);
        
        if(!$where){
            return [];
        }

        return DB::connection('main_db')->select("
            SELECT users.id, users.name, users.email
            FROM users
            WHERE users.id IN $connected_ids AND $ids ( $where )
            LIMIT 100
        ");
    }
    public function contact(){
        $where = $this->generateWhere($this->contacten_columns);
        $ids = $this->generateIDSIn('contacten');
        
        if(!$where){
            return [];
        }
        
        return DB::connection('client_db')->select("
            SELECT contacten.id, contacten.name, contacten.email
            FROM contacten
            WHERE $ids ( $where )
            LIMIT 100
        ");
    }
    public function rol(){
		    $where = $this->generateWhere($this->rollen_columns);
		    $ids = $this->generateIDSIn('user_roles');
		    
		    if(!$where){
			    return [];
		    }
	    
	    return DB::connection('main_db')->select("
            SELECT user_roles.name
            FROM user_roles
            WHERE $ids ( $where )
            LIMIT 100
        ");
    }
    public function asbestBronnen(){
        $where = $this->generateWhere($this->merge($this->asbest_bronnen_columns, $this->adressen_columns, $this->projecten_columns));
        $ids = $this->generateIDSIn('asbest_bronnen');

        if(!$where){
            return [];
        }

        return DB::connection('client_db')->select("
            SELECT asbest_bronnen.id, asbest_bronnen.bronnummer, projecten_bronlocaties.locatie, projecten_bronomschrijvingen.omschrijving
            FROM asbest_bronnen
            LEFT JOIN adressen ON adres_id = adressen.id
            LEFT JOIN projecten ON project_id = projecten.id
            LEFT JOIN projecten_bronlocaties ON locatie_id = projecten_bronlocaties.id
            LEFT JOIN projecten_bronomschrijvingen ON omschrijving_id = projecten_bronomschrijvingen.id
            LEFT JOIN projecten_risicoklassen ON risicoklasse_id = projecten_risicoklassen.id
            WHERE $ids ( $where )
            LIMIT 100
        ");


    }
    public function mail(){
        $where = $this->generateWhere($this->mails_columns);
        $ids = $this->generateIDSIn('emails');

        if(!$where){
            return [];
        }

        return DB::connection('client_db')->select("
            SELECT emails.id, emails.subject, projecten.projectnummer
            FROM emails
            LEFT JOIN projecten ON project_id = projecten.id
            WHERE $ids ( $where )
            LIMIT 100
        ");
    }
    public function stappenApi(){
        $where = $this->generateWhere($this->merge($this->stappen_api_columns, $this->stappen_columns));
        $ids = $this->generateIDSIn('stappen_api');

        if(!$where){
            return [];
        }

        return DB::connection('main_db')->select("
            SELECT stappen_api.id, stappen_api.koppeling, stappen.stapnaam
            FROM stappen_api
            LEFT JOIN stappen ON stappen_api.stap_id = stappen.id
            WHERE $ids ( $where )
            LIMIT 100
        ");
    }
    public function stappenApiLog(){
        $where = $this->generateWhere($this->merge($this->stappen_api_log_columns, $this->stappen_api_columns, $this->stappen_columns));
        $ids = $this->generateIDSIn('stappen_api_log');

        if(!$where){
            return [];
        }

        return DB::connection('main_db')->select("
            SELECT stappen_api_log.id, stappen_api_log.code, stappen_api_log.created_at, stappen_api.koppeling, stappen_api.type, stappen.stapnaam
            FROM stappen_api_log
            LEFT JOIN stappen_api ON stappen_api_log.api_id = stappen_api.id
            LEFT JOIN stappen ON stappen_api.stap_id = stappen.id
            WHERE $ids ( $where )
            ORDER BY stappen_api_log.created_at DESC
            LIMIT 100
        ");
    }
    public function logs(){
        $where = $this->generateWhere($this->merge($this->logs_columns, $this->users_columns));
        $ids = $this->generateIDSIn('logs');

        if(!$where){
            return [];
        }

        return DB::connection('main_db')->select("
            SELECT logs.id, logs.level_name, logs.message, users.name as user_name
            FROM logs
            LEFT JOIN users ON logs.user_id = users.id
            WHERE $ids ( $where )
            LIMIT 100
        ");
    }

    private function generateIDSIn($prefix, $primary_key = 'id'){
        if(!$this->ids){
            return '';
        }
        
        $list = $this->arrayToList($this->ids);
        
        return "$prefix.$primary_key IN $list AND";
    }
    private function arrayToList($array){
        return "('" . implode("' ,'", $array) . "')";
    }
    private function generateWhere($columns){
        $where = [];
        
        foreach($this->string_exploded as $word){
            if(!$word){
                continue;
            }
            $where[] = "REPLACE(CONCAT_WS('', " . implode(', ', $columns) . "), ' ', '') LIKE '%$word%'";
        }
        
        return implode(' AND ', $where);
    }
    
}
