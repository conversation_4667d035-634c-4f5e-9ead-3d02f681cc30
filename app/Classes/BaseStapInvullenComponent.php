<?php

namespace App\Classes;

class BaseStapInvullenComponent extends ARLivewireComponent{

	public $opmerking;
	
	public $stap;
	public $proces;
	public $project;

    public $planmatig;
	public $settings;
	
	public function baseMount($manager){
		$this->proces = $manager->proces;
		$this->stap = $manager->stap;

		$this->settings = $manager->settings;
        $this->planmatig = !!$this->project->planmatig_key;
	}
	
	public function getStapSettingValue($key, $default = null){
		return $this->settings->where('keyword', $key)->first()->value ?? $default;
	}
	public function getStapSettingData($key){
		$data = $this->settings->where('keyword', $key)->first()->data ?? '{}';
		return json_decode($data);
	}
}
