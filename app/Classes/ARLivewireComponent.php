<?php

namespace App\Classes;

use Livewire\Component;

class ARLivewireComponent extends Component{

    public function setValue($value, $params){
        $model = $params['model'] ?? null;

        if(!$model || !$this->hasRule($model)){ return; }

        data_set($this, $model, $value);
        $this->updated($model, $value);

        $fn = 'updated';
        $segments = explode('.', $model);

        foreach($segments as $i => $segment){
            $fn .= ucfirst($segment);
            if(!method_exists($this, $fn)){ continue; }

            $model_segment = $segment;
            if($i + 1 < count($segments)){
                $model_segment = implode('.', array_slice($segments, ($i + 1), count($segments)));
            }

            $this->$fn($value, $model_segment);
        }

    }
    protected function hasRule($model) {
        $segments = explode('.', $model);

        //Full model rule
        if (array_key_exists($model, $this->rules)) { return true; }

        //Same length rules
        $rules = array_filter($this->rules, function($rule, $rule_model) use ($segments) {
            $rule_segments = explode('.', $rule_model);
            return count($rule_segments) == count($segments);
        }, ARRAY_FILTER_USE_BOTH);
        $temp_rules = [];

        //Check each rule for *
        foreach($segments as $index => $segment){
            foreach($rules as $rule_model => $rule){
                $rule_segments = explode('.', $rule_model);
                if(!isset($rule_segments[$index])){ continue; }

                $rule_segment = $rule_segments[$index];
                if($rule_segment == '*' || $rule_segment == $segment){
                    if($index + 1 == count($segments)){

                        return true;
                    }
                    $temp_rules[$rule_model] = $rule;
                }
            }
            $rules = $temp_rules;
            $temp_rules = [];
        }
        return false;

    }

}
