<?php

namespace App\Classes;

use PriceType;

class Price{

    public $incl;
    public $excl;
    public $btw;

    public function __construct($incl = 0, $excl = 0, $btw = 0){
        $this->incl = floatval($incl);
        $this->excl = floatval($excl);
        $this->btw = floatval($btw);
    }

    public function set($type, $value){
        if($type == PriceType::INCL){
            $this->incl = floatval($value);
        }
        elseif($type == PriceType::EXCL){
            $this->excl = floatval($value);
        }
        elseif($type == PriceType::BTW){
            $this->btw = floatval($value);
        }
    }
    public function get($type, $plain = false){
        $target = 0;

        if($type == PriceType::INCL){
            $target = $this->incl;
        }
        elseif($type == PriceType::EXCL){
            $target = $this->excl;
        }
        elseif($type == PriceType::BTW){
            $target = $this->btw;
        }

        if($plain){ return $target; }

        return number_format($target, 2, ',', '.');
    }

    public function merge(Price $price){
        $this->incl += $price->incl;
        $this->excl += $price->excl;
        $this->btw += $price->btw;
        return $this;
    }
    public function multiply(float $multiplier){
        $this->incl = floatval($this->incl) * $multiplier;
        $this->excl = floatval($this->excl) * $multiplier;
        $this->btw = floatval($this->btw) * $multiplier;
        return $this;
    }


}
