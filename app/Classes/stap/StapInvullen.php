<?php

namespace App\Classes\stap;

use App\Models\InstellingenStappenGebruikers;
use App\Models\InstellingenStappenGebruikersModifiers;
use App\Models\InstellingenStappenMails;
use App\Models\InstellingenStappenNotificaties;
use App\Models\InstellingenStappenSettings;
use App\Models\InstellingenStappenVelden;
use App\Models\Processen;
use App\Models\AsbestBronnen;
use App\Models\Roles;
use App\Models\Stappen;

class StapInvullen{
	public $stap_id;
	public $proces_id;
	public $planmatig;

	public $proces;
	public $stap;
	
	public $query;

    public $gebruikers_modifiers;
	public $gebruikers_toevoegen;
	public $gebruikers_contact;
	
	
	public $inputs;
	public $mails;
	public $notifications;
	public $settings;
	public $gebruikers;
	
	public function __construct($options){
		$this->stap_id = $options['stap'];
		$this->proces_id = $options['proces'];
		$this->planmatig = $options['planmatig'] ?? false;

		$this->proces = Processen::find($this->proces_id);
		$this->stap = Stappen::find($this->stap_id);
		
		
		$query = ['proces_id' => $this->proces_id, 'stap_id' => $this->stap_id];
		$this->query = $query;
		
		$this->inputs = InstellingenStappenVelden::where($query)->where('is_sub', 0)->with('db_input', 'sub_inputs')->where(function($query){
            $query->where('is_planmatig', intval($this->planmatig))->orWhereNull('is_planmatig');
        })->get();

		$this->mails = InstellingenStappenMails::where($query)->where('is_concept', 0)->get();
		$this->notifications = InstellingenStappenNotificaties::where($query)->get();
		$this->settings = InstellingenStappenSettings::where($query)->get();
		$this->gebruikers = InstellingenStappenGebruikers::where($query)->get();
		$this->gebruikers_modifiers = InstellingenStappenGebruikersModifiers::where($query)->get();
		$this->setRolesToSelect();
	}
	
	public function inputsByContext($context = null){
		return $this->inputs->where('db_input.context', $context);
	}
	
	private function setRolesToSelect(){
		$toevoegen_ids = $this->gebruikers->where('toevoegen', 1)->pluck('role_id')->toArray();
		$this->gebruikers_toevoegen = Roles::whereIn('id', $toevoegen_ids)->get();
		
		$contacten_ids = $this->gebruikers->where('contact', 1)->pluck('role_id')->toArray();
		$this->gebruikers_contact = Roles::whereIn('id', $contacten_ids)->get();
	}
	
}
