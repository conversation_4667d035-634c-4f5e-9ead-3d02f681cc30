<?php

namespace App\Classes\stap;

use App\Models\InstellingenStappen;
use App\Models\InstellingenStappenActies;
use App\Models\InstellingenStappenGebruikers;
use App\Models\InstellingenStappenMails;
use App\Models\InstellingenStappenSettings;
use App\Models\InstellingenStappenVelden;
use App\Models\Processen;
use App\Models\Roles;
use App\Models\Stappen;
use App\Models\StappenActies;
use App\Models\StappenSettings;
use App\Models\StappenVelden;
use Carbon\Carbon;

class StapInstellingen{
    public $proces_id;
    public $stap_id;

    public $proces;

    public $stap;
    public $instellingen_stap;
    public $instellingen_stappen;

    public $inputs;
    public $instellingen_inputs;

    public $settings;
    public $instellingen_settings;

    public $gebruikers;
    public $contacten;
    public $available_gebruikers;
    public $instellingen_gebruikers;

    public $acties;
    public $instellingen_acties;

    public $mails;

    public function __construct($options){
        $this->proces_id = $options['proces'];
        $this->stap_id = $options['stap'];

        $query = ['proces_id' => $this->proces_id, 'stap_id' => $this->stap_id];

        //Base
        $this->proces = Processen::where('id', $this->proces_id)->first();
        $this->stap = Stappen::where('id', $this->stap_id)->first();

        //Stappen
        $this->instellingen_stap = InstellingenStappen::where('stap_id', $this->stap_id)->first();
        $this->instellingen_stappen = InstellingenStappen::where('proces_id', $this->proces_id)->with('stap')->whereNull('sub_stap_of')->orderBy('stap_id', 'ASC')->get();

        //Settings
        $this->settings = StappenSettings::where($query)->get();
        $this->instellingen_settings = InstellingenStappenSettings::where($query)->get();

        //Velden
        $this->inputs = StappenVelden::where($query)->whereNull('parent_input')->with('sub_inputs')->get();
        $this->instellingen_inputs = InstellingenStappenVelden::where($query)->get();

        //Gebruikers
        $this->gebruikers = getRoles(1);
        $this->contacten = getRoles(0);
        $this->instellingen_gebruikers = InstellingenStappenGebruikers::where($query)->get();
        $this->setAvailableRoles();

        //Acties
        $this->acties = StappenActies::where($query)->get();
        $this->instellingen_acties = InstellingenStappenActies::where($query)->get();
        $this->setCheckedActies();

        //Mails
        $this->mails = InstellingenStappenMails::where($query)->get();
    }

    //Gebruikers
    private function setAvailableRoles(){
        if($this->stap_id == $this->instellingen_stappen->first()->stap_id){
            $this->available_gebruikers = $this->gebruikers;
            return;
        }

        //Selected roles and roles that can create the projet ( Stap1: Stap invulbaar door )
        $selected_roles = InstellingenStappenGebruikers::where(['proces_id' => $this->proces_id, 'toevoegen' => 1])->where('stap_id', '<', $this->stap_id)->pluck('role_id')->toArray();
        $first_stap_creators = InstellingenStappenGebruikers::where(['stap_id' => $this->instellingen_stappen->first()->stap_id, 'invullen' => 1])->pluck('role_id')->toArray();
        $ids = array_merge($selected_roles, $first_stap_creators);

        $this->available_gebruikers = Roles::whereIn('id', $ids)->get();
    }

    //Acties
    private function setCheckedActies(){
        foreach($this->acties as $i => $actie){
            $setting_actie = $this->instellingen_acties->where('stap_actie_id', $actie->id)->first();
            $this->acties[$i]->checked = isset($setting_actie);
        }
    }

    //Mails
    public function setMail($params){
        //update mail if the record ID is provided
        if($params['id']){
            InstellingenStappenMails::where('id', $params['id'])->update([
                'onderwerp' => $params['onderwerp'],
                'ontvangers' => json_encode($params['ontvangers'] ?? []),
                'mail' => $params['mail'],
            ]);
            return null;
        }

        //Create mail when no ID is provided
        return InstellingenStappenMails::insertGetId([
            'proces_id' => $this->proces_id,
            'stap_id' => $this->stap_id,
            'onderwerp' => $params['onderwerp'],
            'ontvangers' => json_encode($params['ontvangers'] ?? []),
            'mail' => $params['mail'],
        ]);
    }
}
