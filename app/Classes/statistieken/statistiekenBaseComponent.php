<?php

namespace App\Classes\statistieken;
use App\Models\User;
use App\Models\Projecten;
use App\Models\ProjectenStappen;
use Livewire\Component;

abstract class statistiekenBaseComponent extends Component{

    public array $chart_groups = [];
    public array $filters = [
        'start' => null,
        'end' => null,
    ];


    //Abstract
    abstract function initCharts();
    abstract function defineProcessen();
    abstract function defineFilters();

    //Lifecycle
    public function mount(){
        $this->defineFilters();
        $this->defineProcessen();
        $this->initCharts();
    }

    //Targeted lifecycle
    public function updatedFilters(){
        $this->refreshCharts();
    }

    //Charts render
    public function openCharts($key){
        $index = $this->getGroupIndex($key);

        $this->chart_groups[$index]['open'] = !$this->chart_groups[$index]['open'];
        $group = $this->chart_groups[$index];

        //Only trigger the charts when opening the card
        if(!$group['open']){ return; }

        $this->loadCharts($key);
    }
    protected function loadCharts($key){
        $index = $this->getGroupIndex($key);

        foreach($this->chart_groups[$index]['charts'] as $chart){
            $this->loadChart($chart);
        }
    }
    protected function loadChart($chart){
        $fn = $chart['fn'];
        $fn_attrs = $chart['fn_attrs'];

        if(!method_exists($this, $fn)){
            $this->emit('notification', "Chart method ( {$fn}() ) doesn't exist", 'danger');
            return;
        }

        $data = $this->$fn($fn_attrs);

        $this->emit('setChart', array_merge([
            'id' => $chart['key'],
            'title' => $chart['name'],
            'type' => $chart['type'],
            'legend' => $chart['legend'],
            'legend_position' => $chart['legend_position'],
        ], $data));
    }
    protected function refreshCharts(){
        foreach($this->chart_groups as $group){
            if(!$group['open']){ continue; }
            $this->loadCharts($group['key']);
        }
    }

    //Charts CRUD
    public function getGroupIndex($key){
        return find('key', $key, $this->chart_groups, true);
    }
    public function addGroup($attrs){
        $this->chart_groups[] = [
            'name' => $attrs['name'],
            'description' => $attrs['description'] ?? null,
            'key' => spaceToSnakeCase($attrs['name']),
            'open' => false,
            'charts' => [],
        ];

        return spaceToSnakeCase($attrs['name']);
    }
    public function addChart($group_key, $attrs){
        $index = $this->getGroupIndex($group_key);

        $this->chart_groups[$index]['charts'][] = [
            'name' => $attrs['name'],
            'key' => spaceToSnakeCase($attrs['name']),
            'type' => $attrs['type'] ?? 'bar',
            'legend' => $attrs['legend'] ?? false,
            'legend_position' => $attrs['legend_position'] ?? 'top',
            'fn' => $attrs['fn'] ?? spaceToCamelCase($attrs['name']),
            'fn_attrs' => $attrs['fn_attrs'] ?? [],
        ];
    }

    //General Project Charts
    public function aantalProjectenAangemaaktPerJaar($attrs = []){
        $records = Projecten::whereBetween('aangemaakt_op', [$this->filters['start'], $this->filters['end']])
            ->whereIn('proces_id', $this->processen->pluck('id')->toArray())
            ->selectRaw('YEAR(aangemaakt_op) as year, COUNT(*) as count')
            ->groupBy('year')
            ->orderBy('year', 'DESC')
            ->get();

        $labels = $records->map(function($row){ return $row->year; });
        $data = $records->map(function($row){ return $row->count; });

        return [
            'datasets' => [
                [
                    'label' => 'Aantal projecten',
                    'data' => $data,
                ]
            ],
            'labels' => $labels,
        ];
    }
    public function aantalProjectenAangemaaktPerMaand($attrs = []){
        $records = Projecten::whereBetween('aangemaakt_op', [$this->filters['start'], $this->filters['end']])
            ->whereIn('proces_id', $this->processen->pluck('id')->toArray())
            ->selectRaw('YEAR(aangemaakt_op) as year, MONTH(aangemaakt_op) as month, COUNT(*) as count')
            ->groupBy('year', 'month')
            ->orderBy('year', 'DESC')
            ->orderBy('month', 'DESC')
            ->get();

        $labels = $records->map(function($row){
            return getMaanden($row->month)." {$row->year}";
        });
        $data = $records->map(function($row){ return $row->count; });

        return [
            'datasets' => [
                [
                    'label' => 'Aantal projecten',
                    'data' => $data,
                ]
            ],
            'labels' => $labels,
        ];
    }
    public function gemiddeldeProjectduurInDagenPerJaar($attrs = []){
        $records = Projecten::whereBetween('aangemaakt_op', [$this->filters['start'], $this->filters['end']])
            ->where('status', 'AFGEROND')
            ->whereIn('proces_id', $this->processen->pluck('id')->toArray())
            ->whereNotNull('completed_at')
            ->selectRaw('YEAR(aangemaakt_op) as year, AVG(DATEDIFF(completed_at, aangemaakt_op)) as average_duration')
            ->groupBy('year')
            ->orderBy('year', 'DESC')
            ->get();

        $labels = $records->map(function($row){ return $row->year; });
        $data = $records->map(function($row){ return $row->average_duration; });

        return [
            'datasets' => [
                [
                    'label' => 'Gemiddelde aantal dagen',
                    'data' => $data,
                ]
            ],
            'labels' => $labels,
        ];
    }
    public function aantalDagenTussenAanmaakProjectEnVooropnamedatumPlanmatigeProjectenGexcludeerd(){
        $records = Projecten::whereBetween('aangemaakt_op', [$this->filters['start'], $this->filters['end']])
            ->whereIn('proces_id', $this->processen->pluck('id')->toArray())
            ->selectRaw('YEAR(aangemaakt_op) as year,
                 SUM(CASE WHEN DATEDIFF(vooropnamedatum, aangemaakt_op) = 0 THEN 1 ELSE 0 END) as diff_0,
                 SUM(CASE WHEN DATEDIFF(vooropnamedatum, aangemaakt_op) = 1 THEN 1 ELSE 0 END) as diff_1,
                 SUM(CASE WHEN DATEDIFF(vooropnamedatum, aangemaakt_op) = 2 THEN 1 ELSE 0 END) as diff_2,
                 SUM(CASE WHEN DATEDIFF(vooropnamedatum, aangemaakt_op) = 3 THEN 1 ELSE 0 END) as diff_3,
                 SUM(CASE WHEN DATEDIFF(vooropnamedatum, aangemaakt_op) = 4 THEN 1 ELSE 0 END) as diff_4,
                 SUM(CASE WHEN DATEDIFF(vooropnamedatum, aangemaakt_op) = 5 THEN 1 ELSE 0 END) as diff_5,
                 SUM(CASE WHEN DATEDIFF(vooropnamedatum, aangemaakt_op) > 5 THEN 1 ELSE 0 END) as diff_meer_dan_5')
            ->groupBy('year')
            ->orderBy('year', 'DESC')
            ->get();

        $labels = $records->map(function($row){ return $row->year; });

        $datasets = [];
        foreach([0, 1, 2, 3, 4, 5, 'meer_dan_5'] as $index => $d){
            $data = $records->map(function($row) use ($d){ return $row["diff_{$d}"]; });
            $datasets[] = [
                'label' => snakeToSpaceCase($d).' dagen',
                'data' => $data,
                'backgroundColor' => [ '#007000', '#238823', '#2DB12D', '#33C533', '#FFBF00', '#F48718', '#D2222D' ][$index],
            ];
        }

        return [
            'datasets' => $datasets,
            'labels' => $labels,
        ];
    }
    public function gemiddeldeStapduurInDagenPerMaand($attrs){
        $records = ProjectenStappen::where('stap_id', $attrs['stap_id'])
            ->whereHas('project', function($query){
                $query->whereBetween('aangemaakt_op', [$this->filters['start'], $this->filters['end']])
                    ->whereIn('proces_id', $this->processen->pluck('id')->toArray());
            })
            ->whereNotNull('completed_at')
            ->selectRaw("user_id, completed_at, DATE_FORMAT(completed_at, '%Y-%m') as year_month_date, AVG(DATEDIFF(completed_at, created_at)) as average_duration")
            ->groupBy('user_id', 'year_month_date')
            ->orderBy('completed_at', 'DESC')
            ->get();


        $user_records = $records->unique('user_id');
        $date_records = $records->unique('year_month_date');

        $labels = $date_records->map(function($date_record){
            $date = Carbon("{$date_record->year_month_date}-01");
            return getMaanden($date->format('n')).' '.$date->format('Y');
        });

        $datasets = [];
        foreach($user_records as $user_record){
            $user = User::find($user_record->user_id);
            $data = $date_records->map(function($date_record) use ($records, $user_record){
                $duration = $records->where('user_id', $user_record->user_id)->where('year_month_date', $date_record->year_month_date)->first()->average_duration ?? 0;
                return (float)$duration;
            })->toArray();

            $datasets[] = [
                'label' => ($user->bedrijf ? "{$user->bedrijf->naam}, " : '').$user->name,
                'data' => resetIndex($data),
                'backgroundColor' => stringToHexColor($user->name, .75),
            ];
        }

        return [
            'datasets' => $datasets,
            'labels' => resetIndex($labels),
        ];
    }

}
