<?php

namespace App\Classes;

use Http;
use Request;
use Route;

class Proxy{

    public function __construct(){}

    public static function get($origin, $target){
        Route::get($origin, function () use ($target) {
            $get_params = request()->query();
            $query_string = '';
            if(count($get_params)){
                $query_string = '?'.http_build_query($get_params);
            }

            return redirect()->to($target.$query_string);
        });
    }
    public static function post($origin, $target){
        Route::post($origin, function () use ($target) {
            $response = Http::withHeaders(request()->headers->all())->asForm()->post(url($target), request()->all());
            return response(json_decode($response->body(), true), $response->status());
        });
    }

}
