<?php

namespace App\Classes;

class Structure{
    
    public static function paginateModel($model, $page = null, $per_page = null){
        $response  = new \stdClass();
        $response->all_ids = $model->pluck('id')->toArray();
        
        if($page && $per_page){
            $model->skip($per_page * ($page - 1))->take($per_page);
        }
        
        $response->records = $model->get();
        $response->current_ids = $response->records->pluck('id')->toArray();
        
        return $response;
    }
    
}
