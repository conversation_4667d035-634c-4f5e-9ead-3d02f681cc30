<?php

namespace App\Logging;

use App\Classes\IPLocation;
use App\Models\Logs;
use App\Models\User;
use Monolog\Handler\AbstractProcessingHandler;
use Jenssegers\Agent\Agent;


class DatabaseLoggerHandler extends AbstractProcessingHandler {

    public function write(array $record): void {
        try{
            $domain_id = _domain()->id;
            $context = $record['context'];
            $user_id = $context['user_id'] ?? null;
            $stap_id = $context['stap_id'] ?? null;
            $project_id = $context['project_id'] ?? null;
            $data = $context['data'] ?? [];


            $agent = new Agent();
            $location = new IpLocation(
                $record['level'] > 200
                    ? getIP()
                    : null
            );

            //Set user_id based on logged in user if not probided
            if(!$user_id && _user()->id){
                $user_id = _user()->id;
            }

            //Set domain_id based on user if domain is not set
            if(!_domain()->id && $user_id){
                $user = User::find($user_id);
                $domain_id = optional($user)->active_domain;
            }

            Logs::insert([
                'domain_id' => $domain_id,
                'project_id' => $project_id,
                'stap_id' => $stap_id,
                'user_id' => $user_id,
                'route' => request()->path(),
                'method' => request()->method(),
                'message' => $record['message'] ?? null,
                'data' => count($data) ? json_encode($data) : '{}',
                'level' => $record['level'],
                'level_name' => $record['level_name'],
                'user_agent' => $agent->getUserAgent(),
                'platform' => $agent->platform(),
                'browser' => $agent->browser(),
                'ip' => $location->ip,
                'country' => $location->country,
                'region' => $location->region,
                'hostname' => $location->hostname,
            ]);

        }
        catch(\Throwable $e){ dd($e->getMessage(), $e->getLine(), 'handler'); }
    }

}
