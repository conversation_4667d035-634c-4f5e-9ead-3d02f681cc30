<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AsbestBronnenReferenties extends Model{
	use HasFactory;
	
	protected $connection = 'client_db';
	protected $table = 'asbest_bronnen_referenties';
	
	//Relations
	public function adres(){
		return $this->hasOne(Adressen::class, 'id', 'adres_id');
	}
	public function project(){
		return $this->hasOne(Projecten::class, 'id', 'project_id');
	}
    public function bron(){
        return $this->hasOne(AsbestBronnen::class, 'id', 'bron_id');
    }

	//Functions
	public function hasBron($id){
		return (!!$this->bronnen->where('id', $id)->first()) ? 1 : 0;
	}
}
