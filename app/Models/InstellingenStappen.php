<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class InstellingenStappen extends Model
{
    use HasFactory;

    protected $connection = 'client_db';
    protected $table = 'instellingen_stappen';
    
    //relations
    public function stap(){
        return $this->hasOne(Stappen::class, 'id', 'stap_id');
    }
    public function instellingen_stap_gebruikers(){
        return $this->hasMany(InstellingenStappenGebruikers::class, 'stap_id', 'stap_id');
    }
    
    //functions
    public function isInvulbaar(){
        return !!($this->instellingen_stap_gebruikers->where('invullen', 1)->where('role_id', Auth::user()->role->id)->first());
    }

    //Stappen
    public function stapNr(){
        return $this->stap->stapNr();
    }

    //Sub stappen
    public function subStappen(){
        return InstellingenStappen::where('sub_stap_of', $this->stap_id)->orderBy('stap_order')->get();
    }

    
}
