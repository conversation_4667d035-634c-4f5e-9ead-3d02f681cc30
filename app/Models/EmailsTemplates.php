<?php

namespace App\Models;

use Carbon\Carbon;
use EmailsKeywords;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmailsTemplates extends Model{
    use HasFactory;

    protected $table = 'emails_templates';
    protected $fillable = [ 'template_key' ];

    public static function getContent($template_key, $data = []){
        $template = self::where('template_key', $template_key)->first();
        $content = self::setContentData(($template->content ?? ''), $data);

        return $content;
    }
    public static function setContentData($content, $data){

        $project = $data['project'] ?? null;
        $adres = $data['adres'] ?? null;
        $stap_change = $data['stap_change'] ?? null;

        //Project
        $content = str_replace('#'.EmailsKeywords::PROJECT_PROJECTNUMMER.'#', optional($project)->projectnummer, $content);
        $content = str_replace('#'.EmailsKeywords::PROJECT_OPDRACHTNUMMER.'#', optional($project)->opdrachtnummer, $content);
        $content = str_replace('#'.EmailsKeywords::PROJECT_CREATED_AT.'#', ($project ? Carbon::parse($project->created_at)->format('d-m-Y') : null), $content);
        $content = str_replace('#'.EmailsKeywords::PROJECT_SOORT.'#', optional($project)->soortproject, $content);

        //Adres
        $content = str_replace('#'.EmailsKeywords::ADRES_STRAAT.'#', optional($adres)->straat, $content);
        $content = str_replace('#'.EmailsKeywords::ADRES_HUISNUMMER.'#', optional($adres)->huisnummer, $content);
        $content = str_replace('#'.EmailsKeywords::ADRES_TOEVOEGING.'#', optional($adres)->toevoeging, $content);
        $content = str_replace('#'.EmailsKeywords::ADRES_POSTCODE.'#', optional($adres)->postcode, $content);
        $content = str_replace('#'.EmailsKeywords::ADRES_PLAATS.'#', optional($adres)->plaats, $content);
        $content = str_replace('#'.EmailsKeywords::ADRES_VHE.'#', optional($adres)->vhenummer, $content);
        $content = str_replace('#'.EmailsKeywords::ADRES_COMPLEX.'#', optional($adres)->complexnummer, $content);

        //Stap change
        $content = str_replace('#'.EmailsKeywords::STAP_CHANGE_REASON.'#', optional($stap_change)->reason, $content);

        //Regisseur
        $regisseur = optional($project)->getGebruiker('regisseur');
        $content = str_replace('#'.EmailsKeywords::REGISSEUR_NAAM.'#', optional($regisseur)->name, $content);
        $content = str_replace('#'.EmailsKeywords::REGISSEUR_EMAIL.'#', optional($regisseur)->email, $content);
        $content = str_replace('#'.EmailsKeywords::REGISSEUR_TELEFOON.'#', optional($regisseur)->telefoon, $content);
        $content = str_replace('#'.EmailsKeywords::REGISSEUR_LOGO.'#', optional($regisseur)->logoHTML(), $content);

        return $content;
    }

}
