<?php

namespace App\Models;

use App\Classes\Price;
use App\Exports\AsbestKostenExport;
use App\Exports\AsbestMatrixExport;
use ExportType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Maatwebsite\Excel\Facades\Excel;


class Complexen extends Model
{
    use HasFactory;
    
    protected $connection = 'client_db';
    protected $table = 'adressen';


    //Static funcions
    public static function getAll($options = []){
        $model = new Complexen();
        $model = $model->whereNotNull('complexnummer')->groupBy('complexnummer')->orderBy('complexnummer', 'ASC');

        if(isset($options['page']) && isset($options['per_page'])){
            $skip = ($options['page'] - 1) * $options['per_page'];
            $model = $model->skip($skip)->limit($options['per_page']);
        }

        return $model->get();
    }
    
    //Functions
    public function projecten(){
        if(isset($this->_projecten)){ return $this->_projecten; }

        $adressen_ids = $this->adressen()->pluck('id')->toArray();
        $this->_projecten = Projecten::whereIn('adres_id', $adressen_ids)->orderBy('projectnummer', 'DESC')->get();

        return $this->_projecten;
    }
    public function postcodes(){
        if(isset($this->_postcodes)){ return $this->_postcodes; }
        
        $this->_postcodes = Adressen::where(['complexnummer' => $this->complexnummer])->groupBy('postcode')->orderBy('postcode', 'ASC')->get();
        return $this->_postcodes;
    }
    public function adressen(){
        if(isset($this->_adressen)){ return $this->_adressen; }
        
        $this->_adressen = Adressen::where(['complexnummer' => $this->complexnummer])->orderBy('postcode', 'ASC')->get();
        return $this->_adressen;
    }
    public function geinventariseerdeAdressen(){
        if($this->geinventariseerde_adressen){ return $this->geinventariseerde_adressen; }

        $this->geinventariseerde_adressen = $this->adressen()->filter(function($adres){
           return $adres->inventarisatieStatus() == 'WONING_GEINVENTARISEERD';
        });

        return $this->geinventariseerde_adressen;
    }

    //Files
    public function getFiles($proces_type, $key){
        $ids = ComplexenFiles::where(['complexnummer' => $this->complexnummer, 'file_key' => $key, 'proces_type' => $proces_type])->pluck('file_id')->toArray();
        return Files::whereIn('id', $ids)->get();
    }
    public function getInstellingenFiles($proces_type){
        return InstellingenProcessenBestanden::where([
            'proces_type' => $proces_type,
            'context' => 'complex'
        ])->get();
    }


    ////Asbest

    //Bronnen
    public function asbestBronnen($omschrijving_id = null, $locatie_id = null){
        $adressen_ids = $this->adressen()->pluck('id')->toArray();

        $query = AsbestBronnen::whereIn('adres_id', $adressen_ids)->where('active', 1)->orderBy('adres_id', 'ASC')->orderBy('bronnummer', 'ASC');
        if($omschrijving_id){
            $query = $query->where('omschrijving_id', $omschrijving_id);
        }
        if($locatie_id){
            $query = $query->where('locatie_id', $locatie_id);
        }

        return $query->get();
    }
    public function uniekeAsbestBronnen(){
        if(isset($this->_unieke_asbest_bronnen)){ return $this->_unieke_asbest_bronnen; }

        $adressen_ids = $this->adressen()->pluck('id')->toArray();
        $this->_unieke_asbest_bronnen = AsbestBronnen::whereIn('adres_id', $adressen_ids)->groupBy('locatie_id')->groupBy('omschrijving_id')->where('active', 1)->orderBy('adres_id', 'ASC')->orderBy('bronnummer', 'ASC')->get();

        return $this->_unieke_asbest_bronnen;
    }
    public function adresAsbestBronnen($adres_id, $omschrijving_id, $locatie_id){
        return AsbestBronnen::where([
            'adres_id' => $adres_id,
            'omschrijving_id' => $omschrijving_id,
            'locatie_id' => $locatie_id
        ])->get();
    }

    //Indicatie Bronnen
    public function isIndicatieBron($omschrijving_id, $locatie_id, $percentage){
        $adressen = $this->geinventariseerdeAdressen();
        $count = 0;

        foreach($adressen as $adres){
            if($adres->asbestBronnen($omschrijving_id, $locatie_id)->where('zav', '!=', 'ja')->where('asbesthoudend', 1)->count()){ $count++; }
        }

        //Avoid division by zero
        if(!$count && !$adressen->count()){ return false; }

        $bron_percentage = $count / $adressen->count() * 100;
        return $bron_percentage >= $percentage;
    }
    public function aantalIndicatieBronnen($omschrijving_id, $locatie_id, $percentage){
        $count = 0;

        foreach($this->adressen() as $adres){
            if($this->adresAsbestBronnen($adres->id, $omschrijving_id, $locatie_id)->count()){ continue; }
            if($this->adresAsbestReferentieBronnen($adres->id, $omschrijving_id, $locatie_id)->count()){ continue; }
            if($this->isIndicatieBron($omschrijving_id, $locatie_id, $percentage)){ $count++; }
        }

        return $count;
    }

    //Referentie bronnen
    public function adresAsbestReferentieBronnen($adres_id, $omschrijving_id, $locatie_id){
        $referentie_ids = AsbestBronnenReferenties::where('adres_id', $adres_id)->whereHas('bron', function($query) use ($omschrijving_id, $locatie_id){
            $query->where([
                'omschrijving_id' => $omschrijving_id,
                'locatie_id' => $locatie_id
            ]);
        })->pluck('bron_id')->toArray();
        return AsbestBronnen::whereIn('id', $referentie_ids)->get();
    }
    public function aantalReferentieBronnen($omschrijving_id, $locatie_id, $status = null){
        $count = 0;

        foreach($this->adressen() as $adres){
            if($this->adresAsbestBronnen($adres->id, $omschrijving_id, $locatie_id)->count()){ continue; }

            $this->adresAsbestReferentieBronnen($adres->id, $omschrijving_id, $locatie_id)->map(function($bron) use (&$count, $status){
               if(!$status || $status == $bron->referentieStatus()){ $count++; }
            });
        }

        return $count;
    }

    //Kosten
    public function asbestBronnenTotalCost($settings = []){
        if(isset($this->asbest_bronnen_total_cost)){ return $this->asbest_bronnen_total_cost; }

        $cost = new Price();

        $this->adressen()->map(function($adres) use (&$cost, $settings){
            $cost->merge($adres->asbestBronnenTotalCost($settings));
        });

        $this->asbest_bronnen_total_cost = $cost;
        return $this->asbest_bronnen_total_cost;
    }
    public static function asbestUitvoerkosten($cost_settings = [], $query_settings = []){
        $kosten = [];
        $complexen = self::getAll($query_settings);

        foreach($complexen as $i => $complex){
            $uitvoerjaar = $complex->uitvoerjaar ?? 'Onbekend';
            if(!isset($kosten[$uitvoerjaar])){ $kosten[$uitvoerjaar] = new Price(); }

            $kosten[$uitvoerjaar]->merge($complex->asbestBronnenTotalCost($cost_settings));
            unset($complexen[$i]);
        }

        ksort($kosten);
        return $kosten;
    }

    //Export
    public static function exportAsbestKosten($type, $data){
        return match ($type) {
            ExportType::EXCEL => self::exportAsbestKostenExcel($data),
            default => null,
        };
    }
    public static function exportAsbestKostenExcel($data){
        return Excel::download(
            new AsbestKostenExport($data),
            "Complexen_Asbest_Kosten.xlsx",
        );
    }

    ////Export

    //Asbest Matrix
    public function exportAsbestMatrix($type, $data){
        return match ($type) {
            ExportType::EXCEL => $this->exportAsbestMatrixExcel($data),
            default => null,
        };
    }
    public function exportAsbestMatrixExcel($data){
        return Excel::download(
            new AsbestMatrixExport($this, $data),
            "Asbest_Matrix_{$this->complexnummer}.xlsx"
        );
    }


}
