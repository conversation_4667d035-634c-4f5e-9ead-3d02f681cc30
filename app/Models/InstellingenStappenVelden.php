<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InstellingenStappenVelden extends Model{
    use HasFactory;

    protected $connection = 'client_db';
    protected $table = 'instellingen_stappen_velden';
    protected $table_name = 'inforareg_demoa.instellingen_stappen_velden';

    private $labels_blacklist = [
        'referentie_bronnen',
        'asbest_bronnen',
        'planmatige_bewoners',
        'planmatige_asbest_bronnen',
        'asbest_bronnen_vaststellen',
        'historische_bronnen_toevoegen',
        'historische_bronnen_afronden',
        'bewoner',
    ];

    public function db_input(){
        return $this->hasOne(StappenVelden::class, 'id', 'stap_veld_id');
    }
    public function sub_inputs(){
        return $this->hasMany(InstellingenStappenVelden::class, 'parent_id', 'stap_veld_id');
    }

    //Functions
    public function HTMLLabel(){
        if(in_array($this->db_input->type, $this->labels_blacklist)){
            return '';
        }

        $info = $this->db_input->info;

        $required = $this->verplicht ? '*' : '';
        $tippy = $info ? 'data-tippy-content="' . htmlspecialchars($info) . '"' : null;

        return '<label class="flex-between" >
                    <span>' . $this->db_input->titel . ' ' . $required . '</span>
                    ' . ($tippy ? '<span class="tippy mr-1" ' . $tippy . ' > <i class="fa-regular fa-circle-question"></i> </span>' : '') . '
                </label>';
    }
    public function HTMLInput($options = []){
        $data = $options['data'] ?? '';
        $livewire = $options['livewire'] ?? null;
        $index = $options['index'] ?? null;

        if(!($livewire->invulbaar ?? true)){
            $data .= ' disabled';
        }

        $db_input = $this->db_input;
        $input_data = json_decode($db_input->data ?? '{}');

        return view('projecten.input_components.' . $db_input->type, array_merge([
            'livewire' => $livewire,
            'input' => $this,
            'db_input' => $db_input,
            'veldnaam' => $db_input->veldnaam,
            'context' => $db_input->context,
            'model' => $this->model($index),
            'data' => $data,
            'input_data' => $input_data,
            'invulbaar' => $livewire->invulbaar,
        ], $options['blade_data'] ?? []));
    }

    public function hasSubInput($target){
        foreach($this->sub_inputs as $sub_input){
            if(($sub_input->db_input->veldnaam ?? null) == $target){
                return true;
            }
        }

        return false;
    }

    //Proxy functions
    public function options(){
        return $this->db_input->options();
    }
    public function optionName($value){
        return $this->db_input->optionName($value);
    }
    public function isOptionDisabled($value){
        return $this->db_input->isOptionDisabled($value);
    }
    public function model($index = null){
        return $this->db_input->model($index);
    }
    public function dataValue($key, $def = null){
        return $this->db_input->dataValue($key, $def);
    }

}
