<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Contacten extends Model
{
    use HasFactory;

    protected $connection = 'client_db';
    protected $table = 'contacten';
    
    
    //Relations
    public function role(){
        return $this->hasOne(Roles::class, 'id', 'role_id');
    }

    //static functions
    public static function byEmail($email, $data = null){
        $contact = self::where('email', $email)->first();

        if(!$contact && $data){
            $data['email'] = $email;
            $contact_id = self::insertGetId($data);
            $contact = self::find($contact_id);
        }

        return $contact;
    }

}
