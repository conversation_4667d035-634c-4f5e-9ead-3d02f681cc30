<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Http;
use Throwable;

class Files extends Model
{
    use HasFactory;
    
    protected $connection = 'client_db';
    protected $table = 'files';
    
    //functions
    public function fullpath(){
        return $this->path.'/'.$this->src;
    }
    public function url(){
        return _file($this->guid);
    }
    public function isImg(){
        return $this->icon == 'img.png';
    }
    public function icon(){
        return url('dashboard/img/files/'.$this->icon);
    }
    public function html(){
        return view('files.components.file_icon', [
					'name' => $this->name,
					'src' => _file($this->guid),
					'icon_src' => $this->icon(),
					'is_image' => $this->isImg(),
        ]);
    }
    
    //Static functions
    public static function getExtensionIcon($extension){
        $extensions = extensions();
        
        if(isset($extensions['img'][$extension])){
            return 'img.png';
        }
        elseif(isset($extensions['mp3'][$extension])) {
            return 'mp3.png';
        }
        elseif(isset($extensions['mp4'][$extension])){
            return 'mp4.png';
        }
        elseif(isset($extensions['pdf'][$extension])){
            return 'pdf.png';
        }
        elseif(isset($extensions['stat'][$extension])){
            return 'stat.png';
        }
        elseif(isset($extensions['txt'][$extension])){
            return 'txt.png';
        }
        elseif(isset($extensions['xlsx'][$extension])){
            return 'xlsx.png';
        }
        elseif(isset($extensions['zip'][$extension])){
            return 'zip.png';
        }
        else{
            return 'und.png';
        }
    }
    public static function downloadAndStoreFile($url, $options){
        $response = Http::get($url);

        if ($response->successful()) {
            $tempPath = tempnam(sys_get_temp_dir(), 'download_');
            file_put_contents($tempPath, $response->body());

            // Convert to UploadedFile instance
            $uploadedFile = new UploadedFile(
                $tempPath,
                basename($url),
                $response->header('Content-Type'),
                null,
                true
            );

            $options['file'] = $uploadedFile;
            if(!isset($options['name'])){
                $options['name'] = basename($url);
            }

            return self::storeFile($options);
        }

        throw new \Exception("Failed to download file from {$url}");
    }
    public static function storeFile($options, $disk = 'domain'){
        try{
            $file = $options['file'];

            $mime = $file->getMimeType();
            $extension = $file->getClientOriginalExtension();
            $name = $options['name'] ?? $file->getClientOriginalName();
            $path = $options['path'] ?? '/overig';

            $guid = guid();
            $path = Files::correctPath($path);
            $icon = Files::getExtensionIcon($extension);

            $filename = "{$guid}.{$extension}";
            Storage::disk($disk)->putFileAs($path.'/', $file, $filename);

            $id = Files::insertGetId([
                'path' => $path,
                'name' => $name,
                'extension' => $extension,
                'src' => $filename,
                'mime' => $mime,
                'icon' => $icon,
                'disk' => $disk,
                'guid' => $guid,
            ]);
            return Files::find($id);
        }
        catch(Throwable $e){
            handleError($e);
            return null;
        }
    }
    public static function storeBase64File($options, $disk = 'domain') {
        try{
            $base64 = $options['base64'];
            $name = $options['name'] ?? 'file';
            $path = $options['path'] ?? '/overig';

            $fileData = base64_decode($base64);

            $finfo = new \finfo(FILEINFO_MIME_TYPE);
            $mime = $finfo->buffer($fileData);
            $extension = explode('/', $mime)[1] ?? 'bin';

            $guid = guid();
            $path = Files::correctPath($path);
            $icon = Files::getExtensionIcon($extension);

            $filename = "{$guid}.{$extension}";

            Storage::disk($disk)->put("{$path}/{$filename}", $fileData);

            $id = Files::insertGetId([
                'path' => $path,
                'name' => $name,
                'extension' => $extension,
                'src' => $filename,
                'mime' => $mime,
                'icon' => $icon,
                'disk' => $disk,
                'guid' => $guid,
            ]);

            return Files::find($id);
        }
        catch(Throwable $e){
            handleError($e);
            return null;
        }
    }
    public static function correctPath($path){
        if(substr($path, 0, 1) != '/'){ $path = "/$path";}
        if(substr($path, -1) == '/'){ $path = substr($path, 0, -1);}
        return $path;
    }
    public static function zipFiles($files, $name, $disk = 'domain'){
        if(!$files->count()){ return null; }

        $path = 'zips';
        $guid = guid();
        $src = "{$guid}.zip";
        $mime = "application/zip";

        $zip = new \ZipArchive();
        $temp_file = tempnam(sys_get_temp_dir(), 'zip');

        $zip->open($temp_file, \ZipArchive::CREATE);
        foreach ($files as $file) {
            $storageFile = Storage::disk($file->disk)->get($file->fullPath());
            $zip->addFromString($file->name . '.' . $file->extension, $storageFile);
        }
        $zip->close();


        $zipContent = file_get_contents($temp_file);
        unlink($temp_file);

        Storage::disk($disk)->put("{$path}/{$src}", $zipContent);

        $id = Files::insertGetId([
            'path' => $path,
            'name' => $name,
            'extension' => 'zip',
            'src' => $src,
            'mime' => $mime,
            'icon' => self::getExtensionIcon('zip'),
            'disk' => $disk,
            'guid' => $guid,
        ]);
        return Files::find($id);

    }
    
    
}
