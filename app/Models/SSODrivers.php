<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Laravel\Socialite\Facades\Socialite;

class SSODrivers extends Model{
    use HasFactory;

    protected $table = 'sso_drivers';
    protected $connection = 'main_db';

    //Relations
    public function domain(){
        return $this->hasOne(Domeinen::class, 'id', 'domain_id');
    }

    //Functions
    public function defineDriver(){
        $driver = [
            'tenant' => $this->tenant_id,
            'client_id' => $this->client_id,
            'client_secret' => $this->client_secret,
            'redirect' => url('/dashboard/sso.php'),
        ];
        config(['services.microsoft' => $driver]);
    }
    public function redirect(){
        $this->defineDriver();
        return Socialite::driver('microsoft')->with(['domain' => 1])->redirect();
    }
    public function user(){
        $this->defineDriver();
        return Socialite::driver('microsoft')->user();
    }

    //Static functions
    public static function byDomain<PERSON>ey($domain_key){
        $domain = Domeinen::where('domein_key', $domain_key)->firstOrFail();
        return SSODrivers::where('domain_id', $domain->id)->firstOrFail();
    }

}
