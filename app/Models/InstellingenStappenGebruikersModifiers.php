<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InstellingenStappenGebruikersModifiers extends Model{
    use HasFactory;

    protected $table = 'instellingen_stappen_gebruikers_modifiers';

    //Functions
    public function apply($users, $project_id){
        switch($this->modifier){
            case 'INCLUDE_PROJECT_USER_BY_ROLE': return $this->applyIncludeProjectUserByRole($users, $project_id);
        }
    }
    public function applyIncludeProjectUserByRole($users, $project_id){
        $user_id = ProjectenGebruikers::where([
            'project_id' => $project_id,
            'role_id' => $this->value,
        ])->first()->user_id ?? null;

       $user = User::find($user_id);
       if(!$user){ return $users; }

       return $users->push($user)->unique('id')->values();
    }

}
