<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InstellingenStappenMails extends Model
{
    use HasFactory;

    protected $connection = 'client_db';
    protected $table = 'instellingen_stappen_mails';
		protected $fillable = ['proces_id', 'stap_id', 'trigger'];
	
		public function isValid(){
			if(!$this->onderwerp){ return false; }
			if(!$this->mail){ return false; }
			if(!count(json_decode($this->ontvangers ?? '[]'))){ return false; }
			
			return true;
		}
		public function isRoleSelected($id){
			return in_array($id, json_decode($this->ontvangers ?? '[]'));
		}
		
}
