<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Config;

class Domeinen extends Model
{
    use HasFactory;

    protected $connection = 'main_db';
    protected $table = 'domeinen';

    //Relations
	public function users(){
		return $this->hasManyThrough(User::class, DomeinKoppel::class, 'domain_id', 'id', 'id', 'user_id');
	}
	public function database(){
		return $this->hasOne(DomeinenDatabases::class, 'domein_id', 'id');
	}
    public function domein_api() {
        return $this->hasMany('App\Models\DomeinApi', 'id', 'domain_id');
    }
    public function domein_koppel() {
        return $this->hasMany('App\Models\DomeinKoppel', 'id', 'domain_id');
    }
    public function sso_driver(){
        return $this->hasOne(SSODrivers::class, 'domain_id', 'id');
    }

	//functions
	public function isAccessible(){
        return DomeinKoppel::where([
            'domain_id' => $this->id,
            'user_id' => User::_id(),
        ])->exists();
    }

    public function setConfig(){
        Config::set('active_domain', $this->id);
        Config::set('active_domain_name', $this->domein);
		Config::set('filesystems.disks.domain.root', storage_path('app').'/domains/'.$this->domein_key);
	}


}
