<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectenOpdrachtformulierenLocaties extends Model{
	use HasFactory;
	
	protected $connection = 'client_db';
	protected $table = 'projecten_opdrachtformulieren_locaties';
	
	public function bron_locatie(){
		return $this->hasOne(ProjectenBronlocaties::class, 'id', 'locatie_id');
	}
	public function bron_omschrijvingen_koppeling(){
		return $this->hasMany(ProjectenOpdrachtformulierenLocatiesOmschrijvingen::class, 'opdrachtformulier_locatie_id', 'id');
	}
	
	public function hasBron($bron_id){
		return ProjectenOpdrachtformulierenLocatiesOmschrijvingen::where([
			'opdrachtformulier_locatie_id' => $this->id,
			'bron_id' => $bron_id
		])->exists();
	}
	
}
