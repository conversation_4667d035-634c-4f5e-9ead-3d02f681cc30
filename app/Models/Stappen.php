<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use ProjectStapType;

class Stappen extends Model
{
    use HasFactory;

    protected $connection = 'main_db';
    protected $table = 'stappen';

    //Relations
    public function proces(){
        return $this->hasOne(Processen::class, 'id', 'proces_id');
    }
    public function processen() {
        return $this->hasOne('App\Models\Processen', 'id', 'proces_id');
    }
    public function stappen_acties() {
        return $this->hasMany('App\Models\StappenActies', 'id', 'stap_id');
    }
    public function stappen_mails() {
        return $this->hasMany('App\Models\StappenMails', 'id', 'stap_id');
    }
    public function stappen_velden() {
        return $this->hasMany('App\Models\StappenVelden', 'id', 'stap_id');
    }
    public function sub_stappen(){
        return $this->hasMany(Stappen::class, 'sub_stap_of', 'id');
    }
    
    //Stappen
    public function isEnabled(){
        return InstellingenStappen::where('stap_id', $this->id)->exists();
    }
    public function stapNr(){
        if($this->sub_stap_of){ return $this->subStapNr(); }

        $stap = $this;
        $index = $this->instellingenStappen()->search(function($item) use ($stap){
            return $item->stap_id == $stap->id;
        });
        
        return $index + 1;
    }
    public function instellingenStappen(){
        if($this->_instellingen_stappen){
            return $this->_instellingen_stappen;
        }

        $this->_instellingen_stappen = InstellingenStappen::where('proces_id', $this->proces_id)->whereNull('sub_stap_of')->orderBy('stap_order', 'ASC')->get();
        return $this->_instellingen_stappen;
    }

    //Sub stappen
    public function hasSubStappen(){
        return !!$this->subStappen()->count();
    }
    public function isSubStap(){
        return $this->sub_stap_of !== null;
    }
    public function subStapNr(){
        $stap = $this->parentStap();

        $sub_stap = $this;
        $index = $this->instellingenSubStappen($stap->id)->search(function($item) use ($sub_stap){
            return $item->stap_id == $sub_stap->id;
        });

        return $stap->stapNr() + (($index + 1) / 10);
    }
    public function subStappen(){
        return Stappen::where('sub_stap_of', $this->id)->orderBy('stap_order')->get();
    }
    public function activeSubStappen(){
        //Steps that are: Enabled in the settings
        $sub_stappen_ids = InstellingenStappen::where('sub_stap_of', $this->id)->pluck('stap_id')->toArray();
        return Stappen::whereIn('id', $sub_stappen_ids)->orderBy('stap_order', 'ASC')->get();
    }
    public function fillableSubStappen($project_id){
        //Steps that are: Enabled in the settings, not completed in the given project, and fillable by the user
        $project = Projecten::findOrFail($project_id);
        return $this->activeSubStappen()->filter(function($stap) use ($project){
            if($project->stapStatus($stap->id) == ProjectStapType::COMPLETED){ return false; }
            if(!$project->isInvulbaar($stap->id)){ return false; }

            return true;
        });
    }
    public function parentStap(){
        return Stappen::find($this->sub_stap_of);
    }
    public function instellingenSubStappen($stap_id){
        if($this->_instellingen_sub_stappen){
            return $this->_instellingen_sub_stappen;
        }

        $this->_instellingen_sub_stappen = InstellingenStappen::where('proces_id', $this->proces_id)->where('sub_stap_of', $stap_id)->orderBy('stap_order', 'ASC')->get();
        return $this->_instellingen_sub_stappen;
    }

    //Gebruikers
    public static function invulbareRoleStappenIds($role_id){
        //Stappen which can be filled in by a role
        return InstellingenStappenGebruikers::select('stap_id')->with('stap')->where([
            'role_id' => $role_id,
            'invullen' => 1
        ])->get()->map(function($stap_gebruikers){
            //If the stap is a sub_stap, return the parent_stap
            if($stap_gebruikers->stap->sub_stap_of){ return $stap_gebruikers->stap->sub_stap_of; }
            return $stap_gebruikers->stap_id;
        })->unique()->toArray();
    }

    //Static
    public static function getID($proces_key, $stap_key){
        $stap = self::where('stap_key', $stap_key)->whereHas('proces', function($query) use ($proces_key){
            $query->where('proces_key', $proces_key);
        })->first();

        return optional($stap)->id;
    }
    public static function getStap($proces_key, $stap_key){
        return Stappen::find( self::getID($proces_key, $stap_key) );
    }

}
