<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Logs extends Model{
    use HasFactory;

    protected $connection = 'main_db';
    protected $table = 'logs';

    //Relations
    public function user(){
        return $this->hasOne(User::class, 'id', 'user_id');
    }
    public function project(){
        return $this->hasOne(Projecten::class, 'id', 'project_id');
    }
    public function stap(){
        return $this->hasOne(Stappen::class, 'id', 'stap_id');
    }

}
