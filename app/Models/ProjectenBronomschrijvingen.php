<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectenBronomschrijvingen extends Model
{
    use HasFactory;

    protected $connection = 'client_db';
    protected $table = 'projecten_bronomschrijvingen';

    //Static functions
    public static function byOmschrijving($omschrijving, $data = null){
        $instance = self::where('omschrijving', $omschrijving)->first();

        if(!$instance && $data){
            $data['omschrijving'] = $omschrijving;
            $omschrijving_id = self::insertGetId($data);
            $instance = self::find($omschrijving_id);
        }

        return $instance;
    }

}
