<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StappenActies extends Model
{
    use HasFactory;

    protected $connection = 'main_db';
    protected $table = 'stappen_acties';

    public function stappen() {
        return $this->hasOne('App\Models\Stappen', 'id', 'stap_id');
    }

    public function processen() {
        return $this->hasOne('App\Models\Processen', 'id', 'proces_id');
    }
}
