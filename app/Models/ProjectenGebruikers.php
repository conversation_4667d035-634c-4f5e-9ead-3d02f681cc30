<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectenGebruikers extends Model{
    
    use HasFactory;
    
    protected $connection = 'client_db';
    protected $table = 'projecten_gebruikers';
    
    protected $fillable = ['project_id', 'role_id'];
    
    //Relations
    public function role(){
        return $this->hasOne(Roles::class, 'id', 'role_id');
    }
    
}
