<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AsbestBronnenReferentiesAdressen extends Model{
	use HasFactory;
	
	protected $connection = 'client_db';
	protected $table = 'asbest_bronnen_referenties_adressen';
	
	public function adres(){
		return $this->hasOne(Adressen::class, 'id', 'adres_id');
	}
	public function project(){
		return $this->hasOne(Projecten::class, 'id', 'project_id');
	}
	
}
