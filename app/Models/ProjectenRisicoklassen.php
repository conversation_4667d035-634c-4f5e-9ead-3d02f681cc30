<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectenRisicoklassen extends Model{
    use HasFactory;

    protected $connection = 'client_db';
    protected $table = 'projecten_risicoklassen';

    //Static functions
    public static function byKlasse($klasse, $data = null){
        $instance = self::where('klasse', $klasse)->first();

        if(!$instance && $data){
            $data['klasse'] = $klasse;
            $klasse_id = self::insertGetId($data);
            $instance = self::find($klasse_id);
        }

        return $instance;
    }

}
