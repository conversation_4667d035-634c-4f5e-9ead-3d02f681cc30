<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RolesPermissions extends Model{
	use HasFactory;
	
	protected $connection = 'main_db';
	protected $table = 'roles_permissions';

    //Static
    public static function name($keyword){
        return self::where('keyword', $keyword)->select('name')->first()->name ?? null;
    }

}
