<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InstellingenProcessen extends Model
{
    use HasFactory;

    protected $connection = 'client_db';
    protected $table = 'instellingen_processen';

    public function instellingen_stappen() {
        return $this->hasMany('App\Models\InstellingenStappen', 'proces_id', 'id');
    }

    public function instellingen_stappen_acties() {
        return $this->hasMany('App\Models\InstellingenStappenActies', 'proces_id', 'id');
    }

    public function instellingen_stappen_mails() {
        return $this->hasMany('App\Models\InstellingenStappenMails', 'proces_id', 'id');
    }

    public function instellingen_stappen_velden() {
        return $this->hasMany('App\Models\InstellingenStappenVelden', 'proces_id', 'id');
    }    
}
