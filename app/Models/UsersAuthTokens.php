<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UsersAuthTokens extends Model{
    use HasFactory;

    protected $table = 'users_auth_tokens';
    protected $connection = 'main_db';



    public static function token($user_id){
        $token = randomString(64);
        UsersAuthTokens::insert([
            'user_id' => $user_id,
            'token' => $token,
        ]);

        return $token;
    }
    public static function validate($token){
        $record = UsersAuthTokens::where([
            'token' => $token,
            'active' => 1,
        ])->first();
        if(!$record){ return null; }

        $record->active = 0;
        $record->save();

        return User::find($record->user_id);
    }
}
