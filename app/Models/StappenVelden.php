<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class StappenVelden extends Model
{
    use HasFactory;

    protected $connection = 'main_db';
    protected $table = 'stappen_velden';
    protected $table_name = 'inforareg_domainsa.stappen_velden';

    public function stappen() {
        return $this->hasOne('App\Models\Stappen', 'id', 'stap_id');
    }
    public function processen() {
        return $this->hasOne('App\Models\Processen', 'id', 'proces_id');
    }
    public function sub_inputs(){
        return $this->hasMany(StappenVelden::class, 'parent_input', 'id');
    }

    //functions
    public function model($index = null){
        $model = $this->context;
        if($index !== null){
            $model .= ".$index";
        }
        if($this->veldnaam){
            $model .= ".$this->veldnaam";
        }

        return $model;
    }

    public function options(){
        if($this->__options){ return $this->__options; }

        $data = json_decode($this->data);

        //Fixed options
        if(isset($data->options)){
            $this->__options = $data->options;
            return $data->options;
        }

        //Database options
        if(isset($data->database)){
            list('table' => $table, 'name' => $name, 'value' => $value) = (array)$data->database;

            $options = [];

            $query = DB::connection('client_db')->table($table)->orderBy($name, 'ASC');
            if(isset($data->database->where)){
                $query->where((array)$data->database->where);
            }
            $records = $query->get();

            foreach($records as $record){
                $option = new \stdClass();
                $option->name = $record->$name;
                $option->value = $record->$value;
                $options[] = $option;
            }

            $this->__options = $options;
            return $options;
        }


        return [];
    }
    public function optionName($value){
        foreach($this->options() as $option){
            if($option->value == $value){ return $option->name; }
        }

        return null;
    }
    public function isOptionDisabled($value){
        foreach($this->options() as $option){
            if($option->value != $value){ continue; }
            return $option->disabled ?? false;
        }
        return false;
    }
    public function isRequired(){
        $settings = InstellingenStappenVelden::where('stap_veld_id', $this->id)->first();
        return !!intval(optional($settings)->verplicht);
    }
    public function dataValue($key, $def = null){
        $data = json_decode($this->data ?? '{}');
        if(!isset($data->$key)){ return $def; }

        return $data->$key;
    }
		
}
