<?php

namespace App\Models;

use App\Classes\ARModel;
use EmailStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Emails extends ARModel{
    use HasFactory;

    protected $table = 'emails';

    //Relations
    public function project(){
        return $this->hasOne(Projecten::class, 'id', 'project_id');
    }
    public function user(){
        return $this->hasOne(User::class, 'id', 'user_id');
    }
    public function stap(){
        return $this->hasOne(Stappen::class, 'id', 'stap_id');
    }
    public function recipients(){
        return $this->hasMany(EmailsRecipients::class, 'mail_id', 'id');
    }

    //Functions
    public function status(){
        return $this->setCache('status_data', EmailStatus::get($this->status));
    }

}
