<?php

namespace App\Models;

use AdresStatus;
use App\Classes\ARModel;
use App\Classes\Price;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Adressen extends ARModel
{
    use HasFactory;

    protected $connection = 'client_db';
    protected $table = 'adressen';

    const ORDERABLY_BY = [ "CONCAT_WS('', straat, huisnummer, toevoeging)", 'postcode', 'plaats', 'complexnummer', 'vhenummer', 'bouwjaar', 'wijk', 'buurt', 'woningtype' ];

    
    //Relations
    public function complex(){
        return $this->hasOne(Complexen::class, 'complexnummer', 'complexnummer');
    }
    public function complex_adressen(){
        return $this->hasMany(Adressen::class, 'complexnummer', 'complexnummer');
    }
    public function projecten() {
        return $this->hasMany(Projecten::class, 'adres_id', 'id');
    }
    public function asbest_bronnen() {
        return $this->hasMany(AsbestBronnen::class, 'adres_id', 'id');
    }
    public function documenten() {
        return $this->hasMany('App\Models\Documenten', 'adres_id', 'id');
    }
    public function temp_hist_bronnen_inventariseerder(){
        return $this->hasMany('App\Models\TempHistBronnenInventariseerder', 'adres_id', 'id')->with('omschrijving', 'locatie', 'risicoklasse');
    }
    public function temp_hist_bronnen_saneerder(){
        return $this->hasMany('App\Models\TempHistBronnenSaneerder', 'adres_id', 'id')->with('omschrijving', 'locatie', 'risicoklasse');
    }

    //Attributes
    public function attributeChange($attribute, $value){
        AdressenAttributeChanges::insert([
            'adres_id' => $this->id,
            'attribute' => $attribute,
            'value' => $value
        ]);
    }

    //Files
    public function getFiles($proces_type, $key){
        $ids = AdressenFiles::where(['adres_id' => $this->id, 'file_key' => $key, 'proces_type' => $proces_type])->pluck('file_id')->toArray();
        return Files::whereIn('id', $ids)->get();
    }
    public function getInstellingenFiles($proces_type){
        return InstellingenProcessenBestanden::where([
            'proces_type' => $proces_type,
            'context' => 'adres'
        ])->get();
    }

    //Projecten Files
    public function getProjectFiles($key, $project_id = null){
        $query = ProjectenFiles::where('file_key', $key);

        if($project_id){
            $query = $query->where('project_id', $project_id);
        }
        else{
            $query = $query->whereHas('project', function($query){
               $query->where('adres_id', $this->id);
            });
        }

        $file_ids = $query->pluck('file_id')->toArray();
        return Files::whereIn('id', $file_ids)->get();
    }

    //Functions
    public function addressLine(){
			return trim("$this->straat $this->huisnummer $this->toevoeging");
    }
    public function fullAddressLine(){
			return trim("$this->straat $this->huisnummer $this->toevoeging, $this->postcode $this->plaats");
    }
    public function complexAdressen(){
        if(isset($this->_complex_adressen)){ return $this->_complex_adressen; }

        //Adressen excl this address
        $this->_complex_adressen = Adressen::where('complexnummer', $this->complexnummer)->where('id', '!=', $this->id)->get();
        return $this->_complex_adressen;
    }
    public function inventarisatieStatus(){
        foreach($this->projecten as $project){
            if($project->getFiles('inventarisatierapport')->count()){
                return AdresStatus::WONING_GEINVENTARISEERD;
            }
        }

        return AdresStatus::WONING_NIET_GEINVENTARISEERD;
    }

    //Asbest functions
    public function referentieBronnen($omschrijving_id = null, $locatie_id = null){
        $query = AsbestBronnenReferenties::where('adres_id', $this->id);
        if($omschrijving_id && $locatie_id){
            $query = $query->whereHas('bron', function($query) use ($omschrijving_id, $locatie_id){
                $query->where([
                    'omschrijving_id' => $omschrijving_id,
                    'locatie_id' => $locatie_id
                ]);
            });
        }

        $ids = $query->pluck('bron_id')->toArray();
        return AsbestBronnen::where('active', 1)->whereIn('id', $ids)->get();
    }
    public function asbestBronnen($omschrijving_id = null, $locatie_id = null){
        $query = AsbestBronnen::where('adres_id', $this->id);
        if($omschrijving_id && $locatie_id){
            $query = $query->where([
                'omschrijving_id' => $omschrijving_id,
                'locatie_id' => $locatie_id
            ]);
        }

        return $query->get();
    }
    public function asbestBronnenTotalCost($options = []){
        $cost = new Price();
        $bronnen = $this->asbest_bronnen;
        $referentie_bronnen = $this->referentieBronnen();

        $bronnen->where('gesaneerd', 0)->where('asbesthoudend', 1)->map(function($bron) use ($cost){
            $cost->merge($bron->cost());
        });

        //Append referentie bronnen costs
        if($options['cost_incl_referentie'] ?? false){
            $referentie_bronnen->where('gesaneerd', 0)->where('asbesthoudend', 1)->map(function($bron) use (&$cost, $bronnen){
                if($bronnen->where('omschrijving_id', $bron->omschrijving_id)->where('locatie_id', $bron->locatie_id)->first()){ return null; }

                $cost->merge($bron->cost());
            });
        }

        //Append Indicatie bronnen costs
        if($options['cost_incl_indicatie'] ?? false && $options['indicatie_percentage'] ?? false){
            $this->complex->uniekeAsbestBronnen()->map(function($bron) use (&$cost, $options){
                $bron_exists = $this->asbestBronnen($bron->omschrijving_id, $bron->locatie_id)->count();
                $referentie_bron_exists = $this->referentieBronnen($bron->omschrijving_id, $bron->locatie_id)->count();
                if($bron_exists || $referentie_bron_exists){ return; }

                $is_indicatie = $this->complex->isIndicatieBron($bron->omschrijving_id, $bron->locatie_id, $options['indicatie_percentage']);
                if(!$is_indicatie){ return; }

                $cost->merge($bron->cost());
            });
        }

        unset($bronnen);
        unset($this->complex);
        unset($referentie_bronnen);
        $this->setRelations([]);

        return $cost;
    }
	
}
