<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Auth;
use Lara<PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    protected $connection = 'main_db';
    protected $fillable = [
        'name',
        'email',
        'password',
    ];
    protected $hidden = [
        'password',
        'remember_token',
    ];
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    
    //Relations
    public function domeinen() {
        return $this->hasManyThrough(Domeinen::class, DomeinKoppel::class, "user_id", "id", "id", "domain_id");
    }
    public function domeinKoppel(){
        return $this->hasMany(DomeinKoppel::class, 'user_id', 'id');
    }
    public function activeDomain(){
        return $this->hasOne(Domeinen::class, 'id', 'active_domain');
    }
    public function role(){
        return $this->hasOneThrough(Roles::class, DomeinKoppel::class, 'user_id', 'id', 'id', 'role_id')->where('domain_id',_domain()->id);
    }
    public function roleByDomain($id){
        $role_id = DomeinKoppel::where(['user_id' => $this->id, 'domain_id' => $id])->first()->role_id ?? null;
        return Roles::find($role_id);
    }
    public function bedrijf(){
        return $this->hasOne(Bedrijven::class, 'id', 'bedrijf_id');
    }
    public function supervised_users(){
        return $this->hasManyThrough(User::class, SupervisorUsers::class, 'supervisor_id', 'id', 'id', 'user_id');
    }
    public function supervisor(){
        return $this->hasOneThrough(User::class, SupervisorUsers::class, 'user_id', 'id', 'id', 'supervisor_id');
    }

    //Functions
    public function censored(){
        unset($this->password);
        unset($this->remember_token);
        return $this;
    }
    public function hasSupervisionOver($user_id){
        return SupervisorUsers::where([
            'supervisor_id' => $this->id,
            'user_id' => $user_id,
        ])->exists();
    }
    public function generateAuthToken(){
        return UsersAuthTokens::token($this->id);
    }

    public function logoGUID(){
        return optional($this->bedrijf)->logo_guid;
    }
    public function logoHTML(){
        $guid = $this->logoGUID();
        if(!$guid){ return null; }

        return '<img src="'._file($guid).'" >';
    }

    //Static Functions
    public static function _id(){
        return Auth::user()->id ?? 0;
    }
    public static function _name(){
        return Auth::user()->name ?? '';
    }

    //LAVS
    public static function byLavsId($lavs_id){
        return self::where('lavs_id', $lavs_id)->first();
    }

    //Domeinen
    public static function setDomainFromGUID($guid){
        $domain = Domeinen::where('guid', $guid)->first();
        if(!$domain){ return; }

        self::setDomain($domain->id);
    }
    public static function hasDomain($domain_id){
        return DomeinKoppel::where(['user_id' => self::_id(), 'domain_id' => _domain()->id])->exists();
    }
    public static function setDomain($domain_id){
        if(!self::hasDomain($domain_id)){ return; }

        User::where(['id' => self::_id()])->update(['active_domain' => $domain_id]);
    }


}
