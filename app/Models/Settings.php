<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Settings extends Model{
    use HasFactory;
    
    protected $connection = 'client_db';
    protected $table = 'settings';
    
    
    public static function getValue($key, $default = null){
        $setting = Settings::where('key', $key)->first();
        if(!$setting || !$setting->value){ return $default; }
        
        if($setting->type == 'checkbox'){ return json_decode($setting->value, true); }
        elseif($setting->type == 'boolean'){ return $setting->value === '1'; }
        
        return $setting->value;
    }
    public static function setValue($key, $value, $type = null, $blade = null){
        Settings::updateOrInsert(['key' => $key],['value' => $value, 'type' => $type, 'blade' => $blade]);
    }
    
}
