<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectenStappen extends Model{
    use HasFactory;

    protected $connection = 'client_db';
    protected $table = 'projecten_stappen';

    protected $fillable = ['project_id', 'stap_id', 'user_id'];

    public function stap(){
        return $this->hasOne(Stappen::class, 'id', 'stap_id');
    }
    public function project(){
        return $this->hasOne(Projecten::class, 'id', 'project_id');
    }

}
