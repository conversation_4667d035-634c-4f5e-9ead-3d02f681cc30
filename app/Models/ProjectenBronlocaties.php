<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectenBronlocaties extends Model{
    use HasFactory;

    protected $connection = 'client_db';
    protected $table = 'projecten_bronlocaties';

    //Static functions
    public static function byLocatie($locatie, $data = null){
        $instance = self::where('locatie', $locatie)->first();

        if(!$instance && $data){
            $data['locatie'] = $locatie;
            $locatie_id = self::insertGetId($data);
            $instance = self::find($locatie_id);
        }

        return $instance;
    }

}
