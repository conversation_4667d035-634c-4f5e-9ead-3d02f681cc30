<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Roles extends Model{
	use HasFactory;
	
	protected $connection = 'main_db';
	protected $table = 'roles';
	
	
	//Relations
	public function users(){
		return $this->hasManyThrough(Users::class, DomeinKoppel::class, 'role_id', 'id', 'id', 'user_id')->where('domain_id', _domain()->id);
	}
	public function contacten(){
		return $this->hasMany(Contacten::class, 'role_id', 'id');
	}
    public function domeinKoppel(){
        return $this->hasMany(DomeinKoppel::class, 'role_id', 'id');
    }
	
	//Functions
	public function label(){
		return intval($this->login) ? 'Gebruikers' : 'Contacten';
	}
	public function allUsers(){
		return $this->users->merge($this->contacten);
	}

    //Static functions
    public static function by<PERSON>ey($key, $data = null){
        $role = self::where('key', $key)->first();

        if(!$role && $data){
            $data['key'] = $key;
            $role_id = self::insertGetId($data);
            $role = self::find($role_id);
        }

        return $role;
    }

}
