<?php

namespace App\Models;

use App\Classes\ARModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use ProcesType;

class Processen extends ARModel
{
    use HasFactory;

    protected $connection = 'main_db';
    protected $table = 'processen';

    //relations
    public function stappen_acties() {
        return $this->hasMany(StappenActies::class, 'proces_id', 'id');
    }
    public function stappen_mails() {
        return $this->hasMany(StappenMails::class, 'proces_id', 'id');
    }
    public function stappen_velden() {
        return $this->hasMany(StappenVelden::class, 'proces_id', 'id');
    }
    
    public function instellingen_stappen(){
        return $this->hasMany(InstellingenStappen::class, 'proces_id', 'id')->orderBy('stap_id', 'ASC');
    }

    //Proces
    public function isEnabled(){
        if($this->getCache('_is_enabled')){ return $this->getCache('_is_enabled'); }

        $is_enabled = InstellingenProcessen::where('proces_id', $this->id)->exists();
        return $this->setCache('_is_enabled', $is_enabled);
    }
    public function status(){
        return ProcesType::get($this->type);
    }
    public function statusInfo($attr){
        return ProcesType::get($this->type, $attr);
    }

    //Stappen
    public function stappen(){
        return Stappen::where([
            'proces_id' => $this->id,
        ])->whereNull('sub_stap_of')->orderBy('stap_order')->get();
    }
    public function activeStappen($data = []){
        $include_sub = $data['include_sub'] ?? true;
        $include_parent = $data['include_parent'] ?? true;

        $stappen_ids = InstellingenStappen::where(['proces_id' => $this->id]);

        //Exclude parent steps
        if(!$include_parent){
            $parent_stappen_ids = InstellingenStappen::where(['proces_id' => $this->id])->whereNotNull('sub_stap_of')->pluck('sub_stap_of')->toArray();
            $stappen_ids = $stappen_ids->whereNotIn('stap_id', $parent_stappen_ids);
        }

        //Exclude sub steps based on $incl_sub
        if(!$include_sub){
            $stappen_ids = $stappen_ids->whereNull('sub_stap_of');
        }

        $stappen_ids = $stappen_ids->pluck('stap_id')->toArray();
        return Stappen::whereIn('id', $stappen_ids)->orderBy('stap_order')->get();
    }
    public function activeInputs(){
        if($this->active_inputs){ return $this->active_inputs; }
        
        $ids = InstellingenStappenVelden::where(['proces_id' => $this->id, 'is_sub' => 0])->orderBy('stap_veld_id', 'ASC')->pluck('stap_veld_id')->toArray();
        
        $this->active_inputs = StappenVelden::whereIn('id', $ids)->get();
        return $this->active_inputs;
    }
    public function firstStap(){
        return $this->activeStappen()->first();
    }
    public function isFristStapInvulbaar(){
        if(!$this->instellingen_stappen->count()){ return false; }

        return $this->instellingen_stappen->first()->isInvulbaar();
    }

    //Inputs
    public function hasInput($keyword){
        //whereHas is not possible through 2 different databases.

        $input = StappenVelden::where(['veldnaam' => $keyword, 'proces_id' => $this->id])->first();
        if(!$input){ return false; }

        return InstellingenStappenVelden::where('stap_veld_id', $input->id)->exists();
    }
    public function hasContext($context){
        //whereHas is not possible through 2 different databases.

        $inputs = StappenVelden::where(['context' => $context, 'proces_id' => $this->id])->get();
        if(!$inputs->count()){ return false; }

        return InstellingenStappenVelden::whereIn('stap_veld_id', $inputs->pluck('id')->toArray())->exists();
    }
    public function fileKeys(){
        return StappenVelden::where([
            'proces_id' => $this->id,
            'type' => 'file',
            'context' => 'projecten_files'
        ])->groupBy('veldnaam')->pluck('veldnaam')->toArray();
    }

    //Settings
    public function getSetting($key, $default = null){
        return Settings::getValue("proces_{$this->id}_{$key}", $default);
    }

    
    
    

}
