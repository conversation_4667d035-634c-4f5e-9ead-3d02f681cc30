<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DomeinKoppel extends Model{
    use HasFactory;
    
    protected $connection = 'main_db';
    protected $table = 'domein_koppel';
    
    //Relations
    public function user(){
        return $this->hasOne('App\Models\User', 'user_id', 'id');
    }
    
    
    //Static functions
    public static function connect($domain_id, $user_id, $role_id){
        DomeinKoppel::updateOrInsert([
          'user_id' => $user_id,
          'domain_id' => $domain_id,
        ], [
          'role_id' => $role_id,
        ]);
    }
    
}
