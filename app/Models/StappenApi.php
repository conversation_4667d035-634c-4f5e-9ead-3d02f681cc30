<?php

namespace App\Models;

use Facade\FlareClient\Http\Response;
use http\Env\Request;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

use Config;
use <PERSON><PERSON><PERSON><PERSON>n\Invoker\Exception;

class StappenApi extends Model{

    use HasFactory;

    protected $connection = 'main_db';
    protected $table = 'stappen_api';

    //Data
    private $data;
    private $request;
    private $projecten;
    private $planmatig;


    //Relations
    public function user(){
        return $this->hasOne(User::class, 'id', 'user_id');
    }
    public function stap(){
        return $this->hasOne(Stappen::class, 'id', 'stap_id');
    }
    public function proces(){
        return $this->hasOne(Processen::class, 'id', 'proces_id');
    }


    public function execute($options = []){
        $this->request = $options['request'] ?? null;
        $this->project = $options['project'] ?? null;
        $this->projecten = $options['projecten'] ?? null;
        $this->planmatig = $options['planmatig'] ?? false;

        if(!method_exists($this, $this->method)){
            $this->abort([
                'notification' => "API {$this->method} bestaat niet!"
            ], 500);
        }

        return $this->{$this->method}();
    }
    private function setDomain($domain_id){
        $domain = Domeinen::find($domain_id);
        if(!$domain){ return false; }

        $domain->setConfig();
        $domain->database->setConfig();

        return true;
    }

    //Verification
    private function verifyStap($stap){
        if($this->project->stap->stap_key == $stap){ return; }

        $stap = Stappen::where('stap_key', 'aannemen_inventarisatie')->first();
        $notification = $stap->stapNr() > $this->project->stap->stapNr()
            ? 'Vorige stap is nog niet ingevuld!'
            : 'Deze stap is al ingevuld!';

        $this->abort([
            'project_exists' => true,
            'correct_stap' => false,
            'notification' => $notification
        ]);
    }
    private function verifyEnum($value, $enums, $key){
        if($value === null){ return $value; }

        if(!in_array($value, $enums)){
            $this->abort([
                'existing_project' => false,
                'correct_stap' => false,
                'notification' => "'{$value}' is geen geldige optie voor {$key}, mogelijke opties: (".implode(', ', $enums).')',
            ]);
        }

        return $value;
    }

    //Data
    private function getData($keys, $default = null){
        $keys = explode('.', $keys);
        $value = $this->data;

        foreach($keys as $key){
            $value = $value[$key] ?? $default;
        }

        return $value;
    }
    private function verifyRequiredData($context, $data){
        foreach($data as $veldnaam => $value){
            $stap_veld = StappenVelden::where([
                'stap_id' => $this->stap_id,
                'veldnaam' => $veldnaam,
                'context' => $context
            ])->first();
            if(!$stap_veld){ continue; }

            if($stap_veld->isRequired() && !$value){
                $this->abort([
                    'success' => false,
                    'authorisation' => true,
                    'project_exists' => true,
                    'correct_stap' => true,
                    'notification' => "'{$stap_veld->titel}' mag niet leeg zijn."
                ]);
            }
        }

        return $data;
    }

    //Inbound requests
    public function inboundAuth($request){
        $auth = false;

        if($this->authorization_type == 'Basic Authentication'){
            $credentials = json_decode($this->credentials);

            $username_check = $request->getUser() == ar_decrypt($credentials->username);
            $password_check = $request->getPassword() == ar_decrypt($credentials->password);

            $auth = $username_check && $password_check;
        }

        if(!$auth){
            $this->abort([
                'success' => false,
                'authorisation' => false,
                'notification' => 'Onjuiste inloggegevens.'
            ], 401);
        }
    }
    public function response($response, $code = 200){

        StappenApiLog::insert([
            'api_id' => $this->id,
            'url' => $this->endpoint,
            'data' => json_encode($this->data),
            'code' => $code,
            'response' => json_encode($response),
            'error' => null,
        ]);

        return self::staticResponse($response, $code);
    }
    public function positiveResponse(){
        return $this->response([
            'success' => true,
            'authorisation' => true,
            'project_exists' => true,
            'correct_stap' => true,
            'notification' => 'De gegevens zijn correct verzonden en opgeslagen in Asbestregisseur.nl'
        ]);
    }
    public function abort($response, $code = 400){

        StappenApiLog::insert([
            'api_id' => $this->id,
            'url' => $this->endpoint,
            'data' => json_encode($this->data),
            'code' => $code,
            'response' => json_encode($response),
            'error' => $response['error'] ?? ($response['notification'] ?? null),
        ]);

        self::staticAbort($response, $code);
    }

    public static function staticResponse($response, $code = 200){
        return response([
            'success' => $response['success'] ?? null,
            'authorisation' => $response['authorisation'] ?? true,
            'existingproject' => $response['project_exists'] ?? null,
            'correctstap' => $response['correct_stap'] ?? null,
            'notification' => $response['notification'] ?? null,
        ], $code);
    }
    public static function staticAbort($response, $code = 400){
        abort(response([
            'success' => false,
            'authorisation' => $response['authorisation'] ?? true,
            'existingproject' => $response['project_exists'] ?? null,
            'correctstap' => $response['correct_stap'] ?? null,
            'notification' => $response['notification'] ?? null,
        ], $code));
    }

    //Outbound Requests
    private function post($data = [], $headers = []){
        $curl = curl_init();

        curl_setopt($curl, CURLOPT_URL, $this->endpoint);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array_merge([
            'Content-Type: application/json',
            'Authorization: '.$this->outboundAuth(),
        ], $headers));
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));

        $raw_response = curl_exec($curl);
        $code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $response = json_decode($raw_response);
        $error = curl_error($curl);
        curl_close($curl);

        StappenApiLog::insert([
            'api_id' => $this->id,
            'url' => $this->endpoint,
            'data' => json_encode($data),
            'code' => $code,
            'response' => $raw_response,
            'error' => $error,
        ]);
    }
    private function outboundAuth(){
        $credentials = json_decode($this->credentials);

        if($this->authorization_type == 'Basic Authentication'){
            return 'Basic '. base64_encode(ar_decrypt($credentials->username).':'.ar_decrypt($credentials->password));
        }
        elseif($this->authorization_type == 'Bearer Token'){
            return 'Bearer '. ar_decrypt($credentials->token);
        }

        return null;
    }


    ////Api Methods

    //Inventarisatiebureau
    private function invInboundInit(){
        $this->data = $this->request->json()->all();

        //Set and verify domain
        $domain_set = $this->setDomain($this->data['domeinid']);
        if(!$domain_set){
            $this->abort([
                'notification' => 'Onjuiste Domein ID'
            ]);
        }

        //Set and verify Project
        $this->project = Projecten::find($this->data['projectid']);
        if(!$this->project){
            $this->abort([
                'project_exists' => false,
                'notification' => 'Project niet gevonden'
            ]);
        }
    }

    private function invCreateProject(){
        $project = $this->project;
        $adres = $project->adres;

        $teschnisch_beheerder = $project->getContact('technisch_beheerder');
        $inventarisatiebureau = $project->getGebruiker('inventariseerder');

        $data = [
            'ref_id' => $project->id,
            'data' =>  [
                'domeinid' => _domain()->id,
                'projectid' => $project->id,
                'projectnummer' => $project->projectnummer,
                'opdrachtnummer' => $project->opdrachtnummer,
                'adresid' => $adres->id,
                'straat' => $adres->straat,
                'huisnummer' => $adres->huisnummer,
                'toevoeging' => $adres->toevoeging,
                'plaats' => $adres->plaats,
                'postcode' => $adres->postcode,
                'vhenummer' => $adres->vhenummer,
                'complexnummer' => $adres->complexnummer,
                'subcomplexnummer' => $adres->subcomplexnummer,
                'bouwjaar' => $adres->bouwjaar,
                'wijk' => $adres->wijk,
                'buurt' => $adres->buurt,
                'woningtype' => $adres->woningtype,
                'plattegrond' => $project->getFiles('plattegrond')->count()
                    ? Files::zipFiles( $project->getFiles('plattegrond'), "plattegrond.zip")->url()
                    : null,
                'plattegrond2' => null,
                'plattegrond3' => null,
                'situatiehuisnummering' => null, //TODO
                'steekproefcomplexinventarisatie' => null, //TODO
                'bestek1' => $project->getFiles('bestek')->count()
                    ? Files::zipFiles( $project->getFiles('bestek'), "bestek.zip")->url()
                    : null,
                'bestek2' => null,
                'bestek3' => null,
                'opdrachtformulier' => $project->opdrachtformulierUrl(),
                'opmerkingalgemeen' => null, //TODO
                'opdrachtdatum' => $project->aangemaakt_op,
                'soortproject' => $project->soortproject,
                'beheerdernaam' => optional($teschnisch_beheerder)->name,
                'opmerkinginventarisatiebureau' => $project->stapOpmerking('inventarisatie_verstrekken'),
                'inventarisatiebureau' =>  [
                    'id' => optional($inventarisatiebureau)->id,
                    'bedrijfsnaam' => optional(optional($inventarisatiebureau)->bedrijf)->naam,
                    'contactnaam' => optional($inventarisatiebureau)->name,
                    'telefoon' => optional($inventarisatiebureau)->telefoon
                ]
            ]
        ];

        $this->post($data);
    }
    private function invCreatePlanmatigeProjects(){
        $project = $this->projecten->first();

        $teschnisch_beheerder = $project->getContact('technisch_beheerder');
        $inventarisatiebureau = $project->getGebruiker('inventariseerder');

        $data = [
            'ref_id' => $project->planmatig_key,
            'data' => [
                'domeinid' => _domain()->id,
                'planmatigprojectid' => $project->planmatig_key,
                'projectnummer' => $project->projectnummer,
                'opdrachtnummer' => $project->opdrachtnummer,
                'opdrachtformulier' => $project->opdrachtformulierUrl(),
                'opmerkingalgemeen' => null, //TODO
                'opdrachtdatum' => $project->aangemaakt_op,
                'soortproject' => $project->soortproject,
                'beheerdernaam' => optional($teschnisch_beheerder)->name,
                'opmerkinginventarisatiebureau' => $project->stapOpmerking('inventarisatie_verstrekken'),
                'adressen' => [],
                'inventarisatiebureau' => [
                    'id' => optional($inventarisatiebureau)->id,
                    'bedrijfsnaam' => optional(optional($inventarisatiebureau)->bedrijf)->naam,
                    'contactnaam' => optional($inventarisatiebureau)->name,
                    'telefoon' => optional($inventarisatiebureau)->telefoon
                ]
            ]
        ];

        foreach($this->projecten as $planmatig_project){

            $planmatig_adres = $planmatig_project->adres;

            $data['data']['adressen'][] = [
                'projectid' => $planmatig_project->id,
                'projectnummer' => $planmatig_project->projectnummer,
                'opdrachtnummer' => $planmatig_project->opdrachtnummer,
                'adresid' => $planmatig_adres->id,
                'straat' => $planmatig_adres->straat,
                'huisnummer' => $planmatig_adres->huisnummer,
                'toevoeging' => $planmatig_adres->toevoeging,
                'plaats' => $planmatig_adres->plaats,
                'postcode' => $planmatig_adres->postcode,
                'vhenummer' => $planmatig_adres->vhenummer,
                'complexnummer' => $planmatig_adres->complexnummer,
                'subcomplexnummer' => $planmatig_adres->subcomplexnummer,
                'bouwjaar' => $planmatig_adres->bouwjaar,
                'wijk' => $planmatig_adres->wijk,
                'buurt' => $planmatig_adres->buurt,
                'woningtype' => $planmatig_adres->woningtype,
                'plattegrond' => $planmatig_project->getFiles('plattegrond')->count()
                    ? Files::zipFiles( $planmatig_project->getFiles('plattegrond'), "plattegrond.zip")->url()
                    : null,
                'plattegrond2' => null,
                'plattegrond3' => null,
                'situatiehuisnummering' => null, //TODO
                'steekproefcomplexinventarisatie' => null, //TODO
                'bestek1' => $planmatig_project->getFiles('bestek')->count()
                    ? Files::zipFiles( $planmatig_project->getFiles('bestek'), "bestek.zip")->url()
                    : null,
                'bestek2' => null,
                'bestek3' => null
            ];
        }

        $this->post($data);

    }
    private function invAannemenInventarisatie(){
        $this->invInboundInit();
        $this->verifyStap('aannemen_inventarisatie');

        //Reject stap
        if($this->data['projectafwijzing'] == 'ja'){
            return $this->invAannemenInventarisatieReject();
        }

        //Map and verify data
        $sanering_data = $this->verifyRequiredData('asbest_sanering', [
            'inventarisatiedatum' => $this->getData('beoogde_inventarisatiedatum'),
            'inventarisatietijd' => $this->getData('inventarisatietijd'),
            'reden_inventarisatie_later' => $this->getData('reden_inv_later')
        ]);
        ProjectenAsbestSanering::where('project_id', $this->project->id)->update($sanering_data);

        //Set DIA
        $role = Roles::byKey('dia', [
            'name' => 'DIA',
            'login' => 0
        ]);
        $contact = Contacten::byEmail($this->getData('dia.email'), [
            'name' => $this->getData('dia.naam'),
            'telefoon' => $this->getData('dia.telefoon'),
            'role_id' => $role->id,
        ]);
        $this->project->setContact($role->id, $contact->id);

        $this->project->setStapOpmerking( $this->getData('opmerkingen2') );
        $this->project->completeStap();

        return $this->positiveResponse();
    }
    private function invAannemenInventarisatieReject(){
        $new_stap = Stappen::where('stap_key', 'inventarisatie_verstrekken')->first();
        $this->project->rejectStap($new_stap->id);

        return $this->positiveResponse();
    }
    private function invBevestigenUitvoerInventarisatie(){
        $this->invInboundInit();
        $this->verifyStap('bevestigen_uitvoer_inventarisatie');

        //Map and verify data
        $sanering_data = $this->verifyRequiredData('asbest_sanering', [
            'inventarisatiedatum' => $this->getData('inventarisatiedatum'),
            'reden_inventarisatie_later' => $this->getData('reden_inv_later')
        ]);
        ProjectenAsbestSanering::where('project_id', $this->project->id)->update($sanering_data);

        $this->project->setStapOpmerking( $this->getData('opmerkingen3') );
        $this->project->completeStap();

        return $this->positiveResponse();
    }
    private function invOpleverenInventarisatierapport(){
        $this->invInboundInit();
        $this->verifyStap('opleveren_inventarisatierapport');

        //Store Files
        foreach(['', 2, 3, 4, 5] as $rapport_index){
            $this->project->storeBase64File('inventarisatierapport', "inventarisatierapport{$rapport_index}", $this->getData("inventarisatierapport{$rapport_index}"));
        }

        //Update Asbest data
        $asbest_data = $this->verifyRequiredData('asbest_sanering', [
            'lavs_activeringscode' => $this->getData('lavsactiveringscode'),
            'lavs_project_id' => $this->getData('lavsprojectid')
        ]);
        ProjectenAsbestSanering::where('project_id', $this->project->id)->update($asbest_data);

        //Asbest Bronnen
        foreach($this->getData('bronnenarray', []) as $bron){
            $bron = optional((object)$bron);

            $omschrijving = ProjectenBronomschrijvingen::byOmschrijving( $bron->bron_omschrijving, [ 'bronnen' => 1 ] );
            $locatie = ProjectenBronlocaties::byLocatie( $bron->bron_locatie, [ 'bronnen' => 1 ] );
            $omgeving = ProjectenBronomgevingen::byOmgeving( $bron->bron_omgeving, [ 'bronnen' => 1 ] );
            $risicoklasse = ProjectenRisicoklassen::byKlasse( $bron->bron_risicoklasse, [ 'bronnen' => 1 ] );

            $bron_data = [
                'omschrijving_id' => $omschrijving->id,
                'locatie_id' => $locatie->id,
                'risicoklasse_id' => $risicoklasse->id,
                'omgeving_id' => $omgeving->id,
                'saneeradvies' => $bron->bron_saneren,
                'aantal' => $bron->bron_aantal,
                'eenheid' =>  str_replace(['m¹', 'm²', 'm³'], ['m1', 'm2', 'm3'], $bron->bron_eenheid),
                'bevestiging' => $bron->bron_bevestiging,
                'percentageserpentijn' => $bron->bron_percentageserpentijn,
                'persentageamfibool' => $bron->bron_persentageamfibool,
                'analysecertificaatnummer' => $bron->analysecertificaatnummer,
                'opmerking' => $bron->opmerking,
                'zav' => $this->verifyEnum($bron->bron_zav, ['ja', 'nee', 'onbekend'], 'bron_zav' ),
                'verwering' => $this->verifyEnum( $bron->bron_verwering, ['niet', 'licht', 'zwaar'], 'bron_verwering' ),
                'beschadiging' => $this->verifyEnum( $bron->bron_beschadiging, ['niet', 'licht', 'zwaar'], 'bron_beschadiging' ),
                'hechtgebondenheid' => $this->verifyEnum($bron->bron_hechtgebondenheid, ['ja', 'nee'], 'bron_hechtgebondenheid'),
                'asbesthoudend' => $this->verifyEnum($bron->bron_asbesthoudend, ['ja', 'nee'], 'bron_asbesthoudend' ) == 'ja' ? 1 : 0,
            ];

            //Create a planamtig bron if the project is planmatig
            if($this->project->isPlanmatig()){

                $planmatig_bron_keys = [
                    'bronnummer' => $bron->bron_nummer,
                    'planmatig_key' => $this->project->planmatig_key,
                ];

                AsbestBronnen::updateOrInsert($planmatig_bron_keys, $bron_data);
                $bron_data['planmatig_parent'] = AsbestBronnen::where($planmatig_bron_keys)->value('id');
            }



            AsbestBronnen::updateOrInsert([
                'project_id' => $this->project->id,
                'adres_id' => $this->project->adres_id,
                'bronnummer' => $bron->bron_nummer,
            ], $bron_data);

        }

        foreach($this->getData('locatielijst', []) as $locatie){
            $locatie = ProjectenBronlocaties::byLocatie( $locatie, [ 'bronnen' => 1 ] );
            ProjectenGeinventariseerdeLocaties::updateOrInsert([
                'project_id' => $this->project->id,
                'locatie_id' => $locatie->id,
            ]);
        }

        $this->project->setStapOpmerking( $this->getData('opmerkingen4') );
        $this->project->completeStap();

        return $this->positiveResponse();
    }



}
