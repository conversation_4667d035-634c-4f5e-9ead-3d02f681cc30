<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectenOpdrachtformulieren extends Model{
	use HasFactory;
	
	protected $connection = 'client_db';
	protected $table = 'projecten_opdrachtformulieren';
    protected $fillable = ['project_id', 'adres_id', 'guid'];
	
	public function adres(){
		return $this->hasOne(Adressen::class, 'id', 'adres_id');
	}
	public function project(){
		return $this->hasOne(Projecten::class, 'id', 'project_id');
	}
	
	//Koppeltabellen
	public function locaties_koppeling(){
		return $this->hasMany(ProjectenOpdrachtformulierenLocaties::class, 'opdrachtformulier_id', 'id');
	}
	
	//functions
	public function html(){
		return view('files.components.file_icon', [
			'src' => url("dashboard/projecten/opdrachtformulier/{$this->guid}"),
			'icon_src' => url('dashboard/img/files/pdf.png'),
		]);
	}
    public function clearLocaties(){
        $locaties_ids = ProjectenOpdrachtformulierenLocaties::where('opdrachtformulier_id', $this->id)->pluck('id')->toArray();

        ProjectenOpdrachtformulierenLocaties::where('opdrachtformulier_id', $this->id)->delete();
        ProjectenOpdrachtformulierenLocatiesOmschrijvingen::whereIn('opdrachtformulier_locatie_id', $locaties_ids)->delete();
    }
	
}
