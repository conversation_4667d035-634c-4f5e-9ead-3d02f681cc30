<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Config;
use DB;

class DomeinenDatabases extends Model{
	use HasFactory;
	
	protected $connection = 'main_db';
	protected $table = 'domeinen_databases';

	//Functions
	public function setConfig(){
		Config::set('database.connections.client_db.database', $this->database_name);
		Config::set('database.connections.client_db.username', $this->username);
		Config::set('database.connections.client_db.password', ar_decrypt($this->password));
		DB::purge('client_db');
	}
	
}
