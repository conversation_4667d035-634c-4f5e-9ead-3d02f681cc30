<?php

namespace App\Models;

use App\Classes\Price;
use AsbestBronStatus;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use PriceType;

class AsbestBronnen extends Model {
    use HasFactory;

    protected $connection = 'client_db';
    protected $table = 'asbest_bronnen';
	protected $fillable = ['project_id', 'adres_id', 'aantal', 'planmatig_key', 'planmatig_parent'];

    //Relations
	public function bronlocatie(){
		return $this->hasOne(ProjectenBronlocaties::class, 'id', 'locatie_id');
	}
	public function bronomschrijving(){
		return $this->hasOne(ProjectenBronomschrijvingen::class, 'id', 'omschrijving_id');
	}
	public function risicoklasse(){
		return $this->hasOne(ProjectenRisicoklassen::class, 'id', 'risicoklasse_id');
	}
	public function project(){
		return $this->hasOne(Projecten::class, 'id', 'project_id');
	}
    public function adres() {
        return $this->hasOne('App\Models\Adressen', 'id', 'adres_id');
    }
    public function deelsanering_child(){
        return $this->hasOne(AsbestBronnen::class, 'deelsanering_parent', 'id')->where('active', 1);
    }
    public function deelsanering_parent(){
        return $this->hasOne(AsbestBronnen::class, 'id', 'deelsanering_parent')->where('active', 1);
    }

    //Functions
    public function cost(){
        $cost = new Price();

        if(!$this->bronomschrijving || !$this->bronlocatie){ return $cost; }

        if($this->bronlocatie->binnen){
            $cost->set(PriceType::EXCL, $this->bronomschrijving->kosten_binnen_excl);
            $cost->set(PriceType::INCL, $this->bronomschrijving->kosten_binnen_incl);
        }
        else if($this->bronlocatie->binnen){
            $cost->set(PriceType::EXCL, $this->bronomschrijving->kosten_buiten_excl);
            $cost->set(PriceType::INCL, $this->bronomschrijving->kosten_buiten_incl);
        }

        return $cost;
    }
    public function setInactive(){
        if($this->planmatig_key){

            $this->active = 0;
            $this->planmatig_key = "-{$this->planmatig_key}";
            $this->save();

            AsbestBronnen::where('planmatig_parent', $this->id)->each(function($bron){
                $bron->setInactive();
            });
            return;
        }

        $this->active = 0;
        $this->project_id = "-{$this->project_id}";
        $this->adres_id = "-{$this->adres_id}";
        $this->save();
    }
    public function status(){

        if($this->asbesthoudend && !$this->gesaneerd){
            return AsbestBronStatus::AANGETROFFEN_NIET_GESANEERD;
        }
        elseif($this->asbesthoudend && $this->gesaneerd){
            return AsbestBronStatus::GESANEERD;
        }
        elseif(!$this->asbesthoudend){
            return AsbestBronStatus::ASBESTVRIJ;
        }

        return null;
    }
    public function statusInfo($key){
        return AsbestBronStatus::get( $this->status(), $key );
    }
    public function referentieStatusInfo($key){
        return AsbestBronStatus::get( $this->referentieStatus(), $key );
    }
    public function referentieStatus(){
        if($this->asbesthoudend){
            return AsbestBronStatus::REFERENTIE;
        }

        return AsbestBronStatus::REFERENTIE_ASBESTVRIJ;
    }
    public function locatieOmschrijving(){
        return ($this->bronomschrijving->omschrijving ?? '')." ".($this->bronlocatie->locatie ?? '');
    }
    public function inventarisatieRapporten(){
        if(!$this->project){ return collect([]); }
        if(isset($this->_inventarisatierapporten)){ return $this->_inventarisatierapporten; }

        $files = $this->project->getFiles('inventarisatierapport');
        $files->map(function($file){
           $file->rapport_valid = Carbon()->diffInMonths($file->created_at) <= env('ASBEST_INVENTARISATIERAPPORT_GELDIGHEID');
           $file->rapport_valid_until = Carbon($file->created_at)->addMonths(env('ASBEST_INVENTARISATIERAPPORT_GELDIGHEID'))->format('Y-m-d');
        });

        $this->_inventarisatierapporten = $files;
        return $this->_inventarisatierapporten;
    }

    //Transfers
    public function lastTransfer(){
        return AsbestBronnenTransfers::where('bron_id', $this->id)->orderBy('created_at', 'desc')->first();
    }
    public function transferToProject($key){
        $planmatig = !is_numeric($key);

        //Deduct from based on if the bron is connected to planmatig_key or project_id
        $from_key = $this->planmatig_key
            ? 'from_planmatig_key'
            : 'from_project_id';
        $from_value = $this->planmatig_key
            ? $this->planmatig_key
            : $this->project_id;

        //To based on $planmatig parameter
        $to_key = $planmatig
            ? 'to_planmatig_key'
            : 'to_project_id';

        AsbestBronnenTransfers::insert([
            'bron_id' => $this->id,
            $from_key => $from_value,
            $to_key => $key,
        ]);

        $adres_id = null;
        if(!$planmatig){
            $project = Projecten::find($key);
            $adres_id = $project->adres_id;
        }

        $this->adres_id = $adres_id;
        $this->project_id = $planmatig ? null : $key;
        $this->planmatig_key = $planmatig ? $key : null;

        $this->save();
        $this->refresh();
    }
    public function revertLastTransfer(){
        $last_transfer = $this->lastTransfer();
        if(!$last_transfer){ return; }

        $this->transferToProject($last_transfer->from_project_id ?? $last_transfer->from_planmatig_key);
    }
    public function recentlyTransferdToProject($key){
        $last_transfer = $this->lastTransfer();
        if(!$last_transfer){ return false; }

        return $last_transfer->to_project_id == $key || $last_transfer->to_planmatig_key == $key;
    }

    //Deelsanering
    public function createDeelsanering(){
        $deelsanering_bron = $this->replicate();

        $deelsanering_bron->bronnummer = $deelsanering_bron->bronnummer.'-D';
        $deelsanering_bron->saneeradvies = 'ja';
        $deelsanering_bron->deelsanering = 1;
        $deelsanering_bron->deelsanering_parent = $this->id;
        $deelsanering_bron->aantal = null;
        $deelsanering_bron->save();

        $this->saneeradvies = 'nee';
        $this->save();
    }
    public function deleteDeelsanering(){
        $deelsanering_bron = $this->deelsanering_child;
        if(!$deelsanering_bron){ return; }

        $this->aantal += $deelsanering_bron->aantal;
        $this->save();
        $deelsanering_bron->save();

        $deelsanering_bron->setInactive();
    }

}
