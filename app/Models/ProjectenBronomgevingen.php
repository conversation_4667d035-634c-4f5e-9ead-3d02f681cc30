<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectenBronomgevingen extends Model{
    protected $connection = 'client_db';
    protected $table = 'projecten_bronomgevingen';

    //Static functions
    public static function byOmgeving($omgeving, $data = null){
        $instance = self::where('omgeving', $omgeving)->first();

        if(!$instance && $data){
            $data['omgeving'] = $omgeving;
            $omgeving_id = self::insertGetId($data);
            $instance = self::find($omgeving_id);
        }

        return $instance;
    }

}
