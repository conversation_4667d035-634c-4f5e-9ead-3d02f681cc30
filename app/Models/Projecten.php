<?php

namespace App\Models;

use App\Classes\ARModel;
use App\Mail\Blank;
use Carbon\Carbon;
use Couchbase\Role;
use EmailStatus;
use EmailsTemplates as EmailsTemplatesEnums;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Mail;
use ProjectStapType;
use ProjectStatus;
use Log;

class Projecten extends ARModel{
    use HasFactory;

    protected $connection = 'client_db';
    protected $table = 'projecten';
    protected $fillable = ['guid', 'adres_id', 'proces_id', 'stap_id', 'planmatig_key'];


    //Relations
    public function adres(){
        return $this->hasOne(Adressen::class, 'id', 'adres_id');
    }
    public function bewoner(){
        return $this->hasOne(ProjectenBewoners::class, 'project_id', 'id');
    }
    public function documenten() {
        return $this->hasMany(Documenten::class, 'project_id', 'id');
    }
    public function opdrachtformulier(){
        return $this->hasOne(ProjectenOpdrachtformulieren::class, 'project_id', 'id');
    }

    public function projecten_gebruikers(){
        return $this->hasMany(ProjectenGebruikers::class, 'project_id', 'id');
    }
    public function asbest_bronnen() {
        return $this->hasMany(AsbestBronnen::class, 'project_id', 'id');
    }
    public function asbest_sanering(){
        return $this->hasOne(ProjectenAsbestSanering::class, 'project_id', 'id');
    }

    public function instellingen_stappen() {
        return $this->hasOne(InstellingenStappen::class, 'stap_id', 'stap_id');
    }
    public function instellingen_stappen_gebruikers() {
        return $this->hasOne(InstellingenStappenGebruikers::class, 'stap_id', 'stap_id');
    }
    public function stappen() {
	    return $this->hasOne(Stappen::class, 'id', 'stap_id')->with('sub_stappen');
    }
    public function stap() {
      return $this->stappen();
    }
    public function proces() {
	    return $this->hasOne(Processen::class, 'id', 'proces_id');
    }
    public function projecten_redenen() {
        return $this->hasOne('App\Models\ProjectenRedenen', 'id', 'redenannuleren');
    }

    //Functions
    public function users(){
        if($this->getCache('users')){ return $this->getCache('users'); }

        $project_users = ProjectenGebruikers::where('project_id', $this->id)->get();
        $users = $project_users->map(function($con){
            $user = User::find($con->user_id);
            $user->role = Roles::find($con->role_id);

            return $user;
        });

        return $this->setCache('users', $users);
    }
    public function complete(){
        $this->setStatus(ProjectStatus::AFGEROND);
        $this->refresh();

        Log::info("Project afgerond.", [
            'project_id' => $this->id,
            'stap_id' => $this->stap_id,
        ]);
    }

    //Users
    public function getGebruiker($key){
        //Key can be either role_key or role_id
        $role = Roles::where('id', $key)->orWhere('key', $key)->first();
        $user_id = ProjectenGebruikers::where([
            'project_id' => $this->id,
            'role_id' => optional($role)->id,
        ])->value('user_id');

        return User::find($user_id);
    }
    public function getContact($key){
        //Key can be either role_key or role_id
        $role = Roles::where('id', $key)->orWhere('key', $key)->first();
        $user_id = ProjectenContacten::where([
            'project_id' => $this->id,
            'role_id' => optional($role)->id,
        ])->value('contact_id');

        return Contacten::find($user_id);
    }

    public function setGebruiker($role_id, $user_id){
        if(!$role_id || !$user_id){ return; }

        ProjectenGebruikers::updateOrInsert([
            'project_id' => $this->id,
            'role_id' => $role_id,
        ],[
            'user_id' => $user_id,
        ]);
    }
    public function setContact($role_id, $contact_id){
        ProjectenContacten::updateOrInsert([
            'project_id' => $this->id,
            'role_id' => $role_id,
            'contact_id' => $contact_id,
        ]);
    }

    public function projectGebruikers(){
        if( $this->getCache('project_gebruikers') ){ return $this->getCache('project_gebruikers'); }

        //HasManyThrough doen't have the $connction signature;
        $ids = ProjectenGebruikers::where('project_id', $this->id)->pluck('user_id')->toArray();
        $users = User::whereIn('id', $ids)->with('role')->get();

        
        return $this->setCache('project_gebruikers', $users);
    }
    public function projectContacten(){
        if( $this->getCache('project_contacten') ){ return $this->getCache('project_contacten'); }

        //HasManyThrough doen't have the $connction signature;
        $ids = ProjectenContacten::where('project_id', $this->id)->pluck('contact_id')->toArray();
        $users = Contacten::whereIn('id', $ids)->with('role')->get();

        return $this->setCache('project_contacten', $users);
    }

    //Stappen
    public function assertStap($key){
        return $this->stap->stap_key == $key;
    }
    public function isStapCurrent($stap_id){
        $status = $this->stapStatus($stap_id);
        return $status == ProjectStapType::CURRENT || $status == ProjectStapType::CURRENT_SUB;
    }
    public function nextStap(){
        $stap = $this->stap;
        $stappen = $stap->instellingenStappen();

        //Main stap
        if(!$stap->hasSubStappen()){
            //Stapnr is (index + 1), so it's the next index.
            $stap_id = $stappen[$stap->stapNr()]->stap_id ?? null;
            return Stappen::find($stap_id);
        }

        //Sub-stappen
        $available_sub_stappen = $stap->activeSubStappen()->filter(function($sub_stap){
            return $this->stapStatus($sub_stap->id) != ProjectStapType::COMPLETED;
        });

        //In case of available sub-stappen stay on the current main-stap, excluding the current sub stap
        if($available_sub_stappen->count() > 1){
            return $this->stap;
        }

        //Return next main stap
        $stap_id = $stappen[$stap->stapNr()]->stap_id ?? null;
        return Stappen::find($stap_id);
    }
    public function stapStatus($stap_id){
        $stap = Stappen::find($stap_id);
        $project_stap = $this->stap;
        $completed_at = $this->stapCompletedAt($stap_id);

        $stap_nr = $stap->stapNr();
        $project_stap_nr = $project_stap->stapNr();

        if($stap->isSubStap()){
            if(intval(floor($stap_nr)) <= $project_stap_nr && $completed_at){ return ProjectStapType::COMPLETED; }
            else if(intval(floor($stap_nr)) < $project_stap_nr && !$completed_at){ return ProjectStapType::SKIPPED; }
            else if(intval(floor($stap_nr)) == $project_stap_nr){ return ProjectStapType::CURRENT_SUB; }
            else{ return ProjectStapType::OPEN; }
        }

        if($stap_nr < $project_stap_nr && $completed_at){ return ProjectStapType::COMPLETED; }
        else if($stap_nr < $project_stap_nr && !$completed_at){ return ProjectStapType::SKIPPED; }
        else if($stap_nr == $project_stap_nr && $this->status == 'AFGEROND'){ return ProjectStapType::COMPLETED; }
        else if($stap_nr === $project_stap_nr){ return ProjectStapType::CURRENT; }
        else{ return ProjectStapType::OPEN; }
    }
    public function setStapOpmerking($opmerking, $stap_id = null){
        $stap_id = $stap_id ?? $this->stap_id;

        ProjectenStappen::where([
            'project_id' => $this->id,
            'stap_id' => $stap_id,
        ])->update([
            'opmerking' => $opmerking,
        ]);
    }
    public function stapOpmerking($stap_key){
        $stap = Stappen::where('stap_key', $stap_key)->first();
        return ProjectenStappen::where([
            'stap_id' => optional($stap)->id,
            'project_id' => $this->id,
        ])->value('opmerking');
    }
    public function stapCompletedAt($stap_id){
        $attr = "_completed_stap_$stap_id";
        if($this->getCache($attr)){
            return $this->getCache($attr);
        }
        
        $date = ProjectenStappen::where(['project_id' => $this->id, 'stap_id' => $stap_id])->first();
        $date = (($date->completed_at ?? null) ? Carbon::parse($date->completed_at) : null);

        return $this->setCache($attr, $date);
    }
    public function completeStap($stap_id = null, $next_stap_id = null, $medium = null){
        if(!$stap_id){
            $stap_id = $this->stap_id;
        }
        if(!$next_stap_id){
            $next_stap_id = optional($this->nextStap())->id;
        }

        $stap = Stappen::find($stap_id);


        //Complete all substappen
        if($stap->hasSubStappen()){
            foreach($stap->subStappen() as $sub_stap){
                $this->completeStap($sub_stap->id, medium: $medium);
            }
            return;
        }

        //Also complete the parent stap if the current stap is the last sub stap
        if($stap->isSubStap() && $next_stap_id != $this->stap_id){
            ProjectenStappen::updateOrInsert([
                'project_id' => $this->id,
                'stap_id' => $stap->sub_stap_of,
            ], [
                'completed_at' => Carbon::now(),
                'user_id' => User::_id(),
            ]);
        }

        //Complete given step
        ProjectenStappen::updateOrInsert([
          'project_id' => $this->id,
          'stap_id' => $stap_id,
        ], [
          'completed_at' => Carbon::now(),
          'user_id' => User::_id(),
        ]);

        $this->sendStapMails([
            $stap->id,
            'trigger' => 'STEP_CONFIRM'
        ]);

        Log::info('Stap afgerond'. ($medium ? " via $medium" : '') .'.', [
            'project_id' => $this->id,
            'stap_id' => $stap->id,
        ]);

        //Set the next step
        if($this->stap_id != $next_stap_id){
            $next_stap_id
                ? $this->setStap($next_stap_id)
                : $this->complete();
        }
    }
    public function setStap($stap_id, $reason = null){
        $stap = Stappen::find($stap_id);

        //Clear previous stap completad_at
        ProjectenStappen::where('project_id', $this->id)->each(function($completed_stap) use ($stap){
            if($completed_stap->stap->stapNr() >= $stap->stapNr()){
                $completed_stap->completed_at = null;
                $completed_stap->save();
            }
        });

        //Log stap change
        ProjectenStapChanges::insert([
            'project_id' => $this->id,
            'user_id' => _user()->id,
            'old' => $this->stap_id,
            'new' => $stap->id,
            'reason' => $reason,
        ]);

        //Update project stap
        Projecten::where('id', $this->id)->update([
            'stap_id' => $stap_id,
        ]);

        $this->refresh();
    }
    public function revertStap($stap_id, $reason){
        $stap = Stappen::find($stap_id);
        Log::info("Project teruggezet van {$this->stap->stapnaam} naar {$stap->stapnaam}.", [
            'project_id' => $this->id,
            'data' => [ 'reason' => $reason ]
        ]);


        $this->setStap($stap_id, $reason);
        $this->sendRevertStapMails();
    }
    public function rejectStap($stap_id, $reason = null){
        $current_stap_id = $this->stap_id;

        $this->setStap($stap_id, $reason);
        $this->sendStapMails([
            'stap_id' => $current_stap_id,
            'trigger' => 'STEP_REJECT',
        ]);
    }
    public function fillableSubStappen(){
        return $this->stap->fillableSubStappen($this->id);
    }

    //Mails
    public function sendRevertStapMails(){
        $stap_change = ProjectenStapChanges::where('project_id', $this->id)->orderBy('id', 'DESC')->first();
        $stap = Stappen::find($stap_change->new);
        $emails = $this->stapUsersInvullen($stap->id)->pluck('email')->toArray();

        $subject = "Project {$this->projectnummer} is teruggezet naar {$stap->stapnaam}";
        $content = EmailsTemplates::getContent(EmailsTemplatesEnums::STAP_REVERT, [
            'stap_change' => $stap_change,
            'project' => $this,
            'adres' => $this->adres
        ]);

        //TODO: Remove
        //$emails = ['<EMAIL>'];


        try{
            Mail::to($emails)->send(new Blank([
                'subject' => $subject,
                'content' => $content,
            ]));
            $status = EmailStatus::VERZONDEN;
            $error_message = null;
        }
        catch(\Exception $e){
            $status = EmailStatus::MISLUKT;
            $error_message = $e->getMessage();
        }

        $mail_id = Emails::insertGetId([
            'user_id' => _user()->id,
            'project_id' => $this->id,
            'stap_id' => $stap->id,
            'subject' => $subject,
            'content' => $content,
            'status' => $status,
            'error_message' => $error_message,
        ]);
        $recipients = array_map(function($recipient) use ($mail_id){
            return [
                'mail_id' => $mail_id,
                'email' => $recipient,
            ];
        }, $emails);
        EmailsRecipients::insert($recipients);
    }
    public function sendStapMails($options){
        $trigger = $options['trigger'] ?? 'STEP_CONFIRM';
        $stap = Stappen::find($options['stap_id'] ?? $this->stap_id);


        $mails = InstellingenStappenMails::where(['proces_id' => $this->proces_id, 'stap_id' => $stap->id, 'is_concept' => 0, 'trigger' => $trigger])->get();

        foreach($mails as $mail){
            $user_ids = ProjectenGebruikers::where('project_id', $this->id)->whereIn('role_id', json_decode($mail->ontvangers))->pluck('user_id')->toArray();
            $contact_ids = ProjectenContacten::where('project_id', $this->id)->whereIn('role_id', json_decode($mail->ontvangers))->pluck('contact_id')->toArray();

            $users = Users::whereIn('id', $user_ids)->get();
            $contacten = Contacten::whereIn('id', $contact_ids)->get();

            $emails = array_merge(
                $users->pluck('email')->toArray(),
                $contacten->pluck('email')->toArray(),
            );
            $emails = array_unique($emails);
            $emails = resetIndex($emails);
            $content = EmailsTemplates::setContentData($mail->mail, [
                'project' => $this,
                'adres' => $this->adres,
            ]);

            try{
                Mail::to($emails)->send(new Blank([
                    'subject' => $mail->onderwerp,
                    'content' => $content,
                ]));
                $status = EmailStatus::VERZONDEN;
                $error_message = null;
            }
            catch(\Exception $e){
                $status = EmailStatus::MISLUKT;
                $error_message = $e->getMessage();
            }

            $mail_id = Emails::insertGetId([
                'user_id' => _user()->id,
                'project_id' => $this->id,
                'stap_id' => $stap->id,
                'subject' => $mail->onderwerp,
                'content' => $mail->mail,
                'status' => $status,
                'error_message' => $error_message,
            ]);
            $recipients = array_map(function($recipient) use ($mail_id){
                return [
                    'mail_id' => $mail_id,
                    'email' => $recipient,
                ];
            }, $emails);
            EmailsRecipients::insert($recipients);
        }

    }

    //Stap permissions
    public function isInvulbaar($stap_id = null){
        if(hasPermission('projecten_stappen_invullen_zelfde_rol')){ return $this->isInvulbaarBasedOnRole($stap_id); }

        $stap_id = $stap_id ?? $this->stap_id;
        $users_attr = "_stap_users_invullen_$stap_id";
        $invulbaar_attr = "_is_invulbaar_$stap_id";

        if($this->getCache($invulbaar_attr)){ return $this->getCache($invulbaar_attr); }
        if(!$this->getCache($users_attr)){ $this->stapUsersInvullen($stap_id); }

        $users = $this->getCache($users_attr);
        $is = !!($users->where('id', User::_id())->first());

        if($stap_id == $this->stap_id){
            $this->setCache('is_invulbaar', $is);
        }
        return $this->setCache($invulbaar_attr, $is);
    }
    public function isInvulbaarBasedOnRole($stap_id = null){
        $stap_id = $stap_id ?? $this->stap_id;
        $stap = Stappen::find($stap_id);
        $invulbaar_attr = "is_invulbaar_based_on_role_$stap_id";

        if($this->getCache($invulbaar_attr)){ return $this->getCache($invulbaar_attr); }

        $stappen_ids = array_merge([$stap_id], $stap->subStappen()->pluck('id')->toArray());
        $is = InstellingenStappenGebruikers::whereIn('stap_id', $stappen_ids)->where([
            'role_id' => _user()->role->id,
            'invullen' => 1,
        ])->exists();

        if($stap_id == $this->stap_id){
            $this->setCache('is_invulbaar', $is);
        }
        return $this->setCache($invulbaar_attr, $is);
    }
    public function isInzichtbaar($stap_id = null){
        if(hasPermission('projecten_stappen_inzien_zelfde_rol')){ return $this->isInzichtbaarBasedOnRole($stap_id); }


        $stap_id = $stap_id ?? $this->stap_id;
        $users_attr = "_stap_users_inzien_$stap_id";
        $inzichtbaar_attr = "_is_inzichtbaar_$stap_id";

        if($this->getCache($inzichtbaar_attr)){ return $this->getCache($inzichtbaar_attr); }
        if(!$this->getCache($users_attr)){ $this->stapUsersInzien($stap_id); }

        $is = !!($this->getCache($users_attr)->where('id', User::_id() )->first());
        if(!$is && $this->isInvulbaar($stap_id)){ $is = true; }

        if($stap_id == $this->stap_id){
            $this->setCache('is_inzichtbaar', $is);
        }

        return $this->setCache($inzichtbaar_attr, $is);
    }
    public function isInzichtbaarBasedOnRole($stap_id = null){
        $stap_id = $stap_id ?? $this->stap_id;
        $stap = Stappen::find($stap_id);
        $inzichtbaar_attr = "is_inzichtbaar_based_on_role_$stap_id";

        if($this->getCache($inzichtbaar_attr)){ return $this->getCache($inzichtbaar_attr); }

        $stappen_ids = array_merge([$stap_id], $stap->subStappen()->pluck('id')->toArray());
        $is = InstellingenStappenGebruikers::whereIn('stap_id', $stappen_ids)->where([
            'role_id' => _user()->role->id,
            'inzien' => 1,
        ])->exists();
        if(!$is && $this->isInvulbaarBasedOnRole($stap_id)){ $is = true; }

        if($stap_id == $this->stap_id){
            $this->setCache('is_inzichtbaar', $is);
        }
        return $this->setCache($inzichtbaar_attr, $is);
    }
    public function isStapFillable($stap_id){
        return $this->isStapCurrent($stap_id) && $this->isInvulbaar($stap_id) && $this->actief;
    }

    public function stapRolesInvullen($stap_id = null){
        $stap = Stappen::find($stap_id ?? $this->stap_id);
        $attr = "_stap_roles_invullen_$stap_id";

        if($this->getCache($attr)){
            return $this->getCache($attr);
        }

        $invullen_roles_ids = InstellingenStappenGebruikers::where(['stap_id' => $stap->id, 'invullen' => 1])->pluck('role_id')->toArray();
        $roles = Roles::whereIn('id', $invullen_roles_ids)->get();
        
        foreach($roles as $role){
            $project_user = ProjectenGebruikers::where(['project_id' => $this->id, 'role_id' => $role->id])->first();
            $user = User::find($project_user->user_id ?? null);
            $role->setAttribute('user', $user);
        }

        //Trigger the function for all sub stappen, if the given stap is a parent
        if($stap->hasSubStappen()){
            foreach($stap->subStappen() as $sub_stap){
                $roles = $roles->merge( $this->stapRolesInvullen($sub_stap->id) )->unique('id')->values();
            }
        }

        if($stap->id == $this->stap_id){
            $this->setCache('stap_users_invullen', $roles);
        }

        return $this->setCache($attr, $roles);
    }
    public function stapRolesInzien($stap_id = null){
        $stap_id = $stap_id ?? $this->stap_id;
        $attr = "_stap_roles_inzien_$stap_id";

        if($this->getCache($attr)){
            return $this->getCache($attr);
        }

        $inzien_roles_ids = InstellingenStappenGebruikers::where(['stap_id' => $stap_id, 'inzien' => 1])->pluck('role_id')->toArray();
        $roles = Roles::whereIn('id', $inzien_roles_ids)->get();

        if($stap_id == $this->stap_id){
            $this->setCache('stap_users_invullen', $roles);
        }

        return $this->setCache($attr, $roles);
    }
    public function stapRolesInvullenVisual($stap_id = null){
        $stap = Stappen::find($stap_id ?? $this->stap_id);
        $attr = "_stap_roles_invullen_visual_{$stap->id}";

        //Return from cache if possible
        if($this->getCache($attr)){
            return $this->getCache($attr);
        }

        //Hide roles which have permission to be hidden, Only if there are any other users available to display
        $roles = $this->stapRolesInvullen($stap->id);
        if($roles->count() > 1){
            $roles = $roles->filter(function($role){
                $can_be_hidden = roleHasPermission('projecten_invullen_hide_user', $role->id);
                return !$can_be_hidden;
            })->values();
        }

        //Set geenric cache if it's the current stap
        if($stap->id == $this->stap_id || $stap->sub_stap_of == $this->stap_id){
            $this->setCache('stap_roles_invullen_visual', $roles);
        }

        return $this->setCache($attr, $roles);
    }

    public function stapUsersInzien($stap_id = null){ //Find users that can fill the given stap
        $stap = Stappen::findOrFail($stap_id ?? $this->stap_id);
        $attr = "_stap_users_inzien_{$stap->id}";

        //Return from cache if possible

        if($this->getCache($attr)){
            return $this->getCache($attr);
        }

        //Set users that can fill the given stap
        $invullen_roles_ids = InstellingenStappenGebruikers::where(['stap_id' => $stap->id, 'inzien' => 1])->pluck('role_id')->toArray();
        $selected_users = ProjectenGebruikers::where('project_id', $this->id)->whereIn('role_id', $invullen_roles_ids)->pluck('user_id')->toArray();
        $users = User::whereIn('id', $selected_users)->with('role', 'bedrijf')->get();

        //Trigger the function for all sub stappen, if the given stap is a parent
        if($stap->hasSubStappen()){
            foreach($stap->subStappen() as $sub_stap){
                $users = $users->merge( $this->stapUsersInvullen($sub_stap->id) )->unique('id')->values();
            }
        }

        //Set generic cache attribute if it's the current stap
        if($stap->id == $this->stap_id || $stap->sub_stap_of == $this->stap_id){
            $this->setCache('stap_users_inzien', $users);
        }

        return $this->setCache($attr, $users);
    }
    public function stapUsersInvullen($stap_id = null){ //Find users that can fill the given stap
        $stap = Stappen::findOrFail($stap_id ?? $this->stap_id);
        $attr = "_stap_users_invullen_{$stap->id}";

        //Return from cache if possible
        if($this->getCache($attr)){
            return $this->getCache($attr);
        }

        //Set users that can fill the given stap
        $invullen_roles_ids = InstellingenStappenGebruikers::where(['stap_id' => $stap->id, 'invullen' => 1])->pluck('role_id')->toArray();
        $selected_users = ProjectenGebruikers::where('project_id', $this->id)->whereIn('role_id', $invullen_roles_ids)->pluck('user_id')->toArray();
        $users = User::whereIn('id', $selected_users)->with('role', 'bedrijf')->get();

        //Trigger the function for all sub stappen, if the given stap is a parent
        if($stap->hasSubStappen()){
            foreach($stap->subStappen() as $sub_stap){
                $users = $users->merge( $this->stapUsersInvullen($sub_stap->id) )->unique('id')->values();
            }
        }

        //Set generic cache attribute if it's the current stap
        if($stap->id == $this->stap_id || $stap->sub_stap_of == $this->stap_id){
            $this->setCache('stap_users_invullen', $users);
        }

        return $this->setCache($attr, $users);
    }
    public function stapUsersInvullenVisual($stap_id = null){
        $stap = Stappen::find($stap_id ?? $this->stap_id);
        $attr = "_stap_users_invullen_visual_{$stap->id}";

        //Return from cache if possible
        if($this->getCache($attr)){
            return $this->getCache($attr);
        }

        //Hide users which have permission to be hidden, Only if there are any other users available to display
        $users = $this->stapUsersInvullen($stap->id);
        if($users->count() > 1){
            $users = $users->filter(function($user){
                $can_be_hidden = hasPermission('projecten_invullen_hide_user', $user->id);
                return !$can_be_hidden;
            })->values();
        }

        //Set geenric cache if it's the current stap
        if($stap->id == $this->stap_id || $stap->sub_stap_of == $this->stap_id){
            $this->setCache('stap_users_invullen_visual', $users);
        }

        return $this->setCache($attr, $users);
    }

    //Planmatige projecten
    public function planmatig(){
        $this->setCache('is_planmatig', $this->isPlanmatig());
        $this->planmatigeAdressen();
        $this->planmatigeProjecten();
        $this->planmatigeProjectenTotalCount();
    }
    public function isPlanmatig(){
        return !!$this->planmatig_key;
    }
    public function planmatigeProjecten($options = []){
        if($this->getCache('planmatige_projecten')){ return $this->getCache('planmatige_projecten'); }

        $query = [
            'planmatig_key' => $this->planmatig_key,
            'stap_id' => $this->stap_id,
            'status' => $this->status,
        ];

        $projecten =  Projecten::where($query)->get();

        return $this->setCache('planmatige_projecten', $projecten);
    }
    public function planmatigeAdressen($options = []){
        $include_project = $options['include_project'] ?? ['id']; // Append project id by default

        if($this->getCache('planmatige_adressen')){  return $this->getCache('planmatige_adressen'); }

        $adressen = $this->planmatigeProjecten($options)->map(function($project) use ($include_project) {
            $adres = $project->adres;

            $adres->address_line = $adres->addressLine();

            if($include_project){
                $project_data = [];
                foreach($include_project as $project_key){
                    $project_data[$project_key] = $project[$project_key];
                }
                $adres->project = $project_data;
            }

            return $adres;
        });

        return $this->setCache('planmatige_adressen', $adressen);
    }
    public function planmatigeProjectenTotalCount(){
        if($this->getCache('planmatige_projecten_total_count')){ return $this->getCache('planmatige_projecten_total_count'); }

        $count = Projecten::where('planmatig_key', $this->planmatig_key)->count();
        return $this->setCache('planmatige_projecten_total_count', $count);
    }

    //Status
    public function setStatus($status){
        ProjectenStatusChanges::insert([
            'project_id' => $this->id,
            'user_id' => _user()->id,
            'old' => $this->status,
            'new' => $status,
        ]);

        Projecten::where('id', $this->id)->update([
            'status' => $status,
            'actief' => $status == ProjectStatus::LOPEND,
            'completed_at' => ($status == ProjectStatus::AFGEROND) ? Carbon::now() : null,
        ]);

        Log::info('Project status gewijzigd naar '. ucfirst(strtolower($status)).'.', [
            'project_id' => $this->id,
            'stap_id' => $this->stap_id,
        ]);

        $this->refresh();
    }
    public function status(){
        return $this->setCache('status_data', ProjectStatus::get($this->status));
    }

    //Files
    public function getFile($key){
        $id = ProjectenFiles::where(['project_id' => $this->id, 'file_key' => $key])->value('file_id');
        return Files::find($id);
    }
    public function getFiles($key){
        $ids = ProjectenFiles::where(['project_id' => $this->id, 'file_key' => $key])->pluck('file_id')->toArray();
        return Files::whereIn('id', $ids)->get();
    }
    public function storeBase64File($file_key, $name, $base64){
        if(!$base64){ return; }

        $file = Files::storeBase64File([
            'base64' => $base64,
            'name' => $name,
            'path' => "/{$file_key}"
        ]);
        if(!$file){ return; }

        ProjectenFiles::insert([
            'project_id' => $this->id,
            'file_id' => $file->id,
            'file_key' => $file_key,
        ]);

    }

    //Koppelingen
    public function stapApis($stap_id = null, $planmatig = null, $type = null){
        $stap_id = $stap_id ?? $this->stap_id;
        $user_ids = $this->projectGebruikers()->pluck('id')->toArray();

        $query = StappenApi::where([
            'domain_id' => _domain()->id,
            'stap_id' => $stap_id,
        ])->whereIn('user_id', $user_ids);

        if($planmatig !== null){
            $query = $query->where('planmatig', $planmatig ? 1 : 0);
        }
        if($type !== null){
            $query = $query->where('type', $type);
        }

        return $query->get();
    }

    //Opdrachtformulier
    public function opdrachtformulierUrl(){
        if($this->opdrachtformulier){
            return url("dashboard/projecten/opdrachtformulier/{$this->opdrachtformulier->guid}");
        }

        return $this->getFiles('opdrachtformulier');
    }

}
