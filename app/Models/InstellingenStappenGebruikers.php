<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InstellingenStappenGebruikers extends Model
{
    use HasFactory;

    protected $connection = 'client_db';
    protected $table = 'instellingen_stappen_gebruikers';
    
    
    //Realtions
    public function role(){
        return $this->hasOne(Roles::class, 'id', 'role_id');
    }
    public function stap(){
        return $this->hasOne(Stappen::class, 'id', 'stap_id');
    }
    public function instellingen_stap(){
        return $this->hasOne(InstellingenStappen::class, 'stap_id', 'stap_id');
    }
    
}
