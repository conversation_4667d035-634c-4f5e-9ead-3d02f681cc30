<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class Blank extends Mailable{
	use Queueable, SerializesModels;
	
	public $subject;
	public $content;
	
	public function __construct($options){
		$this->subject = $options['subject'];
		$this->content = $options['content'];
	}
	
	public function build(){
		$this->subject($this->subject);
		$this->view('mails.blank', [
			'content' => $this->content,
		]);
		
		return $this;
	}
}
