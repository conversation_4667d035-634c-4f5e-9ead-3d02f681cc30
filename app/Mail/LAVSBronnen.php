<?php

namespace App\Mail;

use App\Models\Projecten;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class LAVSBronnen extends Mailable
{
    use Queueable, SerializesModels;

    private Projecten $project;

    public function __construct($project){
        $this->project = $project;
    }

    public function build(){
        $this->subject("Welke bronnen zijn gesaneerd? Project {$this->project->projectnummer}");

        return $this->view('mails.lavs_bronnen', [
            'project' => $this->project,
        ]);
    }
}
