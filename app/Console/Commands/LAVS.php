<?php

namespace App\Console\Commands;

use App\Models\Domeinen;
use App\Models\Projecten;
use App\Models\ProjectenAsbestSanering;
use App\Models\Stappen;
use Illuminate\Console\Command;

use App\Classes\koppelingen\LAVS as LAVSAPI;

class LAVS extends Command{
    protected $signature = 'lavs:run';

    public function __construct(){
        parent::__construct();
    }
    public function handle(){
        $domains = Domeinen::whereNotNull(['lavs_entity_concerned_id', 'lavs_entity_concerned_id'])->get();
        foreach($domains as $domain){
            $domain->setConfig();
            $domain->database->setConfig();
            $this->handleDomain($domain);
        }
    }

    private function handleDomain($domain){
        $aannemen_sanering_id = Stappen::getID('asbest_project', 'aannemen_sanering');
        $sanering_opleveren_id = Stappen::getID('asbest_project', 'sanering_opleveren');
        $stortbon_id = Stappen::getID('asbest_project', 'stortbon');

        $project = Projecten::whereIn('stap_id', [ $aannemen_sanering_id, $sanering_opleveren_id, $stortbon_id ])->whereHas('asbest_sanering', function($query){
            $query->whereNotNull('lavs_project_id')->where(function ($q) {
                    $q->where('lavs_last_check', '<', date('Y-m-d'))->orWhereNull('lavs_last_check');
            });
        })->first();
        if(!$project){ return; }

        ProjectenAsbestSanering::where('id', $project->asbest_sanering->id)->update(['lavs_last_check' => date('Y-m-d')]);
        $lavs = new LAVSAPI($project->id, $domain->lavs_entity_concerned_id, $domain->lavs_entity_concerned_sub_id);

        switch($project->stap_id){
            case $aannemen_sanering_id: $lavs->handleAannemenSanering(); break;
            case $sanering_opleveren_id: $lavs->handleSaneringopleveren(); break;
            case $stortbon_id: $lavs->handleStortbon(); break;
        }
    }

}
