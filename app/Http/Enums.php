<?php

//Environment
class PriceType{
    public const INCL = 'INCL';
    public const EXCL = 'EXCL';
    public const BTW = 'BTW';
}
class ExportType{
    public const EXCEL = 'EXCEL';
}

//Adressen
class AdresStatus{
    public const WONING_GEINVENTARISEERD = 'WONING_GEINVENTARISEERD';
    public const WONING_NIET_GEINVENTARISEERD = 'WONING_NIET_GEINVENTARISEERD';

    public const LEGENDA = [
        [
            'key' => self::WONING_GEINVENTARISEERD,
            'color' => '#ffbf00',
            'description' => 'Woning geïnventariseerd'
        ],
        [
            'key' => self::WONING_NIET_GEINVENTARISEERD,
            'color' => '#F57F17',
            'description' => 'Woning nog niet geïnventariseerd'
        ],
    ];

    public static function get($key, $attribute = null){
        foreach(self::LEGENDA as $row){
            if($key == $row['key']){
                if(!$attribute){ return $row; }
                return $row[$attribute] ?? null;
            }
        }
        return null;
    }

    public static function color($key){
        return self::get($key, 'color');
    }
    public static function description($key){
        return self::get($key, 'description');
    }

}

//Asbest Bronnen
class AsbestBronStatus{
    public const NIET_AANGETROFFEN = 'NIET_AANGETROFFEN';
    public const AANGETROFFEN_NIET_GESANEERD = 'AANGETROFFEN_NIET_GESANEERD';
    public const GESANEERD = 'GESANEERD';
    public const ASBESTVRIJ = 'ASBESTVRIJ';
    public const REFERENTIE = 'REFERENTIE';
    public const REFERENTIE_ASBESTVRIJ = 'REFERENTIE_ASBESTVRIJ';
    public const INDICATIE = 'INDICATIE';

    public const LEGENDA = [
        [
            'key' => self::NIET_AANGETROFFEN,
            'Title' => 'Niet aangetroffen',
            'color' => '#cbd7dd',
            'description' => 'Bron niet aangetroffen op locatie'
        ],
        [
            'key' => self::AANGETROFFEN_NIET_GESANEERD,
            'title' => 'Niet gesaneerd',
            'color' => '#D32F2F',
            'description' => 'Asbesthoudende bron aangetroffen op locatie én niet gesaneerd'
        ],
        [
            'key' => self::ASBESTVRIJ,
            'title' => 'Asbestvrij',
            'color' => '#6ddb71',
            'description' => 'Niet-Asbesthoudende Bron aangetroffen op locatie'
        ],
        [
            'key' => self::GESANEERD,
            'title' => 'Gesaneerd',
            'color' => '#4CAF50',
            'description' => 'Bron is gesaneerd'
        ],
        [
            'key' => self::REFERENTIE,
            'title' => 'Referentie',
            'color' => '#637cd8',
            'description' => 'Referentiebron: door inventariseerder aangemerkt als referentiebron'
        ],
        [
            'key' => self::REFERENTIE_ASBESTVRIJ,
            'title' => 'Asbestvrije referentie',
            'color' => '#aae4ac',
            'description' => 'Niet-Asbesthoudende Referentiebron'
        ],
        [
            'key' => self::INDICATIE,
            'title' => 'Indicatie',
            'color' => '#93c4e0',
            'description' => 'Indicatiebron: aangetroffen asbesthoudende bron op ander adres, geen Z.A.V.'
        ]
    ];

    public static function get($key, $attribute){
        foreach(self::LEGENDA as $row){
            if($key == $row['key']){ return $row[$attribute] ?? null; }
        }
        return null;
    }

    public static function color($key){
        return self::get($key, 'color');
    }
    public static function description($key){
        return self::get($key, 'description');
    }



}

//Processen
class ProcesType{
    public const ASBEST = 'ASBEST';
    public const CHROOM6 = 'CHROOM6';

    public const LEGENDA = [
        [
            'key' => self::ASBEST,
            'color' => '#458052',
            'title' => 'Asbest'
        ],
        [
            'key' => self::CHROOM6,
            'color' => '#7B4B55',
            'title' => 'Chroom-6'
        ]
    ];

    public static function get($key, $attribute = null){
        foreach(self::LEGENDA as $row){
            if($key == $row['key']){
                if(!$attribute){ return $row; }
                return $row[$attribute] ?? null;
            }
        }
        return null;
    }
}

//Stappen
class ProjectStapType{
    public const COMPLETED = 'COMPLETED';
    public const SKIPPED = 'SKIPPED';
    public const OPEN = 'OPEN';
    public const CURRENT = 'CURRENT';
    public const CURRENT_SUB = 'CURRENT_SUB';
}
class ProjectStapContext{
    public const PROJECT = 'project';
    public const PROJECT_STAP = 'project_stap';
    public const ADRES = 'adres';
    public const BEWONER = 'bewoner';
    public const PROJECTEN_FILES = 'projecten_files';
    public const ASBEST_BRONNEN = 'asbest_bronnen';
    public const ASBEST_SANERING = 'asbest_sanering';
    public const REFERENTIE_BRONNEN = 'referentie_bronnen';
    public const HISTORISCHE_BRONNEN = 'historische_bronnen';
    public const OPDRACHTFORMULIER = 'opdrachtformulier';
    public const PLANMATIGE_ASBEST_BRONNEN = 'planmatige_asbest_bronnen';

    public const LEGENDA = [
        [
            'key' => self::PROJECT,
            'label' => 'Project'
        ],
        [
            'key' => self::PROJECT_STAP,
            'label' => 'Project Stap'
        ],
        [
            'key' => self::ADRES,
            'label' => 'Adres'
        ],
        [
            'key' => self::BEWONER,
            'label' => 'Bewoner'
        ],
        [
            'key' => self::PROJECTEN_FILES,
            'label' => 'Bestanden'
        ],
        [
            'key' => self::ASBEST_BRONNEN,
            'label' => 'Asbest Bronnen'
        ],
        [
            'key' => self::ASBEST_SANERING,
            'label' => 'Asbest Sanering'
        ],
        [
            'key' => self::REFERENTIE_BRONNEN,
            'label' => 'Asbest Referentie Bronnen'
        ],
        [
            'key' => self::HISTORISCHE_BRONNEN,
            'label' => 'Asbest Historische Bronnen'
        ],
        [
            'key' => self::OPDRACHTFORMULIER,
            'label' => 'Opdrachtformulier'
        ],
        [
            'key' => self::PLANMATIGE_ASBEST_BRONNEN,
            'label' => 'Planmatige Asbest Bronnen'
        ],
    ];

    public static function get($key, $attribute = null){
        foreach(self::LEGENDA as $row){
            if($key == $row['key']){
                if(!$attribute){ return $row; }
                return $row[$attribute] ?? null;
            }
        }
        return null;
    }

}

//Projecten
class ProjectStatus{
    public const ACTIELIJST = 'ACTIELIJST';
    public const ACTIELIJST_BY_ROLE = 'ACTIELIJST_BY_ROLE';
    public const LOPEND = 'LOPEND';
    public const GEPARKEERD = 'GEPARKEERD';
    public const GEANNULEERD = 'GEANNULEERD';
    public const AFGEROND = 'AFGEROND';
    public const ONBEKEND = 'ONBEKEND';

    public const LEGENDA = [
        [
            'key' => self::LOPEND,
            'color' => '#1E90FF',
        ],
        [
            'key' => self::GEPARKEERD,
            'color' => '#FFA500',
        ],
        [
            'key' => self::GEANNULEERD,
            'color' => '#dc3545',
        ],
        [
            'key' => self::AFGEROND,
            'color' => '#0bc066',
        ],
        [
            'key' => self::ONBEKEND,
            'color' => '#6c757d',
        ],
    ];

    public static function get($key, $attribute = null){
        foreach(self::LEGENDA as $row){
            if($key == $row['key']){
                if(!$attribute){ return $row; }
                return $row[$attribute] ?? null;
            }
        }

        return ProjectStatus::get(ProjectStatus::ONBEKEND);
    }

}

//Mails
class EmailsTemplates{

    public const STAP_REVERT = 'STAP_REVERT';
    public const TODO = 'TODO';

    public const LEGENDA = [
        [
            'key' => self::STAP_REVERT,
            'name' => 'Stap Terugzetten',
        ],
        [
            'key' => self::TODO,
            'name' => 'TODO',
        ]
    ];

    public static function get($key, $attribute = null){
        foreach(self::LEGENDA as $row){
            if($key == $row['key']){
                if(!$attribute){ return $row; }
                return $row[$attribute] ?? null;
            }
        }

        return null;
    }

}
class EmailsKeywords{

    //Project
    public const PROJECT_PROJECTNUMMER = 'PROJECT_PROJECTNUMMER';
    public const PROJECT_CREATED_AT = 'PROJECT_CREATED_AT';
    public const PROJECT_OPDRACHTNUMMER = 'PROJECT_OPDRACHTNUMMER';
    public const PROJECT_SOORT = 'PROJECT_SOORT';

    //Adres
    public const ADRES_STRAAT = 'ADRES_STRAAT';
    public const ADRES_HUISNUMMER = 'ADRES_HUISNUMMER';
    public const ADRES_TOEVOEGING = 'ADRES_TOEVOEGING';
    public const ADRES_POSTCODE = 'ADRES_POSTCODE';
    public const ADRES_PLAATS = 'ADRES_PLAATS';
    public const ADRES_VHE = 'ADRES_VHE';
    public const ADRES_COMPLEX = 'ADRES_COMPLEX';

    //Stap
    public const STAP_CHANGE_REASON = 'STAP_CHANGE_REASON';

    //Regisseur
    public const REGISSEUR_NAAM = 'REGISSEUR_NAAM';
    public const REGISSEUR_EMAIL = 'REGISSEUR_EMAIL';
    public const REGISSEUR_TELEFOON = 'REGISSEUR_TELEFOON';
    public const REGISSEUR_LOGO = 'REGISSEUR_LOGO';

    public const LEGENDA = [
        // Project
        [
            'key' => self::PROJECT_PROJECTNUMMER,
            'name' => 'Project Projectnummer',
        ],
        [
            'key' => self::PROJECT_CREATED_AT,
            'name' => 'Project Aanmaakdatum',
        ],
        [
            'key' => self::PROJECT_OPDRACHTNUMMER,
            'name' => 'Project Opdrachtnummer',
        ],
        [
            'key' => self::PROJECT_SOORT,
            'name' => 'Project soort',
        ],

        //Adres
        [
            'key' => self::ADRES_STRAAT,
            'name' => 'Adres Straat',
        ],
        [
            'key' => self::ADRES_HUISNUMMER,
            'name' => 'Adres Huisnummer',
        ],
        [
            'key' => self::ADRES_TOEVOEGING,
            'name' => 'Adres Toevoeging',
        ],
        [
            'key' => self::ADRES_POSTCODE,
            'name' => 'Adres Postcode',
        ],
        [
            'key' => self::ADRES_PLAATS,
            'name' => 'Adres Plaats',
        ],
        [
            'key' => self::ADRES_VHE,
            'name' => 'Adres VHEnummer',
        ],
        [
            'key' => self::ADRES_COMPLEX,
            'name' => 'Adres Complexnummer',
        ],

        //Stap
        [
            'key' => self::STAP_CHANGE_REASON,
            'name' => 'Stap Reden Van terugzetting',
        ],

        //Regisseur
        [
            'key' => self::REGISSEUR_NAAM,
            'name' => 'Regisseur Naam',
        ],
        [
            'key' => self::REGISSEUR_EMAIL,
            'name' => 'Regisseur Email',
        ],
        [
            'key' => self::REGISSEUR_TELEFOON,
            'name' => 'Regisseur Telefoon',
        ],
        [
            'key' => self::REGISSEUR_LOGO,
            'name' => 'Regisseur Logo',
        ],
    ];

    public static function get($key, $attribute = null){
        foreach(self::LEGENDA as $row){
            if($key == $row['key']){
                if(!$attribute){ return $row; }
                return $row[$attribute] ?? null;
            }
        }

        return null;
    }

}
class EmailStatus{
    public const VERZONDEN = 'VERZONDEN';
    public const MISLUKT = 'MISLUKT';
    public const ONBEKEND = 'ONBEKEND';

    public const LEGENDA = [
        [
            'key' => self::VERZONDEN,
            'color' => '#0bc066',
        ],
        [
            'key' => self::MISLUKT,
            'color' => '#dc3545',
        ],
        [
            'key' => self::ONBEKEND,
            'color' => '#6c757d',
        ],
    ];

    public static function get($key, $attribute = null){
        foreach(self::LEGENDA as $row){
            if($key == $row['key']){
                if(!$attribute){ return $row; }
                return $row[$attribute] ?? null;
            }
        }

        return EmailStatus::get(EmailStatus::ONBEKEND);
    }

}

//StappenApis
class StappenApiType{
    public const INBOUND = 'inbound';
    public const OUTBOUND = 'outbound';
}
