<?php

namespace App\Http\Controllers;

use App\Models\Bedrijven;
use App\Models\Files;
use Illuminate\Http\Request;

class BedrijvenController extends Api\BedrijvenController{
    
    public function index(){
        return view('bedrijven.index');
    }
    public function bedrijf($id = null){
        $bedrijf = $id
          ? Bedrijven::findOrFail($id)
          : new Bedrijven();
        
        return view('bedrijven.bedrijf', [
          'bedrijf' => $bedrijf
        ]);
    }
    
}
