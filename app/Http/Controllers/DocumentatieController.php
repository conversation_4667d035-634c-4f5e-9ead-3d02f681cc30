<?php


namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

use DB;
use Config;
use Closure;
use Carbon\Carbon;

use App\Models\User;
use App\Models\Domeinen;
use App\Models\DomeinKoppel;
use App\Models\Adressen;
use App\Models\Projecten;
use App\Models\AsbestBronnen;
use App\Models\ProjectenBronomschrijvingen;
use App\Models\ProjectenBronlocaties;
use App\Models\ProjectenRisicoklassen;
use App\Models\Documenten;



class DocumentatieController extends Controller
{
    public function index(){
       
            $documentatie = Documenten::where('project_id', 0)->where('adres_id', 0)->where('actief', 1)->orderBy('soort', 'ASC')->get();
            
       
        return view('documentatie', [ 'documentatie' => $documentatie ]);
    }

    public function storefile(Request $request){
        // $request->validate([
        //     'file' => 'required|mimes:pdf|max:2048',
        // ]);
        // $file = $request->file('file');
        // $name = time().'.'.$file->extension();
        // $file->move(public_path('files'), $name);

        if ($request->hasFile('file')) {
            $uploadedFile = $request->file('file');
            $filename = time().$uploadedFile->getClientOriginalName();

            Storage::disk('public_uploads')->putFileAs( 'logos/', $uploadedFile, $filename );

            $bestand = env('APP_URL').'/uploads/logos/'.$filename;
        } else {
            $bestand = '';            
        }

        ProjectenBronomschrijvingen::insert(
            [ 
                'bestand' => isset($request->bestand) ? $request->bestand : null, 
                'naam' => isset($request->naam) ? $request->naam : '', 
                'soort' => isset($request->soort) ? $request->soort : '', 
                'adreid' => '0', 
                'project_id' => '0',
                'actief' => 1, 
                'created_at' => Carbon::now() 
            ]
        );


        return back()
            ->with('success','You have successfully upload file.')
            ->with('file',$name);
    }
}
