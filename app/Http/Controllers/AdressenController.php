<?php

namespace App\Http\Controllers;

use App\Models\AsbestBronnenReferenties;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

use DB;
use PDF;
use Config;
use Carbon\Carbon;
use App\Classes\Helpers;
use App\Models\Adressen;
use App\Models\Projecten;
use App\Models\AsbestBronnen;
use App\Models\Log;

class AdressenController extends Api\AdressenController{

    public function index(){
		return view('adressen.index');
	}
	public function adres($id){
		$adres = Adressen::where('id', $id)->with('documenten')->firstOrFail();
		$projecten = Projecten::where('adres_id', $adres->id)->with('stap', 'proces')->get();
		$bronnen = AsbestBronnen::where(['adres_id' => $adres->id])->get();

		return view('adressen.adres', [
		  'adres' => $adres ?? [],
		  'projecten' => $projecten ?? [],
		  'bronnen' => $bronnen ?? [],
		]);
	}

    //Meterkastkaart
    public function meterkastkaart($adres_id){
        $adres = Adressen::findOrFail($adres_id);
        $pdf = $this->meterkastkaartPDF($adres->id);

        return $pdf->stream();
    }
    public function meterkastkaartPDF($adres_id){
        $adres = Adressen::findOrFail($adres_id);

        $blade = 'pdf.adressen.meterkastkaart.domain_' . _domain()->id;
        if(!bladeExists($blade)){
            $blade = 'pdf.adressen.meterkastkaart.default';
        }


        $data = [
            "adres" => $adres,
        ];

        $pdf = PDF::loadView($blade, $data);
        $data['pages'] = getPDFPages($pdf);

        return PDF::loadView($blade, $data);
    }

    //QR
    public function qrkaart($adres_id){
        $adres = Adressen::findOrFail($adres_id);
        $pdf = $this->qrkaartPDF($adres->id);

        return $pdf->stream();
    }
    public function qrkaartPDF($adres_id){
        $adres = Adressen::findOrFail($adres_id);

        $blade = 'pdf.adressen.qrkaart.domain_' . _domain()->id;
        if(!bladeExists($blade)){
            $blade = 'pdf.adressen.qrkaart.default';
        }


        $data = [
            "adres" => $adres,
        ];

        $pdf = PDF::loadView($blade, $data);
        $data['pages'] = getPDFPages($pdf);

        return PDF::loadView($blade, $data);
    }


    // Old
	public function new(){
		return view('adressen.new', []);
	}
	public function store(Request $request){
		Adressen::insert(
		  [
			'straat' => isset($request->straat) ? $request->straat : '',
			'huisnummer' => isset($request->huisnummer) ? $request->huisnummer : '',
			'toevoeging' => isset($request->toevoeging) ? $request->toevoeging : '',
			'vhenummer' => isset($request->vhenummer) ? $request->vhenummer : '',
			'complexnummer' => isset($request->complexnummer) ? $request->complexnummer : '',
			'subcomplexnummer' => isset($request->subcomplexnummer) ? $request->subcomplexnummer : '',
			'postcode' => isset($request->postcode) ? $request->postcode : '',
			'plaats' => isset($request->plaats) ? $request->plaats : '',
			'bouwjaar' => isset($request->bouwjaar) ? $request->bouwjaar : '',
			'wijk' => isset($request->wijk) ? $request->wijk : '',
			'buurt' => isset($request->buurt) ? $request->buurt : '',
			'woningtype' => isset($request->woningtype) ? $request->woningtype : '',
			'opmerkingen' => isset($request->opmerkingen) ? $request->opmerkingen : '',
			'actief' => 1,
			'created_at' => Carbon::now()
		  ]
		);
		Log::insert(
		  [
			'actie' => "Heeft een nieuw adres gemaakt genaamd " . $request->straat . " " . $request->huisnummer . ".",
			  //'project_id' => " ",
			'user_id' => Auth::user()->id,
			'created_at' => Carbon::now(),
		  ]
		);
		return redirect('dashboard/adressen')->with('status', 'Adres is toegevoegd.');
		
	}


	
	
}
