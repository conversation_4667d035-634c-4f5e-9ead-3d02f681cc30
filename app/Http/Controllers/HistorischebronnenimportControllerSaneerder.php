<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use DB;
use Config;


use App\Models\Adressen;
use App\Models\TempHistBronnenSaneerder;


class HistorischebronnenimportControllerSaneerder extends Controller
{
    public function index()
    {
        return view('historischebronnenimport.index', []);
    }

    public function get100Bronnensaneerder(Request $request){
        $i = $request->i;
        $start = $i * 100;
        if($i > 0){
            $updatestart = ($i - 1) * 100;
            $updateend = $updatestart + 100;
            $array = TempHistBronnenSaneerder::join('adressen', 'adressen.id', '=', 'temp_hist_bronnen_saneerder.adres_id')->where('temp_hist_bronntemp_hist_bronnen_saneerderen_inventariseerder.actief', 1)->where('adreskopieklaar', 0)->orderBy('adressen.straat')->orderBy('adressen.huisnummer')->orderBy('adressen.toevoeging')->offset($updatestart)->limit(100)->pluck('temp_hist_bronnen_saneerder.id')->toArray();
            TempHistBronnenSaneerder::whereIn('id', $array)->update([
                'adreskopieklaar' => 1
            ]);
        }
        
        $bronnen = TempHistBronnenSaneerder::join('adressen', 'adressen.id', '=', 'temp_hist_bronnen_saneerder.adres_id')->where('temp_hist_bronnen_saneerder.actief', 1)->where('adreskopieklaar', 0)->orderBy('adressen.straat')->orderBy('adressen.huisnummer')->orderBy('adressen.toevoeging')->offset($start)->limit(100)->get();

        return response()->json(
            [
                'success' => true,
                'i' => $i,
                'bronnen' => $bronnen
            ]
        );
    }

    public function getcomplexadressen($complexnummer){
        // Verkrijgen adressen van geselecteerd complexnummer
        $complexadressen = Adressen::where('complexnummer', $complexnummer)->get();
        
        return response()->json(
            [
                'adressen' => $complexadressen
            ]  
        );
    }

}
