<?php

namespace App\Http\Controllers;

use App\Classes\stap\StapInvullen;
use App\Models\Adressen;
use App\Models\Processen;
use App\Models\Projecten;
use App\Models\ProjectenBewoners;
use App\Models\ProjectenOpdrachtformulieren;
use App\Models\Stappen;
use Log;
use PDF;
use ProjectStapType;


class ProjectenController extends Api\ProjectenController{

    public function index(){
        return view('projecten.index');
    }

    public function create($proces_id){
        $proces = Processen::findOrFail($proces_id);
        $manager = new StapInvullen([
            'stap' => $proces->firstStap()->id,
            'proces' => $proces_id,
            'planmatig' => _getAssert('planmatig', 'true'),
        ]);

        return view('projecten.create', [
            'manager' => $manager,
        ]);
    }
    public function stap($project_id){
        $project = Projecten::findOrFail($project_id);
        $adres = Adressen::findOrFail($project->adres_id);
        $bewoner = ProjectenBewoners::where('project_id', $project->id)->firstOrFail();
        $stap = Stappen::findOrFail(_get('stap', $project->stap_id));

        //Verify the stap is not pas the current project stap
        if($project->stapStatus($stap->id) == ProjectStapType::OPEN){
            $stap = Stappen::findOrFail($project->stap_id);
        }

        //Set sub stap if it's a parent stap
        if($stap->hasSubStappen()){
            //Verfiy if sub stap is set correcly
            if(!$stap->activeSubStappen()->count()){
                return redirectHome()->with('danger', "Sub stappen van {$stap->stapnaam} zijn niet goed geconfigureerd.");
            }

            //Check if there is a sub stap meant for the user
            //If not, set the first sub stap of the main stap
            $stap = $project->fillableSubStappen()->first() ?? $stap->activeSubStappen()->first();
        }

        if(!$project->isInzichtbaar($stap->id)){
            Log::warning("Project toegang geweigerd", [
                'project_id' => $project->id,
                'stap_id' => $stap->id,
            ]);
            return redirectUnauthorized();
        }

        $manager = new StapInvullen([
            'stap' => $stap->id,
            'proces' => $project->proces_id,
            'planmatig' => $project->isPlanmatig(),
        ]);
        $invulbaar = $project->isStapFillable($stap->id);

        Log::info('Project geopend.', [
            'project_id' => $project->id,
            'stap_id' => $stap->id,
        ]);

        return view('projecten.stap', [
            'project' => $project,
            'adres' => $adres,
            'bewoner' => $bewoner,
            'manager' => $manager,
            'invulbaar' => $invulbaar,
        ]);
    }

    //Opdrachtformulier
    public function opdrachtformulier($guid){
        $opdrachtofmrulier = ProjectenOpdrachtformulieren::where('guid', $guid)->firstOrFail();
        $pdf = $this->opdrachtformulierPDF($guid);

        return $pdf->stream("Opdrachtformulier {$opdrachtofmrulier->project->projectnummer}");
    }
    private function opdrachtformulierPDF($guid){
        $formulier = ProjectenOpdrachtformulieren::where('guid', $guid)->firstOrFail();

        $blade = 'pdf.projecten.opdrachtformulier.domain_' . _domain()->id;
        if(!bladeExists($blade)){
            $blade = 'pdf.projecten.opdrachtformulier.default';
        }

        $data = [
            "formulier" => $formulier,
            "project" => $formulier->project,
            "adres" => $formulier->project->adres,
            "bronnen" => getBronOmschrijvingen(['where' => ['opdrachtformulier' => 1]]),
        ];

        $pdf = PDF::loadView($blade, $data);
        $data['pages'] = getPDFPages($pdf);

        return PDF::loadView($blade, $data);
    }


}
