<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use Illuminate\Support\Facades\Auth;

use App\Models\User;

use Carbon\Carbon;

class HomeController extends Controller{
    public function __construct(){
        $this->middleware('auth');
    }
    
    public function home(){
        return redirectHome();
    }
    public function welcome(){
        return view('welcome');
    }

    public function switch(Request $request, $id){
        $domain_id = 0;
        $domeinen = $request->attributes->get('domeinen');
        foreach($domeinen as $domein){
            if($domein->id == $id){
                $domain_id = $domein->id;
            }
        }
        if($domain_id !== 0){
            User::where('id', Auth::id())->update([
              'active_domain' => $id,
              'updated_at' => Carbon::now(),
            ]);
        }
        return redirect()->route('home', []);
    }
}
