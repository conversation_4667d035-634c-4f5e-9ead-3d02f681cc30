<?php

namespace App\Http\Controllers;

use App\Classes\stap\StapInstellingen;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

use App\Models\User;
use App\Models\DomeinKoppel;
use App\Models\Processen;
use App\Models\Stappen;
use App\Models\Contacten;
use App\Models\InstellingenProcessen;
use App\Models\InstellingenStappen;
use App\Models\ProjectenRedenen;
use App\Models\ProjectenBronomschrijvingen;
use App\Models\ProjectenBronlocaties;
use App\Models\ProjectenRisicoklassen;

use Carbon\Carbon;

class InstellingenController extends Api\InstellingenController{
    
    public function index(){
        return view('instellingen.index', []);
    }
    
    //Processen
    public function processen(){
        return view('instellingen.processen.processen');
    }
    public function procesSettings($proces_id){
        $blades = ['Algemeen' => 'processen.algemeen'];

        return view('instellingen.general', [
            'blades' => $blades,
            'blades_data' => [
                'proces_id' => $proces_id,
            ]
        ]);
    }
    public function procesStappen($proces_id){
        $proces = Processen::findOrFail($proces_id);
        return view('instellingen.processen.stappen', [
          'proces' => $proces,
        ]);
    }
    public function procesBestanden($proces_id){
        $proces = Processen::findOrFail($proces_id);
        return view('instellingen.processen.bestanden', [
          'proces' => $proces,
        ]);
    }
    
    //Stappen
    public function stap($id){
        $stap = Stappen::findOrFail($id);
        $manager = new StapInstellingen(['stap' => $id, 'proces' => $stap->proces_id]);

        return view('instellingen.stappen.stap', [
          'manager' => $manager,
          'proces' => $manager->proces,
          'stap' => $manager->stap,
        ]);
    }
    
    //General Settings
    public function projecten(){
        $blades = ['Hoofdpagina' => 'projecten.index', 'Stappen' => 'projecten.steps'];
        return view('instellingen.general', [
          'blades' => $blades,
        ]);
    }
    public function adressen(){
        $blades = ['Algemeen' => 'adressen.algemeen', 'Hoofdpagina' => 'adressen.index', 'Tabel component' => 'adressen.table-component'];
        return view('instellingen.general', [
            'blades' => $blades,
        ]);
    }

    //Lijsten
    public function lijsten(){
        return view('instellingen.lijsten.lijsten', []);
    }
    public function locaties(){
        return view('instellingen.lijsten.locaties');
    }
    public function bronnen(){
        return view('instellingen.lijsten.bronnen');
    }
    public function risicoklassen(){
        return view('instellingen.lijsten.risicoklassen');
    }

    //Redenen
    public function redenen(){
        return view('instellingen.redenen.redenen');
    }

    //TODO: Clean up / refactor
//    public function redenen(){
//        $redenen = ProjectenRedenen::where('actief', '1')->orderBy('onderdeel', 'asc')->orderBy('reden', 'asc')->get();
//        $onderdelen = $redenen->unique('onderdeel')->all();
//        return view('instellingen.redenen', ['redenen' => $redenen, 'onderdelen' => $onderdelen]);
//    }
    
    public function removereden($id){
        $redenens = ProjectenRedenen::where('id', $id)->update(['actief' => 0]);;
        return redirect('dashboard/instellingen/redenen')->with('status', 'Reden is verwijderd.');
    }
    public function newreden($onderdeel){
        return view('instellingen.newreden', ['onderdeel' => $onderdeel]);
    }
    public function storereden(Request $request, $onderdeel){
        ProjectenRedenen::insert(
          [
            'onderdeel' => isset($onderdeel) ? $onderdeel : null,
            'reden' => isset($request->reden) ? $request->reden : null,
            'actief' => 1,
            'created_at' => Carbon::now()
          ]
        );
        return redirect('dashboard/instellingen/redenen')->with('status', 'Reden is toegevoegd.');
    }
    

}
