<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use DB;
use Config;
use Carbon\Carbon;
use App\Models\Projecten;
use App\Models\Adressen;
use App\Models\AsbestBronnen;
use App\Models\ProjectenRisicoklassen;
use App\Models\ProjectenBronlocaties;
use App\Models\ProjectenBronomschrijvingen;
use App\Models\TempHistBronnenInventariseerder;
use App\Models\TempHistBronnenSaneerder;


class HistorischebronnenimportController extends Controller
{
    // Ophalen benodigde bestanden
    public function menu(){
        return view('historischebronnenimport.menu', []);
    }
    public function index()
    {
        return view('historischebronnenimport.index', []);
    }
    public function indexsaneerder()
    {
        return view('historischebronnenimportsaneerder.index', []);
    }
    // Aantal bronnen per inventarisatie en adres berekenen
    public function indexproject()
    {
        $histbronnencount = count(TempHistBronnenInventariseerder::where('actief', 1)->groupBy('adres_id', 'inventarisatiedatum')->get());
        return view('historischebronnenimportproject.index', ['histbronnencount' => $histbronnencount]);
    }

    // 1. SPLITSEN ADRESSEN (BRONNEN)
    // Loop die 100 bronnen per keer ophaalt, voor inventarisatie data (Dit maakt het verdraagzaam voor de server)
    // Wanneer de huidige 100 klaar zijn krijgen deze de status 'adreskopieklaar = 1' mee, hierdoor komen ze niet meer in de lijst voor
    public function get100bronnen(Request $request){
        $i = $request->i;
        $start = $i * 100;
        if($i > 0){
            $updatestart = ($i - 1) * 100;
            $updateend = $updatestart + 100;
            $array = TempHistBronnenInventariseerder::join('adressen', 'adressen.id', '=', 'temp_hist_bronnen_inventariseerder.adres_id')->where('temp_hist_bronnen_inventariseerder.actief', 1)->where('adreskopieklaar', 0)->orderBy('adressen.straat')->orderBy('adressen.huisnummer')->orderBy('adressen.toevoeging')->offset($updatestart)->limit(100)->pluck('temp_hist_bronnen_inventariseerder.id')->toArray();
            TempHistBronnenInventariseerder::whereIn('id', $array)->update([
                'adreskopieklaar' => 1
            ]);
        }

        $bronnen = TempHistBronnenInventariseerder::join('adressen', 'adressen.id', '=', 'temp_hist_bronnen_inventariseerder.adres_id')->where('temp_hist_bronnen_inventariseerder.actief', 1)->where('adreskopieklaar', 0)->select('*', 'temp_hist_bronnen_inventariseerder.id AS regelid')->orderBy('adressen.straat')->orderBy('adressen.huisnummer')->orderBy('adressen.toevoeging')->offset($start)->limit(100)->get();

        return response()->json(
            [
                'success' => true,
                'i' => $i,
                'bronnen' => $bronnen
            ]
        );
    }

    // Loop die 100 bronnen per keer ophaalt, voor saneerder data (Dit maakt het verdraagzaam voor de server)
    // Wanneer de huidige 100 klaar zijn krijgen deze de status 'adreskopieklaar = 1' mee, hierdoor komen ze niet meer in de lijst voor
    public function get100bronnensaneerder(Request $request){
        $i = $request->i;
        $start = $i * 100;
        if($i > 0){
            $updatestart = ($i - 1) * 100;
            $updateend = $updatestart + 100;
            $array = TempHistBronnenSaneerder::join('adressen', 'adressen.id', '=', 'temp_hist_bronnen_saneerder.adres_id')->where('temp_hist_bronnen_saneerder.actief', 1)->where('adreskopieklaar', 0)->orderBy('adressen.straat')->orderBy('adressen.huisnummer')->orderBy('adressen.toevoeging')->offset($updatestart)->limit(100)->pluck('temp_hist_bronnen_saneerder.id')->toArray();
            TempHistBronnenSaneerder::whereIn('id', $array)->update([
                'adreskopieklaar' => 1
            ]);
        }

        $bronnen = TempHistBronnenSaneerder::join('adressen', 'adressen.id', '=', 'temp_hist_bronnen_saneerder.adres_id')->where('temp_hist_bronnen_saneerder.actief', 1)->where('adreskopieklaar', 0)->select('*', 'temp_hist_bronnen_saneerder.id AS regelid')->orderBy('adressen.straat')->orderBy('adressen.huisnummer')->orderBy('adressen.toevoeging')->offset($start)->limit(100)->get();

        return response()->json(
            [
                'success' => true,
                'i' => $i,
                'bronnen' => $bronnen
            ]
        );
    }

    // Verkrijgen adressen van geselecteerd complexnummer
    public function getcomplexadressen($complexnummer){
        $complexadressen = Adressen::where('complexnummer', $complexnummer)->get();

        return response()->json(
            [
                'adressen' => $complexadressen
            ]
        );
    }

    // Voeg voor het geselecteerde adres alle projectinformatie toe die zich in de hoofdrij bevindt, voor de inventarisatie data (>Splitsen adressen)
    public function addbronnen(Request $request){
        $bron = TempHistBronnenInventariseerder::where('id', $request->regelid)->first();

        foreach($request->adressen AS $adres){
            TempHistBronnenInventariseerder::insert([
                'created_at' => Carbon::now(),
                'inventarisatiedatum' => $bron->inventarisatiedatum,
                'adres_id' => $adres,
                'project_id' => $bron->project_id,
                'bronnummer' => $bron->bronnummer,
                'aantal' => $bron->aantal,
                'eenheid' => $bron->eenheid,
                'omschrijving_id'=> $bron->omschrijving_id,
                'risicoklasse_id' => $bron->risicoklasse_id,
                'locatie_id' => $bron->locatie_id,
                'zav' => $bron->zav,
                'asbesthoudend' => $bron->asbesthoudend,
                'saneeradvies' => $bron->saneeradvies,
                'gesaneerd' => $bron->gesaneerd,
                'deelsanering'=> $bron->deelsanering,
                'naarnieuwproject'=> $bron->naarnieuwproject,
                'opmerking' => $bron->opmerking,
                'opmerking2' => $bron->opmerking2,
                'opmerking3' => $bron->opmerking3,
                'actief' => 1,
                'locatie' => $bron->locatie,
                'risicoklasse' => $bron->risicoklasse,
                'omschrijving' => $bron->omschrijving,
                'adreskopieklaar' => 1
            ]);
        }

        return response()->json(
            [
                'success' => true
            ]
        );
    }

    // Voeg voor het geselecteerde adres alle projectinformatie toe die zich in de hoofdrij bevindt, voor de saneerder data (>Splitsen adressen)
    public function addbronnensaneerder(Request $request){
        $bron = TempHistBronnenSaneerder::where('id', $request->regelid)->first();

        foreach($request->adressen AS $adres){
            TempHistBronnenSaneerder::insert([
                'created_at' => Carbon::now(),
                'saneerdatum' => $bron->saneerdatum,
                'adres_id' => $adres,
                'project_id' => $bron->project_id,
                'bronnummer' => $bron->bronnummer,
                'aantal' => $bron->aantal,
                'eenheid' => $bron->eenheid,
                'omschrijving_id'=> $bron->omschrijving_id,
                'risicoklasse_id' => $bron->risicoklasse_id,
                'locatie_id' => $bron->locatie_id,
                'zav' => $bron->zav,
                'asbesthoudend' => $bron->asbesthoudend,
                'saneeradvies' => $bron->saneeradvies,
                'gesaneerd' => $bron->gesaneerd,
                'deelsanering'=> $bron->deelsanering,
                'naarnieuwproject'=> $bron->naarnieuwproject,
                'opmerking' => $bron->opmerking,
                'actief' => 1,
                'locatie' => $bron->locatie,
                'risicoklasse' => $bron->risicoklasse,
                'omschrijving' => $bron->omschrijving,
                'adreskopieklaar' => 1
            ]);
        }

        return response()->json(
            [
                'success' => true
            ]
        );
    }

    // 2.1. RISICOKLASSE
    // Vervangen risicoklasse verkregen data met risicoklasse in database
    // Voor zowel de verkregen inventarisatie- als de saneerder data
    public function replacerisicoklasse($saneerder = null)
    {
        if($saneerder == null){
            $risicoklassen = TempHistBronnenInventariseerder::groupBy('risicoklasse')->get();
        } else {
            $risicoklassen = TempHistBronnenSaneerder::groupBy('risicoklasse')->get();
        }

        $allerisicoklassen = ProjectenRisicoklassen::where('actief', 1)->get();

        return view('historischebronnenimport.replacerisicoklasse', [ 'saneerder' => $saneerder, 'risicoklassen' => $risicoklassen, 'allerisicoklassen' => $allerisicoklassen ]);
    }
    // Updaten risicoklasse die zich in verkregen data bevindt, met zojuist ingevulde risicoklasse
    // Voor zowel de verkregen inventarisatie- als de saneerder data
    public function updaterisicoklasse($saneerder = null, Request $request)
    {
        if($saneerder == null){
            TempHistBronnenInventariseerder::where('risicoklasse', $request->risicoklasse)->update([
                'risicoklasse_id' => $request->risicoklasse_id]);
        } else {
            TempHistBronnenSaneerder::where('risicoklasse', $request->risicoklasse)->update([
                'risicoklasse_id' => $request->risicoklasse_id]);
        }

        if(empty($request->risicoklasse)){
            $request->risicoklasse = '';
        }

        return response()->json(
            [
                'success' => true
            ]
        );
    }
    // Wanneer een (handige) risicoklasse in de verkregen staat, maar deze zich niet in de database bevindt. kan deze toegevoegd worden aan de database.
    // Voor zowel de verkregen inventarisatie- als de saneerder data
    public function addrisicoklasse($saneerder = null, Request $request){

        $risicoklasse_id = ProjectenRisicoklassen::insertGetId([
            'created_at' => Carbon::now(),
            'klasse' => $request->risicoklasse,
            'actief' => 1,
        ]);

        if($saneerder == null){
            TempHistBronnenInventariseerder::where('risicoklasse', $request->risicoklasse)->update([
                'risicoklasse_id' => $risicoklasse_id]);
        } else {
            TempHistBronnenSaneerder::where('risicoklasse', $request->risicoklasse)->update([
                'risicoklasse_id' => $risicoklasse_id]);
        }

        return response()->json(
            [
                'success' => true
            ]
        );
    }

    // 2.2. BRONLOCATIE
    // Vervangen bronlocatie verkregen data met bronlocatie in database
    // Voor zowel de verkregen inventarisatie- als de saneerder data
    public function replacelocatie($saneerder = null)
    {
        if($saneerder == null){
            $locaties = TempHistBronnenInventariseerder::groupBy('locatie')->paginate(100);
        } else {
            $locaties = TempHistBronnenSaneerder::groupBy('locatie')->paginate(100);
        }

        $allelocaties = ProjectenBronlocaties::where('active', 1)->get();

        return view('historischebronnenimport.replacelocatie', [ 'saneerder' => $saneerder, 'locaties' => $locaties, 'allelocaties' => $allelocaties ]);
    }
    // Updaten bronlocatie die zich in verkregen data bevindt, met zojuist ingevulde bronlocatie
    // Voor zowel de verkregen inventarisatie- als de saneerder data
    public function updatelocatie($saneerder = null, Request $request){

        if($saneerder == null){
            TempHistBronnenInventariseerder::where('locatie', $request->locatie)->update([
                'locatie_id' => $request->locatie_id]);
        } else {
            TempHistBronnenSaneerder::where('locatie', $request->locatie)->update([
                'locatie_id' => $request->locatie_id]);
        }

        if(empty($request->locatie)){
            $request->locatie = '';
        }

        return response()->json(
            [
                'success' => true
            ]
        );
    }
    // Wanneer een (handige) risicoklasse in de verkregen staat, maar deze zich niet in de database bevindt. kan deze toegevoegd worden aan de database.
    // Voor zowel de verkregen inventarisatie- als de saneerder data
    public function addlocatie($saneerder = null, Request $request){

        $locatie_id = ProjectenBronlocaties::insertGetId([
            'created_at' => Carbon::now(),
            'locatie' => $request->locatie,
        ]);

        if($saneerder == null){
            TempHistBronnenInventariseerder::where('locatie', $request->locatie)->update([
                'locatie_id' => $locatie_id]);
        } else {
            TempHistBronnenSaneerder::where('locatie', $request->locatie)->update([
                'locatie_id' => $locatie_id]);
        }

        return response()->json(
            [
                'success' => true
            ]
        );
    }

    // 2.3. BRONOMSCHRIJVING
    // Vervangen bronomschrijving verkregen data met bronomschrijving in database
    // Voor zowel de verkregen inventarisatie- als de saneerder data
    public function replaceomschrijving($saneerder = null)
    {
        if($saneerder == null){
            $omschrijvingen = TempHistBronnenInventariseerder::groupBy('omschrijving')->paginate(100);
        } else {
            $omschrijvingen = TempHistBronnenSaneerder::groupBy('omschrijving')->paginate(100);
        }

        $alleomschrijvingen = ProjectenBronomschrijvingen::where('active', 1)->get();

        return view('historischebronnenimport.replaceomschrijving', [ 'saneerder' => $saneerder, 'omschrijvingen' => $omschrijvingen, 'alleomschrijvingen' => $alleomschrijvingen ]);
    }
    // Updaten bronomschrijving die zich in verkregen data bevindt, met zojuist ingevulde bronomschrijving
    // Voor zowel de verkregen inventarisatie- als de saneerder data
    public function updateomschrijving($saneerder = null, Request $request){

        if($saneerder == null){
            TempHistBronnenInventariseerder::where('omschrijving', $request->omschrijving)->update([
                'omschrijving_id' => $request->omschrijving_id]);
        } else {
            TempHistBronnenSaneerder::where('omschrijving', $request->omschrijving)->update([
                'omschrijving_id' => $request->omschrijving_id]);
        }

        if(empty($request->omschrijving)){
            $request->omschrijving = '';
        }

        return response()->json(
            [
                'success' => true
            ]
        );
    }
    // Wanneer een (handige) bronomschrijving in de verkregen staat, maar deze zich niet in de database bevindt. kan deze toegevoegd worden aan de database.
    // Voor zowel de verkregen inventarisatie- als de saneerder data
    public function addomschrijving($saneerder = null, Request $request){

        $omschrijving_id = ProjectenBronomschrijvingen::insertGetId([
            'created_at' => Carbon::now(),
            'omschrijving' => $request->omschrijving,
        ]);

        if($saneerder == null){
            TempHistBronnenInventariseerder::where('omschrijving', $request->omschrijving)->update([
                'omschrijving_id' => $omschrijving_id]);
        } else {
            TempHistBronnenSaneerder::where('omschrijving', $request->omschrijving)->update([
                'omschrijving_id' => $omschrijving_id]);
        }


        return response()->json(
            [
                'success' => true
            ]
        );
    }

    // 3. GESANEERD
    public function indexgesaneerd()
    {
        return view('historischebronnengesaneerd.index', []);
    }
    // Loop die 100 bronnen per keer ophaalt, en de inventarisatie- en saneerder data langs elkaar zet (Overeenkomstig)
    // Wanneer de huidige 100 klaar zijn krijgen deze de status 'tempbronnenklaar = 1' mee, hierdoor komen ze niet meer in de lijst voor
    public function get100adressen(Request $request){
        $i = $request->i;
        $start = $i * 100;
        if($i > 0){
            $updatestart = ($i - 1) * 100;
            $updateend = $updatestart + 100;
            $array = Adressen::with('temp_hist_bronnen_inventariseerder', 'temp_hist_bronnen_saneerder')->where('tempbronnenklaar', 0)->orderBy('straat')->orderBy('huisnummer')->orderBy('toevoeging')->offset($updatestart)->limit(100)->pluck('id')->toArray();
            Adressen::whereIn('id', $array)->update([
                'tempbronnenklaar' => 1
            ]);
        }

        $adressen = Adressen::with('temp_hist_bronnen_inventariseerder', 'temp_hist_bronnen_saneerder')->where('tempbronnenklaar', 0)->orderBy('straat')->orderBy('huisnummer')->orderBy('toevoeging')->offset($start)->limit(100)->get();

        return response()->json(
            [
                'success' => true,
                'i' => $i,
                'adressen' => $adressen
            ]
        );
    }
    // Wanneer op de checkbox bij 'inventariseerder' wordt gedrukt, verander 'gesaneerd' naar 1
    // Wanneer deze perongeluk is aangevinkt (en wordt onvinkt), moet de status weer aangepast worden naar 0
    public function setgesaneerd(Request $request){
        $id = $request->id;
        $gesaneerd = $request->gesaneerd;

        TempHistBronnenInventariseerder::where('id', $id)->update([
            'gesaneerd' => $gesaneerd
        ]);

        return response()->json(
            [
                'success' => true,
            ]
        );
    }
    // Wanneer op de checkbox bij 'saneerder' wordt gedrukt, kopiëer regel en zet in inventariseerder dataset
    // Wanneer deze perongeluk is aangevinkt (en wordt onvinkt), moet gechecked worden als id in inventariseerder dataset zit, ja=>verwijder rij
    public function setgeinventariseerd(Request $request){
        $id = $request->id;
        $geinventariseerd = $request->geinventariseerd;
        $bronsan = TempHistBronnenSaneerder::where('id', $id)->first();

        if($geinventariseerd == 1){
            TempHistBronnenInventariseerder::updateOrInsert([
                'inventarisatiedatum' => $bronsan->saneerdatum,
                'adres_id' => $bronsan->adres_id,
                'bronnummer' => $bronsan->bronnummer,
                'aantal' => $bronsan->aantal,
                'eenheid' => $bronsan->eenheid,
                'omschrijving_id'=> $bronsan->omschrijving_id,
                'risicoklasse_id' => $bronsan->risicoklasse_id,
                'locatie_id' => $bronsan->locatie_id,
            ],[
                'created_at' => Carbon::now(),
                'project_id' => 0,
                'zav' => $bronsan->zav,
                'asbesthoudend' => $bronsan->asbesthoudend,
                'saneeradvies' => $bronsan->saneeradvies,
                'gesaneerd' => 1,
                'deelsanering'=> $bronsan->deelsanering,
                'naarnieuwproject'=> $bronsan->naarnieuwproject,
                'opmerking' => $bronsan->opmerking,
                'opmerking2' => $bronsan->opmerking2,
                'opmerking3' => $bronsan->opmerking3,
                'actief' => 1,
                'locatie' => $bronsan->locatie,
                'risicoklasse' => $bronsan->risicoklasse,
                'omschrijving' => $bronsan->omschrijving,
                'adreskopieklaar' => 0
            ]);
        }else{
            TempHistBronnenInventariseerder::where('id', $id)->delete();
        }


        return response()->json(
            [
                'success' => true,
            ]
        );
    }

    // 4. AANMAKEN ALS PROJECTEN
    // Voor iedere combinatie van 'adres_id' en 'inventarisatiedatum', maak een project aan in 'projecten' dataset
    public function insertproject(Request $request){
        $histbronnen = TempHistBronnenInventariseerder::where('actief', 1)->groupBy('adres_id', 'inventarisatiedatum')->orderBy('id')->skip($request->i)->take(100)->get();
        foreach($histbronnen as $project){
            Projecten::updateOrInsert([
                'adres_id' => $project->adres_id,
                'inventarisatiedatum' => $project->inventarisatiedatum
            ],[
                'created_at' => Carbon::now(),
                'projectnummer' => 'importproject',
                'opdrachtnummer' => 'importproject',
                'inventarisatiedatumbeoogd' => $project->inventarisatiedatum,
                'saneringafgerond' => $project->gesaneerd,
                'inventarisatiebureauid' => $project->inventarisatiebureauid ?? '',
                'saneerderid' => $project->saneerderid ?? '',
                'status1gereed' => $project->inventarisatiedatum,
                'status2gereed' => $project->inventarisatiedatum,
                'status3gereed' => $project->inventarisatiedatum,
                'status4gereed' => $project->inventarisatiedatum,
                'status5gereed' => $project->inventarisatiedatum,
                'status6gereed' => $project->inventarisatiedatum,
                'status7gereed' => $project->inventarisatiedatum,
                'status8gereed' => $project->inventarisatiedatum,
                'status9gereed' => $project->inventarisatiedatum,
                'status10gereed' => $project->inventarisatiedatum,
                'status11gereed' => $project->inventarisatiedatum,
                'status12gereed' => $project->inventarisatiedatum,
                'status13gereed' => $project->inventarisatiedatum,
                'status14gereed' => $project->inventarisatiedatum,
                'status15gereed' => $project->inventarisatiedatum,
                'status16gereed' => $project->inventarisatiedatum,
                'status17gereed' => $project->inventarisatiedatum,
                'status18gereed' => $project->inventarisatiedatum,
                'status19gereed' => $project->inventarisatiedatum,
                'status20gereed' => $project->inventarisatiedatum,
                'afgerond' => 1,
                'projectafgeronddatum' => $project->inventarisatiedatum,
                'projectafgerondaanpasbaar' => $project->inventarisatiedatum,
                'stap_id' => 14,
                'actief' => 1
            ]);

            // Loop door bronnen en voeg toe in 'ProjectenBronnen' in database
            $bronnen = TempHistBronnenInventariseerder::where('actief', 1)->where('adres_id', $project->adres_id)->where('inventarisatiedatum', $project->inventarisatiedatum)->get();
            foreach($bronnen as $bron){
                AsbestBronnen::updateOrInsert([
                    'adres_id' => $bron->adres_id,
                    'project_id' => $project->id,
                    'bronnummer' => $bron->bronnummer
                ],[
                    'created_at' => Carbon::now(),
                    'aantal' => $bron->aantal,
                    'eenheid' => $bron->eenheid,
                    'omschrijving_id' => $bron->omschrijving_id,
                    'risicoklasse_id' => $bron->risicoklasse_id,
                    'locatie_id' => $bron->locatie_id,
                    'zav' => $bron->zav,
                    'asbesthoudend' => $bron->asbesthoudend,
                    'saneeradvies' => $bron->saneeradvies,
                    'gesaneerd' => $bron->gesaneerd,
                    'deelsanering' => $bron->deelsanering,
                    'naarnieuwproject' => $bron->naarnieuwproject,
                    'opmerking' => $bron->opmerking,
                    'opmerking2' => $bron->opmerking2,
                    'opmerking3' => $bron->opmerking3,
                    'actief' => 1

                ]);
            }
        }

        return response()->json(
            [
                'success' => true
            ]
        );
    }

}
