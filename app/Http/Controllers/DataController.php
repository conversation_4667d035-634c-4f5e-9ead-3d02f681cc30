<?php

namespace App\Http\Controllers;

use DB;
use Config;
use Illuminate\Http\Request;
use App\Models\Projecten;
use App\Models\AsbestBronnen;

class DataController extends Controller{

    public function statistieken(){
        return view('data.statistieken', [

        ]);
    }


    public function statistiek(Request $request){
        if(false){}
        else if($request->statistiek == "gemiddeldeprojectduur"){
            $data = Projecten::where('status13gereed', '!=', '0000-00-00')
                ->selectRaw("ROUND(AVG(DATEDIFF(aangemaakt_op, status13gereed)), 1) AS gemiddeldeduur, DATE_FORMAT(aangemaakt_op, '%b, %Y') AS datum")
                ->where('aangemaakt_op', '>=', now()->subMonths(24))
                ->groupBy('datum')
                ->orderBy('aangemaakt_op', 'DESC')
                ->get();
        }
        else if($request->statistiek == "aantaldagentussenprojectaanmaakenvooropnamedatum"){
            $dagen0 = Projecten::where('actief', 1)
                ->where('planmatigid', '')
                ->where('aangemaakt_op', '!=', '0000-00-00')
                ->where('vooropnamedatum', '!=', '0000-00-00')
                ->whereRaw('DATEDIFF(vooropnamedatum, aangemaakt_op) = 0')
                ->selectRaw('COUNT(*) as aantal, YEAR(aangemaakt_op) as jaar')
                ->where('aangemaakt_op', '>=', now()->subYears(5))
                ->groupBy('jaar')
                ->orderBy('aangemaakt_op', 'DESC')
                ->get();
            $dagen1 = Projecten::where('actief', 1)
                ->where('planmatigid', '')
                ->where('aangemaakt_op', '!=', '0000-00-00')
                ->where('vooropnamedatum', '!=', '0000-00-00')
                ->whereRaw('DATEDIFF(vooropnamedatum, aangemaakt_op) = 1')
                ->selectRaw('COUNT(*) as aantal, YEAR(aangemaakt_op) as jaar')
                ->where('aangemaakt_op', '>=', now()->subYears(5))
                ->groupBy('jaar')
                ->orderBy('aangemaakt_op', 'DESC')
                ->get();
            $dagen2 = Projecten::where('actief', 1)
                ->where('planmatigid', '')
                ->where('aangemaakt_op', '!=', '0000-00-00')
                ->where('vooropnamedatum', '!=', '0000-00-00')
                ->whereRaw('DATEDIFF(vooropnamedatum, aangemaakt_op) = 2')
                ->selectRaw('COUNT(*) as aantal, YEAR(aangemaakt_op) as jaar')
                ->where('aangemaakt_op', '>=', now()->subYears(5))
                ->groupBy('jaar')
                ->orderBy('aangemaakt_op', 'DESC')
                ->get();
            $dagen3 = Projecten::where('actief', 1)
                ->where('planmatigid', '')
                ->where('aangemaakt_op', '!=', '0000-00-00')
                ->where('vooropnamedatum', '!=', '0000-00-00')
                ->whereRaw('DATEDIFF(vooropnamedatum, aangemaakt_op) = 3')
                ->selectRaw('COUNT(*) as aantal, YEAR(aangemaakt_op) as jaar')
                ->where('aangemaakt_op', '>=', now()->subYears(5))
                ->groupBy('jaar')
                ->orderBy('aangemaakt_op', 'DESC')
                ->get();
            $dagen4 = Projecten::where('actief', 1)
                ->where('planmatigid', '')
                ->where('aangemaakt_op', '!=', '0000-00-00')
                ->where('vooropnamedatum', '!=', '0000-00-00')
                ->whereRaw('DATEDIFF(vooropnamedatum, aangemaakt_op) = 4')
                ->selectRaw('COUNT(*) as aantal, YEAR(aangemaakt_op) as jaar')
                ->where('aangemaakt_op', '>=', now()->subYears(5))
                ->groupBy('jaar')
                ->orderBy('aangemaakt_op', 'DESC')
                ->get();
            $dagen5 = Projecten::where('actief', 1)
                ->where('planmatigid', '')
                ->where('aangemaakt_op', '!=', '0000-00-00')
                ->where('vooropnamedatum', '!=', '0000-00-00')
                ->whereRaw('DATEDIFF(vooropnamedatum, aangemaakt_op) = 5')
                ->selectRaw('COUNT(*) as aantal, YEAR(aangemaakt_op) as jaar')
                ->where('aangemaakt_op', '>=', now()->subYears(5))
                ->groupBy('jaar')
                ->orderBy('aangemaakt_op', 'DESC')
                ->get();
            $dagen5meer = Projecten::where('actief', 1)
                ->where('planmatigid', '')
                ->where('aangemaakt_op', '!=', '0000-00-00')
                ->where('vooropnamedatum', '!=', '0000-00-00')
                ->whereRaw('DATEDIFF(vooropnamedatum, aangemaakt_op) > 5')
                ->selectRaw('COUNT(*) as aantal, YEAR(aangemaakt_op) as jaar')
                ->where('aangemaakt_op', '>=', now()->subYears(5))
                ->groupBy('jaar')
                ->orderBy('aangemaakt_op', 'DESC')
                ->get();
            $data = [
                "dagen0" => $dagen0,
                "dagen1" => $dagen1,
                "dagen2" => $dagen2,
                "dagen3" => $dagen3,
                "dagen4" => $dagen4,
                "dagen5" => $dagen5,
                "dagen5meer" => $dagen5meer
            ];
        }
        else if($request->statistiek == "doorlooptijd1"){
            $gehaald = Projecten::where('actief', 1)
                ->where('inventarisatiedatum', '!=', '0000-00-00')
                ->where('status3gereed', '!=', '0000-00-00')
                ->whereRaw('DATEDIFF(inventarisatiedatum, status3gereed) <= 2')
                ->selectRaw('COUNT(*) as aantal, YEAR(status3gereed) as jaar')
                ->where('status3gereed', '>=', now()->subYears(5))
                ->groupBy('jaar')
                ->orderBy('status3gereed', 'DESC')
                ->get();
            $nietgehaald = Projecten::where('actief', 1)
                ->where('inventarisatiedatum', '!=', '0000-00-00')
                ->where('status3gereed', '!=', '0000-00-00')
                ->whereRaw('DATEDIFF(inventarisatiedatum, status3gereed) > 2')
                ->selectRaw('COUNT(*) as aantal, YEAR(status3gereed) as jaar')
                ->where('status3gereed', '>=', now()->subYears(5))
                ->groupBy('jaar')
                ->orderBy('status3gereed', 'DESC')
                ->get();
            $data = [
                "gehaald" => $gehaald,
                "nietgehaald" => $nietgehaald
            ];
        }
        // Doorlooptijd inventarisatie (KPI: 5 dagen)
        else if($request->statistiek == "doorlooptijdinventarisatie"){
            $gehaald = Projecten::where('actief', 1)
                ->where('status5gereed', '!=', '0000-00-00')
                ->where('inventarisatiedatum', '!=', '0000-00-00')
                ->whereRaw('DATEDIFF(status5gereed, inventarisatiedatum) <= 5')
                ->selectRaw('COUNT(*) as aantal, YEAR(inventarisatiedatum) as jaar')
                ->where('inventarisatiedatum', '>=', now()->subYears(5))
                ->groupBy('jaar')
                ->orderBy('inventarisatiedatum', 'DESC')
                ->get();
            $nietgehaald = Projecten::where('actief', 1)
                ->where('status5gereed', '!=', '0000-00-00')
                ->where('inventarisatiedatum', '!=', '0000-00-00')
                ->whereRaw('DATEDIFF(status5gereed, inventarisatiedatum) > 5')
                ->selectRaw('COUNT(*) as aantal, YEAR(inventarisatiedatum) as jaar')
                ->where('inventarisatiedatum', '>=', now()->subYears(5))
                ->groupBy('jaar')
                ->orderBy('inventarisatiedatum', 'DESC')
                ->get();
            $data = [
                "gehaald" => $gehaald,
                "nietgehaald" => $nietgehaald
            ];
        }
        // Doorlooptijd sanering (KPI: 7 dagen)
        else if($request->statistiek == "doorlooptijdsanering"){
            $gehaald = Projecten::where('actief', 1)
                ->where('eindsanering', '!=', '0000-00-00')
                ->where('status8gereed', '!=', '0000-00-00')
                ->whereRaw('DATEDIFF(eindsanering, status8gereed) <= 7')
                ->selectRaw('COUNT(*) as aantal, YEAR(status8gereed) as jaar')
                ->where('status8gereed', '>=', now()->subYears(5))
                ->groupBy('jaar')
                ->orderBy('status8gereed', 'DESC')
                ->get();
            $nietgehaald = Projecten::where('actief', 1)
                ->where('eindsanering', '!=', '0000-00-00')
                ->where('status8gereed', '!=', '0000-00-00')
                ->whereRaw('DATEDIFF(eindsanering, status8gereed) <= 7')
                ->selectRaw('COUNT(*) as aantal, YEAR(status8gereed) as jaar')
                ->where('status8gereed', '>=', now()->subYears(5))
                ->groupBy('jaar')
                ->orderBy('status8gereed', 'DESC')
                ->get();
            $data = [
                "gehaald" => $gehaald,
                "nietgehaald" => $nietgehaald
            ];
        }

// Inventarisatie
        // Woningbezit geïnventariseerd (%)
        else if($request->statistiek == "inventarisatie"){
            $aantal_geinventariseerd = Projecten::where('inventarisatiedatum', '!=', '0000-00-00')
                ->distinct('adres_id')
                ->count('adres_id');
            $aantal_niet_geinventariseerd = Projecten::where('inventarisatiedatum', '=', '0000-00-00')
                ->distinct('adres_id')
                ->count('adres_id');
            $data = [
                "aantal_geinventariseerd" => $aantal_geinventariseerd,
                "aantal_niet_geinventariseerd" => $aantal_niet_geinventariseerd
            ];
        }
        // Status na inventarisatie
        else if($request->statistiek == "statusnainventarisatie"){
            $welasbest = Projecten::where('actief', 1)
                ->where('statusnainventarisatie', 1)
                ->count();
            $geenasbest = Projecten::where('actief', 1)
                ->where('statusnainventarisatie', 2)
                ->count();
            $nietsaneren = Projecten::where('actief', 1)
                ->where('statusnainventarisatie', 3)
                ->count();
            $data = [
                "wel_asbest" => $welasbest,
                "geen_asbest" => $geenasbest,
                "niet_saneren" => $nietsaneren
            ];
        }
        // Aantal inventarisaties per maand
        else if($request->statistiek == "inventarisatiespermaand"){
            $data = Projecten::selectRaw("COUNT(*) AS aantal, DATE_FORMAT(inventarisatiedatum, '%b, %Y') AS datum")
                ->where('inventarisatiedatum', '>=', now()->subMonths(24))
                ->groupBy('datum')
                ->orderBy('inventarisatiedatum', 'DESC')
                ->get();
        }
        // Gemiddelde duur uitgevoerde inventarisaties in dagen
        else if($request->statistiek == "gemiddeldeduurinventarisatie"){
            $data = Projecten::where('status3gereed', '!=', '0000-00-00')
                ->where('status5gereed', '!=', '0000-00-00')
                ->selectRaw("ROUND(AVG(DATEDIFF(status5gereed, status3gereed)), 1) AS gemiddeldeduur, DATE_FORMAT(status3gereed, '%b, %Y') AS datum")
                ->where('status3gereed', '>=', now()->subMonths(24))
                ->groupBy('datum')
                ->orderBy('status3gereed', 'DESC')
                ->get();
        }
        // Inventarisaties tijdig uitgevoerd per jaar
        else if($request->statistiek == "inventarisatietijdiguitgevoerd"){
            $tijdiguitgevoerd = Projecten::where('actief', 1)
                ->where('inventarisatiedatum', '!=', '0000-00-00')
                ->where(DB::raw('inventarisatiedatum'), '<=', DB::raw('inventarisatiedatumbeoogd'))
                ->selectRaw('YEAR(inventarisatiedatum) as jaar, COUNT(*) as aantal')
                ->where('inventarisatiedatum', '>=', now()->subYears(5))
                ->groupBy('jaar')
                ->orderBy('inventarisatiedatum', 'DESC')
                ->get();
            $niettijdiguitgevoerd = Projecten::where('actief', 1)
                ->where('inventarisatiedatum', '!=', '0000-00-00')
                ->where(DB::raw('inventarisatiedatum'), '>', DB::raw('inventarisatiedatumbeoogd'))
                ->selectRaw('YEAR(inventarisatiedatum) as jaar, COUNT(*) as aantal')
                ->where('inventarisatiedatum', '>=', now()->subYears(5))
                ->groupBy('jaar')
                ->orderBy('inventarisatiedatum', 'DESC')
                ->get();
            $data = [
                "tijdiguitgevoerd" => $tijdiguitgevoerd,
                "niet_tijdiguitgevoerd" => $niettijdiguitgevoerd
            ];
        }
        // Reden later uitvoeren van inventarisatie
        else if($request->statistiek == "redenlateruitgevoerdinventarisatie"){
            $data = Projecten::select(DB::raw('COUNT(*) AS aantalkeer'), 'redeninventarisatielater')
                ->groupBy('redeninventarisatielater')
                ->orderBy('aantalkeer', 'desc')
                ->get();
            return response()->json(['data' => $data]);
        }
        // Inventarisatierapport tijdig opgeleverd
        else if($request->statistiek == "inventarisatierapporttijdig"){
            $rapporttijdig = Projecten::where('actief', 1)
                ->where('inventarisatiedatum', '!=', '0000-00-00')
                ->where(DB::raw('DATE_ADD(inventarisatiedatum, INTERVAL 7 DAY)'), '>=', DB::raw('status5gereed'))
                ->selectRaw('YEAR(inventarisatiedatum) as jaar, COUNT(*) as aantal')
                ->where('inventarisatiedatum', '>=', now()->subYears(5))
                ->groupBy('jaar')
                ->orderBy('inventarisatiedatum', 'DESC')
                ->get();
            $nietrapporttijdig = Projecten::where('actief', 1)
                ->where('inventarisatiedatum', '!=', '0000-00-00')
                ->where(DB::raw('DATE_ADD(inventarisatiedatum, INTERVAL 7 DAY)'), '<', DB::raw('status5gereed'))
                ->selectRaw('YEAR(inventarisatiedatum) as jaar, COUNT(*) as aantal')
                ->where('inventarisatiedatum', '>=', now()->subYears(5))
                ->groupBy('jaar')
                ->orderBy('inventarisatiedatum', 'DESC')
                ->get();
            $data = [
                "rapporttijdig" => $rapporttijdig,
                "niet_rapporttijdig" => $nietrapporttijdig
            ];
        }
        // Gemiddelde duur aanlevering rapport per maand
        else if($request->statistiek == "gemiddeldeduuraanleveringrapport"){
            $data = Projecten::where('actief', 1)
                ->where('status5gereed', '!=', '0000-00-00')
                ->where('inventarisatiedatum', '!=', '0000-00-00')
                ->whereRaw('inventarisatiedatum >= status5gereed')
                ->selectRaw("ROUND(AVG(DATEDIFF(inventarisatiedatum, status5gereed)), 1) AS gemiddeldeduur, DATE_FORMAT(inventarisatiedatum, '%b, %Y') AS datum")
                ->where('inventarisatiedatum', '>=', now()->subMonths(24))
                ->groupBy('datum')
                ->orderBy('inventarisatiedatum', 'DESC')
                ->get();
            return response()->json(['data' => $data]);
        }
        // Meest voorkomende bronlocaties (top 10)
        else if($request->statistiek == "meestvoorkomendebronlocaties"){
            $data = AsbestBronnen::select(DB::raw('COUNT(*) AS aantalkeer'), 'locatie_id')
                ->groupBy('locatie_id')
                ->orderBy('aantalkeer', 'desc')
                ->take(10)
                ->join('projecten_bronlocaties', 'projecten_bronlocaties.id', '=', 'asbest_bronnen.locatie_id')
                ->select('projecten_bronlocaties.locatie AS locatie', DB::raw('COUNT(*) AS aantalkeer'))
                ->groupBy('projecten_bronlocaties.locatie')
                ->orderBy('aantalkeer', 'desc')
                ->get();
            return response()->json(['data' => $data]);
        }
        // Meest voorkomende bronnamen(top 10)
        else if($request->statistiek == "meestvoorkomendebronnamen"){
            $data = AsbestBronnen::select(DB::raw('COUNT(*) AS aantalkeer'), 'omschrijving_id')
                ->groupBy('omschrijving_id')
                ->orderBy('aantalkeer', 'desc')
                ->take(10)
                ->join('projecten_bronomschrijvingen', 'projecten_bronomschrijvingen.id', '=', 'asbest_bronnen.omschrijving_id')
                ->select('projecten_bronomschrijvingen.omschrijving AS omschrijving', DB::raw('COUNT(*) AS aantalkeer'))
                ->groupBy('projecten_bronomschrijvingen.omschrijving')
                ->orderBy('aantalkeer', 'desc')
                ->get();
            return response()->json(['data' => $data]);
        }
        // Meest gesaneerde bronnamen(top 10)
        else if($request->statistiek == "meestgesaneerdebronnamen"){
            $data = AsbestBronnen::where('gesaneerd', '==', '1')
                ->select(DB::raw('COUNT(*) AS aantalkeer'), 'omschrijving_id')
                ->groupBy('omschrijving_id')
                ->orderBy('aantalkeer', 'desc')
                ->take(10)
                ->join('projecten_bronomschrijvingen', 'projecten_bronomschrijvingen.id', '=', 'asbest_bronnen.omschrijving_id')
                ->select('projecten_bronomschrijvingen.omschrijving AS omschrijving', DB::raw('COUNT(*) AS aantalkeer'))
                ->groupBy('projecten_bronomschrijvingen.omschrijving')
                ->orderBy('aantalkeer', 'desc')
                ->get();
            return response()->json(['data' => $data]);
        }
        // Zelf aangebrachte voorzieningen
        else if($request->statistiek == "zav"){
            $data = AsbestBronnen::select(DB::raw('COUNT(*) AS aantalkeer'), 'zav')
                ->groupBy('zav')
                ->get();
        }

// Aannemers
// WANNEER ER GEBRUIKERS(/AANNEMERS) IN DE HOOFDDB ZITTEN, MOET ID VERVANGEN WORDEN MET DE NAAM
        // Aantal projecten per aannemer per maand
        else if($request->statistiek == "aantalprojectenpermaandperaannemer"){
            $data = Projecten::selectRaw("COUNT(*) AS aantal, DATE_FORMAT(aangemaakt_op, '%b, %Y') AS datum, aannemerid")
                ->where('aangemaakt_op', '>=', now()->subMonths(24))
                ->groupBy('datum', 'aannemerid')
                ->orderBy('aangemaakt_op', 'DESC')
                ->get();
        }
        // Aantal projecten per aannemer per jaar
        else if($request->statistiek == "aantalprojectenperjaarperaannemer"){
            $data = Projecten::selectRaw("COUNT(*) AS aantal, YEAR(aangemaakt_op) AS datum, aannemerid")
                ->where('aangemaakt_op', '>=', now()->subYears(5))
                ->groupBy('datum', 'aannemerid')
                ->orderBy('aangemaakt_op', 'DESC')
                ->get();
        }

// Saneerders

// Eindcontrole

// Kosten
        // Totale factuurwaarde projecten per maand
        else if($request->statistiek == "totalefactuurwaarde"){
            $data = Projecten::selectRaw("sum(kosteninventarisatie+kostensanering+kostenregie+kosteneindcontrole) AS totaal, DATE_FORMAT(aangemaakt_op, '%b, %Y') AS datum")
                ->where('aangemaakt_op', '>=', now()->subMonths(24))
                ->groupBy('datum')
                ->orderBy('aangemaakt_op', 'DESC')
                ->get();
        }
        // Gemiddelde factuurwaarde per project (per maand)
        else if($request->statistiek == "gemiddeldefactuurwaarde"){
            $data = Projecten::selectRaw("avg(kosteninventarisatie+kostensanering+kostenregie+kosteneindcontrole) AS totaal, DATE_FORMAT(aangemaakt_op, '%b, %Y') AS datum")
                ->where('aangemaakt_op', '>=', now()->subMonths(24))
                ->groupBy('datum')
                ->orderBy('aangemaakt_op', 'DESC')
                ->get();
        }

        return response()->json(
            [
                'success' => true,
                'data' => $data,
            ]
        );
    }
}
