<?php

namespace App\Http\Controllers;

use App\Models\User;
use Carbon\Carbon;
use Hash;
use Illuminate\Http\Request;

class PasswordsController extends Controller{

    public function reset(){
        return view('auth.passwords.reset');
    }
    public function update(Request $request){
        $request->validate([
            'password_new' => [
                'required',
                'string',
                'min:8',
                'regex:/[A-Z]/',
                'regex:/[a-z]/',
                'regex:/[0-9]/',
                'regex:/[@$!%*?&]/',
            ],
            'password_repeat' => [
                'required',
                'same:password_new',
            ],
        ]);

        User::where('id', _user()->id)->update([
            'password' => Hash::make($request->password_new),
            'password_updated_at' => Carbon::now(),
        ]);

        return redirectHome();
    }

}
