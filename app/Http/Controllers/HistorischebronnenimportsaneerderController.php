<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use DB;
use Config;
use Carbon\Carbon;

use App\Models\Adressen;
use App\Models\TempHistBronnenSaneerder;


class HistorischebronnenimportsaneerderController extends Controller
{
    public function index()
    {
        return view('historischebronnenimportsaneerder.index', []);
    }

    public function get100Bronnensaneerder(Request $request){
        $i = $request->i;
        $start = $i * 100;
        if($i > 0){
            $updatestart = ($i - 1) * 100;
            $updateend = $updatestart + 100;
            $array = TempHistBronnenSaneerder::join('adressen', 'adressen.id', '=', 'temp_hist_bronnen_saneerder.adres_id')->where('temp_hist_bronnen_saneerder.actief', 1)->where('adreskopieklaar', 0)->orderBy('adressen.straat')->orderBy('adressen.huisnummer')->orderBy('adressen.toevoeging')->offset($updatestart)->limit(100)->pluck('temp_hist_bronnen_saneerder.id')->toArray();
            TempHistBronnenSaneerder::whereIn('id', $array)->update([
                'adreskopieklaar' => 1
            ]);
        }
        
        $bronnen = TempHistBronnenSaneerder::join('adressen', 'adressen.id', '=', 'temp_hist_bronnen_saneerder.adres_id')->where('temp_hist_bronnen_saneerder.actief', 1)->where('adreskopieklaar', 0)->select('*', 'temp_hist_bronnen_saneerder.id AS regelid')->orderBy('adressen.straat')->orderBy('adressen.huisnummer')->orderBy('adressen.toevoeging')->offset($start)->limit(100)->get();

        return response()->json(
            [
                'success' => true,
                'i' => $i,
                'bronnen' => $bronnen
            ]
        );
    }

    public function getcomplexadressen($complexnummer){
        // Verkrijgen adressen van geselecteerd complexnummer
        $complexadressen = Adressen::where('complexnummer', $complexnummer)->get();
        
        return response()->json(
            [
                'adressen' => $complexadressen
            ]  
        );
    }

    public function addbronnensaneerder(Request $request){
        $bron = TempHistBronnenSaneerder::where('id', $request->regelid)->first();

        foreach($request->adressen AS $adres){
            TempHistBronnenSaneerder::insert([
                'created_at' => Carbon::now(),
                'saneerdatum' => $bron->saneerdatum,
                'adres_id' => $adres,
                'project_id' => $bron->project_id,
                'bronnummer' => $bron->bronnummer,
                'aantal' => $bron->aantal,
                'eenheid' => $bron->eenheid,
                'omschrijving_id'=> $bron->omschrijving_id,
                'risicoklasse_id' => $bron->risicoklasse_id,
                'locatie' => $bron->locatie,
                'zav' => $bron->zav,
                'asbesthoudend' => $bron->asbesthoudend,
                'saneeradvies' => $bron->saneeradvies,
                'gesaneerd' => $bron->gesaneerd,
                'deelsanering'=> $bron->deelsanering,
                'naarnieuwproject'=> $bron->naarnieuwproject,
                'opmerking' => $bron->opmerking,
                'actief' => 1,
                'locatie' => $bron->locatie,
                'risicoklasse' => $bron->risicoklasse,
                'omschrijving' => $bron->omschrijving,
                'adreskopieklaar' => 1
            ]);
        }

        return response()->json(
            [
                'success' => true
            ]
        );
    }

}
