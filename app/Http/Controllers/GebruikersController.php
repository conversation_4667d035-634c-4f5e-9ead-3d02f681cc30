<?php

namespace App\Http\Controllers;

use App\Models\Domeinen;
use App\Models\SSODrivers;
use App\Models\SSOResponses;
use App\Models\User;
use App\Models\UsersAuthTokens;
use Illuminate\Http\Request;
use Lara<PERSON>\Socialite\Facades\Socialite;

use Auth;

class GebruikersController extends Api\GebruikersController{
    
    public function index(){
        return view('gebruikers.index');
    }
    public function gebruiker($id = null){
        $user = $id
          ? User::with('role')->findOrFail($id)
          : new User();
        
        return view('gebruikers.gebruiker', ['user' => $user]);
    }


    //Token Login
    public function loginUsingToken($token){
//        Auth::logout();

        $user = UsersAuthTokens::validate($token);
        if(!$user){ return redirect('dashboard/login'); }

        Auth::loginUsingId($user->id);

        return redirectHome();
    }

    //SSO Login
    public function loginUsingSSO($domain_key){
        session(['domain_key' => $domain_key]);
        return SSODrivers::byDomainKey($domain_key)->redirect();
    }
    public function redirectSSO(Request $request){
        $driver = SSODrivers::byDomainKey(session('domain_key'));
        $response = SSOResponses::create([
           'domain_id' => $driver->domain_id,
           'driver_id' => $driver->id,
           'auth_response' => json_encode($request->all()),
        ]);

        try{
            $user_response = $driver->user();
            $response->update([
                'user_response' => json_encode(optional($user_response)->user),
            ]);

            $user = User::where('username', $user_response->email)->first();
            if(!$user){
                return redirect('dashboard/login')->with('danger', 'Gebruiker niet gevonden');
            }

            Auth::login($user);
            return redirectHome();
        }
        catch(\Throwable $e){
            $message = $e->getMessage();
            if($message === ''){ $message = 'unknown'; }

            $response->update([ 'error_message' => $message ]);
            return redirect('dashboard/login')->with('danger', 'Inloggen mislukt');
        }

    }


}
