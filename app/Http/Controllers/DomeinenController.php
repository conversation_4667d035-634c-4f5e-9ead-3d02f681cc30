<?php

namespace App\Http\Controllers;

use App\Models\Domeinen;
use App\Models\User;

use Log;

class DomeinenController extends Controller{

    public function switch($id){
        $domain = Domeinen::findOrFail($id);
        if(!$domain->isAccessible()){
            return redirectHome()->with('danger', 'Gekozen domein is niet beschikbaar.');
        }

        Log::info('Gebruiker is van domein geswitcht.', [
            'data' => [ 'Domein' => $domain->domein, ]
        ]);

        User::setDomain($id);
        return redirectHome()->with('success', "Domein succesvol geswitched naar  {$domain->domein}");
    }

}
