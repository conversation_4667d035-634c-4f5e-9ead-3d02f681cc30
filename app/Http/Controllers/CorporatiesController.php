<?php

namespace App\Http\Controllers;

use App\Models\Corporaties;
use App\Models\Domeinen;
use App\Models\DomeinenDatabases;
use App\Models\DomeinKoppel;
use App\Models\User;
use Illuminate\Http\Request;

class CorporatiesController extends Api\CorporatiesController{

	public function index(){
		return view('corporaties.index');
	}
	public function corporatie($id = null){
		$corporatie = $id
			? Domeinen::with('users')->findOrFail($id)
			: new Domeinen();
		
		$database = $id
			? DomeinenDatabases::where('domein_id', $id)->firstOrFail()
			: new DomeinenDatabases();
		
		return view('corporaties.corporatie', [
			'corporatie' => $corporatie,
			'database' => $database,
		]);
	}

}
