<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;

class SupervisionController extends Controller{

    public function index(){
        return view('supervision.index');
    }
    public function user($user_id){
        if(!_user()->hasSupervisionOver($user_id)){ return redirectUnauthorized(); }
        $user = User::findOrFail($user_id);

        return view('supervision.user', [
            'user' => $user,
        ]);
    }

}
