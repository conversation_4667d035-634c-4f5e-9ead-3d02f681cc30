<?php

namespace App\Http\Controllers;

use App\Models\Projecten;
use App\Models\Stappen;
use Illuminate\Http\Request;
use ProjectStapType;

class LAVSController extends Api\LAVSController{

    public function saneringOpleveren($guid){
        $project = Projecten::where('guid', $guid)->firstOrFail();
        $stap = Stappen::getStap('asbest_project', 'sanering_opleveren');

        //Correct stap
        if($project->assertStap($stap->stap_key)){
            return view('confirm', [
                'title' => 'Sanering Opleveren Bevestigen',
                'message' => 'Klik op de onderstaande knop om de sanering oplevering te bevestigen.',
                'new_title' => 'Sanering Opleveren Bevestigd',
                'new_message' => 'Uw bevestiging van de saneringsoplevering is succesvol verwerkt.',
                'button_text' => 'Sanering Bevestigen',
                'endpoint' => '/dashboard/api/lavs/complete/sanering-opleveren',
                'data' => [
                    'guid' => $guid,
                ]
            ]);
        }

        //Stap al afgerond
        if($project->stapStatus($stap->id) == ProjectStapType::COMPLETED){
            return view('message', [
                'title' => 'Sanering Opleveren Bevestigd',
                'message' => 'Uw bevestiging van de saneringsoplevering is succesvol verwerkt.'
            ]);
        }

        //Eerdere stappen
        abort(403, 'Het project '.$project->projectnr.' bevindt zich niet in de stap Sanering Opleveren.');
    }


}
