<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Nieuws;

use Carbon\Carbon;
use Cookie;

class NieuwsController extends Controller{
    public function index(){
        $nieuws = Nieuws::where('disabled', 0)->whereDate('datum', '<=', Carbon::now())->orderBy('datum', 'desc')->get();
        Cookie::queue('laatstenieuwsupdate', Carbon::now(), strtotime("+1 year"));
        return view('nieuws.nieuws', ['nieuws' => $nieuws]);
    }

    public function bericht($id){
        $bericht = Nieuws::where('id', $id)->first();
        return view('nieuws.bericht', ['bericht' => $bericht]);
    }
}
