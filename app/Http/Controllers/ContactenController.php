<?php

namespace App\Http\Controllers;

use App\Models\Contacten;
use Illuminate\Http\Request;

class ContactenController extends Api\ContactenController{

    public function index(){
        return view('contacten.index');
    }
    
    public function contact($id = null){
        $contact = $id
          ? Contacten::find($id)
          : new Contacten();
        
        return view('contacten.contact', [
          'contact'=> $contact
        ]);
    }
    
}
