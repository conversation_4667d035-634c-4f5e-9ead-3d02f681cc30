<?php

namespace App\Http\Controllers\Api;

use App\Classes\Structure;
use App\Http\Controllers\Controller;
use App\Models\Logs;
use Carbon\Carbon;
use Illuminate\Http\Request;


class LogsController extends Controller{

    private $relations = ['user', 'project', 'stap'];

    public function get(Request $request){
        //Model
        $model = new Logs();
        $model = $model->where('domain_id', _domain()->id)->with($this->relations)->orderBy(($request->order_by ?? 'created_at'), ($request->order_direction ?? 'DESC'))->orderBy('id', ($request->order_direction ?? 'DESC'));

        //Filters
        if(isset($request->level)){
            $model = $model->where('level', $request->level);
        }
        else{
            $model->where('level', '>', 100);
        }

        if(isset($request->ids)){
            $model = $model->whereIn('id', $request->ids);
        }
        if(isset($request->id)){
            $model = $model->where('id', $request->id);
        }
        if(isset($request->user)){
            $model = $model->where('user_id', $request->user);
        }
        if(isset($request->project)){
            $model = $model->where('project_id', $request->project);
        }
        if(isset($request->start_date)){
            $model = $model->where('created_at', '>=', $request->start_date);
        }
        if(isset($request->end_date)){
            $model = $model->where('created_at', '<=',"{$request->end_date} 23:59:59");
        }


        //Fetch
        $data = Structure::paginateModel($model, $request->page, $request->per_page);

        //Append additional data
//        foreach($data->records as $adres){}

        //Resonse
        if(isset($request->id)){
            return response(['log' => $data->records->first()], 200);
        }
        return response([
            'logs' => $data->records,
            'all_ids' => $data->all_ids,
            'current_ids' => $data->current_ids,
        ], 200);
    }

}
