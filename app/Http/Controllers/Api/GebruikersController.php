<?php

namespace App\Http\Controllers\Api;

use App\Classes\Structure;
use App\Http\Controllers\Controller;
use App\Models\DomeinenDatabases;
use App\Models\DomeinKoppel;
use App\Models\User;
use Illuminate\Http\Request;

class GebruikersController extends Controller{
    
    public $relations = [ 'role', 'bedrijf', 'supervisor' ];
    
    public function get(Request $request){
        //Model
        $model = new User();
        $model = $model->whereHas('domeinen', function($query){
            $query->where('domeinen.id', _domain()->id);
        })->with($this->relations)->orderBy(($request->order_by ?? 'created_at'), ($request->order_direction ?? 'DESC'));

        //Filters
        if(isset($request->ids)){
            $model = $model->whereIn('id', $request->ids);
        }
        if(isset($request->supervised_by)){
            $model = $model->whereHas('supervisor', function($query) use ($request){
                $query->where('supervisor_id', $request->supervised_by);
            });
        }
        
        //Fetch
        $data = Structure::paginateModel($model, $request->page, $request->per_page);
        
        return response([
          'gebruikers' => $data->records,
          'all_ids' => $data->all_ids ?? [],
          'current_ids' => $data->current_ids ?? [],
        ], 201);
    }
    
}
