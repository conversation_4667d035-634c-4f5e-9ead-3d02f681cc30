<?php

namespace App\Http\Controllers\Api;

use App\Classes\Structure;
use App\Http\Controllers\Controller;
use App\Models\InstellingenStappen;
use App\Models\InstellingenStappenGebruikers;
use App\Models\Projecten;
use App\Models\ProjectenStappen;
use App\Models\Stappen;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use ProjectStatus;
use Sebastian<PERSON>ergmann\Invoker\Exception;

use DB;

class ProjectenController extends Controller{
	
    private $relations = ['adres', 'stap'];
    
    public function get(Request $request){
        //Model
        $model = new Projecten();
        $model = $model->with($this->relations)->orderBy(($request->order_by ?? 'created_at'), ($request->order_direction ?? 'DESC'));

        //Filters
        if(isset($request->adres)){
            $model = $model->where('adres_id', $request->adres);
        }
        if(isset($request->proces)){
            $model = $model->where('proces_id', $request->proces);
        }
        if(isset($request->stap)){
            $model = $model->where('stap_id', $request->stap);
        }
        if(isset($request->status)){

            if($request->status == ProjectStatus::ACTIELIJST && hasPermission('projecten_volledige_actielijst')){ $request->status = 'LOPEND'; }

            if($request->status == ProjectStatus::ACTIELIJST || $request->status == ProjectStatus::ACTIELIJST_BY_ROLE){

                $model = $model->where('status', 'LOPEND')->whereHas('projecten_gebruikers', function ($query) use ($request) {

                    //Actielijst of users with the same role
                    if($request->status == ProjectStatus::ACTIELIJST_BY_ROLE && (hasPermission('projecten_stappen_invullen_zelfde_rol') || hasPermission('projecten_stappen_inzien_zelfde_rol'))){
                        $query->where('role_id', _user()->role->id);
                    }
                    else{
                        $query->where('user_id', _user()->id);
                    }

                });

                //Volledige actielijst
                if(!hasPermission('projecten_volledige_proces_actielijst')){
                    $model = $model->whereIn('stap_id', Stappen::invulbareRoleStappenIds(_role()->id));
                }

            }
            else{
                $model = $model->where('status', $request->status);
            }
        }
        if(isset($request->ids)){
            $model = $model->whereIn('id', $request->ids);
        }

        //Group projecten with planmatig_key
        if(isset($request->planmatig_key)){
            $model = $model->where('planmatig_key', $request->planmatig_key);
        }
        else{
            $hidden_planmatige_ids = $model->pluck('id')->toArray();
            $model = $model->where(function ($query) use($request) {

                $subquery = Projecten::selectRaw('MIN(id) as id')->groupBy('planmatig_key')->groupBy('planmatig_key', 'stap_id', 'status');
                if(isset($request->ids)){
                    $subquery = $subquery->whereIn('id', $request->ids);
                }

                $query->whereIn('id', $subquery)->orWhereNull('planmatig_key');

            });
        }

        //Fetch
        $data = Structure::paginateModel($model, $request->page, $request->per_page);

        //Append non grouped ids
        if(isset($hidden_planmatige_ids)){
            $data->all_ids = resetIndex(array_unique(array_merge($data->all_ids, $hidden_planmatige_ids)));
        }

        //Append additional data
        foreach($data->records as $project){
            $project->status();
            $project->users();
            $project->stapUsersInvullen();
            $project->stapUsersInvullenVisual();
            $project->isInvulbaar();
            $project->isInzichtbaar();

            //Append planamtig data only when projects are grouped by the key
            if(!isset($request->planmatig_key)){
                $project->planmatig();
            }

            $project->confirmCache();
        }

        return response([
          'projecten' => $data->records,
          'all_ids' => $data->all_ids ?? [],
          'current_ids' => $data->current_ids ?? [],
        ], 201);
    }
    
    public function setStatus(Request $request){
        try{

            $projecten_ids = is_array($request->project) ? $request->project : [$request->project];
            $status = $request->status;

            Projecten::whereIn('id', $projecten_ids)->each(function($project) use ($status){
               $project->setStatus($status);
            });

            return response(null, 200);
        }
        catch(Exception $e){
            return handleError($e);
        }
    }

}
