<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Files;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class FilesController extends Controller{

	public function get($guid){
		$file = Files::where('guid', $guid)->firstOrFail();
		$name = $file->name;
        $extension = $file->extension;

        if(substr($name, -(strlen($extension) + 1)) !== ".{$extension}"){
            $name .= ".{$extension}";
        }

		try{
			$storage_file = Storage::disk($file->disk)->get($file->fullPath());
			return response($storage_file)
				->header('Content-Type', $file->mime)
				->header('Content-Disposition', 'inline; filename="'.$name.'"');
		}
		catch(\Exception $e){
			handleError($e);
			abort(500, $e->getMessage());
		}
	}

	public function upload(Request $request){
		try{
            $file = Files::storeFile([
                'file' => $request->file('file'),
                'path' => '/editor'
            ]);
			return response($file, 201);
		}
		catch(\Exception $e){
			handleError($e);
			return response(null, 500);
		}
	}

}
