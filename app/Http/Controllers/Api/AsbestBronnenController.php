<?php

namespace App\Http\Controllers\Api;

use App\Classes\Structure;
use App\Http\Controllers\Controller;
use App\Models\AsbestBronnen;
use Illuminate\Http\Request;

class AsbestBronnenController extends Controller{

    public $relations = [ 'adres', 'project', 'bronlocatie', 'bronomschrijving', 'risicoklasse'];

    public function get(Request $request){
        //Model
        $model = new AsbestBronnen();
        $model = $model->whereNull('planmatig_key')->where('active', 1)->with($this->relations)->orderBy(($request->order_by ?? 'created_at'), ($request->order_direction ?? 'DESC'));

        //Filters
        if(isset($request->ids)){
            $model = $model->whereIn('id', $request->ids);
        }
        if(isset($request->project)){
            $model = $model->where('project_id', $request->project);
        }
        if(isset($request->adres)){
            $model = $model->where('adres_id', $request->adres);
        }
        if(isset($request->locatie)){
            if(!is_array($request->locatie)){ $request->locatie = [ $request->locatie ]; }
            $model = $model->whereIn('locatie_id', $request->locatie);
        }
        if(isset($request->omschrijving)){
            if(!is_array($request->omschrijving)){ $request->omschrijving = [ $request->omschrijving ]; }
            $model = $model->whereIn('omschrijving_id', $request->omschrijving);
        }
        if(isset($request->risicoklasse)){
            if(!is_array($request->risicoklasse)){ $request->risicoklasse = [ $request->risicoklasse ]; }
            $model = $model->whereIn('risicoklasse_id', $request->risicoklasse);
        }
        if(isset($request->zav)){
            if(!is_array($request->zav)){ $request->zav = [ $request->zav ]; }
            $model = $model->whereIn('zav', $request->zav);
        }
        if(isset($request->eenheid)){
            if(!is_array($request->eenheid)){ $request->eenheid = [ $request->eenheid ]; }
            $model = $model->whereIn('eenheid', $request->eenheid);
        }
        if(isset($request->asbesthoudend)){
            if(!is_array($request->asbesthoudend)){ $request->asbesthoudend = [ $request->asbesthoudend ]; }
            $model = $model->whereIn('asbesthoudend', $request->asbesthoudend);
        }
        if(isset($request->gesaneerd)){
            if(!is_array($request->gesaneerd)){ $request->gesaneerd = [ $request->gesaneerd ]; }
            $model = $model->whereIn('gesaneerd', $request->gesaneerd);
        }

        //Fetch
        $data = Structure::paginateModel($model, $request->page, $request->per_page);

        //Append additional data
//        foreach($data->records as $bron){}

        return response([
            'asbest_bronnen' => $data->records,
            'all_ids' => $data->all_ids ?? [],
            'current_ids' => $data->current_ids ?? [],
        ], 201);
    }

}
