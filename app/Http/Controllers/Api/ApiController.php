<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ApiController extends Controller
{
    public function location(Request $request){
        if(!isset($request->postcode)){
            return response()->json(["status" => 0]);
        }

//    Define vars and response data
        $key = env('GOOGLE_MAPS');
        $address = urlencode($request->postcode);
        $url = "https://maps.googleapis.com/maps/api/geocode/json?language=nl&key=".$key;

        $response = [
            "status" => null,
            "data" => [
                "straat" => null,
                "huisnummer" => $request->huisnummer,
                "postcode" => $request->postcode,
                "plaats" => null,
                "gemeente" => null,
                "provincie" => null,
                "land" => null,
                "lat" => null,
                "lng" => null,
            ]
        ];


//    First curl => fetch all data except street name
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url."&address=".$address);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        $data = json_decode(curl_exec($ch), true);
        curl_close($ch);

        if(!count($data["results"])){
            return response()->json(["status" => 0]);
        }

        $components = $data["results"][0]["address_components"];

//    Define lat and lng.
        $lat = $data["results"][0]["geometry"]["location"]["lat"];
        $lng = $data["results"][0]["geometry"]["location"]["lng"];
        $response["data"]["lat"] = $lat;
        $response["data"]["lng"] = $lng;

//    Put data into response
        foreach($components ?? [] as $component){
            $type = $component["types"][0] ?? null;
            if($type == "locality"){
                $response["data"]["plaats"] = $component["long_name"] ?? null;
            }
            elseif($type == "administrative_area_level_2"){
                $response["data"]["gemeente"] = $component["long_name"] ?? null;
            }
            elseif($type == "administrative_area_level_1"){
                $response["data"]["provincie"] = $component["long_name"] ?? null;
            }
            elseif($type == "country"){
                $response["data"]["land"] = $component["long_name"] ?? null;
            }
        }

        if(isset($lat) && isset($lng)){
//      Second curl => fetch street name from lat and lng.
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url."&latlng=".$lat.",".$lng);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, 0);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
            $data = json_decode(curl_exec($ch), true);
            curl_close($ch);

//      Put street name into response data
            foreach($data["results"][0]["address_components"] ?? [] as $component){
                $type = $component["types"][0] ?? null;
                if($type == "route"){
                    $response["data"]["straat"] = $component["long_name"] ?? null;
                }
            }
        }

        $response["status"] = 1;

        return response()->json($response, 201);
    }
}
