<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Livewire\Instellingen\Processen\Stappen;
use App\Models\Projecten;
use Illuminate\Http\Request;

class LAVSController extends Controller{

    public function saneringOpleverenBevestigen(Request $request){
        $project = Projecten::where('guid', $request->guid)->firstOrFail();

        if($project->assertStap('sanering_opleveren')){
            $project->completeStap(medium: 'LAVS');
            return response(null, 200);
        }

        return response(null, 400);
    }

}
