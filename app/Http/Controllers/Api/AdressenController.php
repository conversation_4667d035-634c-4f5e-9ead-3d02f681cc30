<?php

namespace App\Http\Controllers\Api;

use App\Classes\Structure;
use App\Http\Controllers\Controller;
use App\Models\Adressen;
use Illuminate\Http\Request;

class AdressenController extends Controller
{
    
    private $relations = [];
    
    public function get(Request $request){
        //Model
        $model = new Adressen();
        $model = $model->with($this->relations)->orderByRaw(Adressen::sanitizeOrderBy($request->order_by) .' '. ($request->order_direction ?? 'DESC'));
        
        //Filters
        if(isset($request->ids)){
            $model = $model->whereIn('id', $request->ids);
        }
        if(isset($request->id)){
            $model = $model->where('id', $request->id);
        }
        if(isset($request->active)){
            $model = $model->where('actief', $request->active);
        }

        //Fetch
        $data = Structure::paginateModel($model, $request->page, $request->per_page);

        
        //Append additional data
//        foreach($data->records as $adres){}
        
        //Resonse
        if(isset($request->id)){
            return response(['adres' => $data->records->first()], 200);
        }
        return response([
          'adressen' => $data->records,
          'all_ids' => $data->all_ids,
          'current_ids' => $data->current_ids,
        ], 200);
    }
    
}
