<?php

namespace App\Http\Controllers\Api;

use App\Classes\Structure;
use App\Http\Controllers\Controller;
use App\Models\Emails;
use Illuminate\Http\Request;

class MailsController extends Controller{

    private $relations = [ 'project', 'user', 'recipients' ];

    public function get(Request $request){
        //Model
        $model = new Emails();
        $model = $model->with($this->relations)->orderBy(($request->order_by ?? 'created_at'), ($request->order_direction ?? 'DESC'));

        //Filters
        if(isset($request->ids)){
            $model = $model->whereIn('id', $request->ids);
        }
        if(isset($request->id)){
            $model = $model->where('id', $request->id);
        }
        if(isset($request->project)){
            $model = $model->where('project_id', $request->project);
        }
        if(isset($request->stap)){
            $model = $model->where('stap_id', $request->stap);
        }
        if(isset($request->user)){
            $model = $model->where('user_id', $request->user);
        }
        if(isset($request->date)){
            $model = $model->whereDate('created_at', $request->date);
        }
        if(isset($request->status)){
            $model = $model->where('status', $request->status);
        }

        //Fetch
        $data = Structure::paginateModel($model, $request->page, $request->per_page);


        //Append additional data
        foreach($data->records as $adres){
            $adres->status();

            $adres->confirmCache();
        }

        //Resonse
        if(isset($request->id)){
            return response(['adres' => $data->records->first()], 200);
        }
        return response([
            'mails' => $data->records,
            'all_ids' => $data->all_ids,
            'current_ids' => $data->current_ids,
        ], 200);
    }

}
