<?php

namespace App\Http\Controllers\Api;

use App\Classes\Structure;
use App\Http\Controllers\Controller;
use App\Models\Contacten;
use Illuminate\Http\Request;

class ContactenController extends Controller
{
    public $relations = [ 'role' ];
    
    public function get(Request $request){
        //Model
        $model = new Contacten();
        $model = $model->with($this->relations)->orderBy(($request->order_by ?? 'created_at'), ($request->order_direction ?? 'DESC'));
        
        //Filters
        if(isset($request->ids)){
            $model = $model->whereIn('id', $request->ids);
        }
        
        //Fetch
        $data = Structure::paginateModel($model, $request->page, $request->per_page);
        
        return response([
          'contacten' => $data->records,
          'all_ids' => $data->all_ids ?? [],
          'current_ids' => $data->current_ids ?? [],
        ], 201);
    }
}
