<?php

namespace App\Http\Controllers\Api;

use App\Classes\Structure;
use App\Http\Controllers\Controller;
use App\Models\Complexen;
use Illuminate\Http\Request;

class ComplexenController extends Controller
{
    
    private $relations = [];
    
    public function get(Request $request){
        //Model
        $model = new Complexen();
        $model = $model->groupBy('complexnummer')->whereNotNull('complexnummer')->with($this->relations)->orderBy(($request->order_by ?? 'created_at'), ($request->order_direction ?? 'DESC'));
        
        //Filters
        if(isset($request->ids)){
            $model = $model->whereIn('complexnummer', $request->ids);
        }
        
        //Fetch
        $data = Structure::paginateModel($model, $request->page, $request->per_page);

        
        //Append additional data
        foreach($data->records as $complex){
            $complex->postcodes = $complex->postcodes();
            $complex->adressen = $complex->adressen();
        }
        
        return response([
          'complexen' => $data->records,
          'all_ids' => $data->all_ids,
          'current_ids' => $data->current_ids,
        ], 201);
    }

    //Asbest
    public function getAsbestUitvoerkosten(Request $request){
        $settings = stringToBool($request->settings ?? []);
        $kosten =  Complexen::asbestUitvoerkosten($settings, $request->query_settings);

        return response([
            'uitvoerkosten' => $kosten,
        ], 200);
    }

}
