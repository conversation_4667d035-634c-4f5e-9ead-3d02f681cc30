<?php

namespace App\Http\Controllers\Api;

use App\Classes\search\SearchManager;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class SearchController extends Controller{
    
    private $manager;
    
    public function __construct(Request $request){
        $params = [];
        if(isset($request->ids)){
            $params['ids'] = $request->ids;
        }
        
        $this->manager = new SearchManager($request->search, $params);
    }
    
    public function search($model){
        $model = snakeToCamelCase($model);

        if (!method_exists($this->manager, $model)) {
            return response(['message' => "Function $model() does not exist!"], 500);
        }
        
        $response = call_user_func(array($this->manager, $model));
        return response($response, 201);
    }

}
