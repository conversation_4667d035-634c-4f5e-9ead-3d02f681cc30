<?php

namespace App\Http\Controllers\Api;

use App\Classes\Structure;
use App\Http\Controllers\Controller;
use App\Models\StappenApi;
use App\Models\StappenApiLog;
use Illuminate\Http\Request;

class KoppelingenController extends Controller{

    private $relations = ['api'];

    public function get(Request $request){
        //Model
        $model = new StappenApiLog();
        $model = $model->with($this->relations)->orderBy(($request->order_by ?? 'created_at'), ($request->order_direction ?? 'DESC'));

        //Only from current domain
        $model->whereHas('api', function($query){
           $query->where('domain_id', _domain()->id);
        });

        //Filters
        if(isset($request->ids)){
            $model = $model->whereIn('id', $request->ids);
        }
        if(isset($request->koppeling)){
            $model = $model->whereHas('api', function($query) use ($request){
                $query->where('koppeling', $request->koppeling);
            });
        }
        if(isset($request->type)){
            $model = $model->whereHas('api', function($query) use ($request){
                $query->where('type', $request->type);
            });
        }
        if(isset($request->stap)){
            $model = $model->whereHas('api', function($query) use ($request){
                $query->where('stap_id', $request->stap);
            });
        }
        if(isset($request->start_date)){
            $model = $model->where('created_at', '>=', $request->start_date);
        }
        if(isset($request->end_date)){
            $model = $model->where('created_at', '<=', $request->end_date);
        }

        //Fetch
        $data = Structure::paginateModel($model, $request->page, $request->per_page);

        //Append additional data
        //foreach($data->records as $request){}

        return response([
            'requests' => $data->records,
            'all_ids' => $data->all_ids ?? [],
            'current_ids' => $data->current_ids ?? [],
        ], 201);
    }

    public function inbound($method, Request $request){
        $api = StappenApi::where([
            'type' => 'inbound',
            'method' => $method,
        ])->first();

        return $api->execute([
            'request' => $request,
        ]);
    }

}
