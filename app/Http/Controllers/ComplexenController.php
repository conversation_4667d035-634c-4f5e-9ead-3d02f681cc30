<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Complexen;

use DB;
use Config;


class ComplexenController extends Api\ComplexenController{

    public function index(){
        return view('complexen.index');
    }
    public function complex($id){
        $complex = Complexen::where(['complexnummer' => $id])->first();

        return view('complexen.complex', [
            'complex' => $complex,
        ]);
    }

    //Asbest
    public function matrix(){
        return view('complexen.matrix');
    }


}
