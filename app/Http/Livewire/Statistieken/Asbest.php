<?php

namespace App\Http\Livewire\Statistieken;

use App\Classes\statistieken\statistiekenBaseComponent;
use App\Interfaces\StatistiekenComponentInterface;
use App\Models\Projecten;
use App\Models\Processen;
use App\Models\ProjectenStappen;
use Carbon\Carbon;

class Asbest extends statistiekenBaseComponent{

    CONST PROCES_TYPE = 'ASBEST';

    //Data
    public $processen;

    //Livewire
    protected $rules = [
        'filters.start' => '',
        'filters.end' => '',
    ];

    //Lifecycle
    public function render(){
        return view('livewire.statistieken.asbest');
    }

    //Definitions
    public function defineProcessen(){
        $this->processen = Processen::where('type', self::PROCES_TYPE)->get();
    }
    public function defineFilters(){
        $this->filters['start'] = Carbon::now()->subYears(5)->firstOfYear()->format('Y-m-d');
        $this->filters['end'] = Carbon::now()->lastOfYear()->format('Y-m-d');
    }

    //Charts definitions
    public function defineAlgemeneCharts(){
        $this->addGroup([
            'name' => 'Algemeen',
        ]);

        $this->addChart('algemeen', [
            'name' => 'Aantal projecten aangemaakt per jaar',
            'type' => 'horizontalBar',
        ]);
        $this->addChart('algemeen', [
            'name' => 'Aantal projecten aangemaakt per maand',
            'type' => 'horizontalBar',
        ]);
        $this->addChart('algemeen', [
            'name' => 'Gemiddelde projectduur in dagen per jaar',
            'type' => 'horizontalBar',
        ]);
        $this->addChart('algemeen', [
            'name' => 'Aantal dagen tussen aanmaak project en vooropnamedatum (Planmatige projecten geëxcludeerd)',
            'type' => 'stackedHorizontalBar',
            'legend' => true,
        ]);
    }
    public function defineProcessenCharts(){
        foreach($this->processen as $proces){
            $group_key = $this->addGroup([
                'name' => "Proces: {$proces->naam}",
                'description' => "In onderstaande grafiek-reeks kunt u de gemiddelde doorlooptijd per projectstap inzien (in werkdagen).",
            ]);

            foreach(getActiveStappen($proces->id) as $stap){
                $this->addChart($group_key, [
                    'name' => "Stap {$stap->stapNr()}: $stap->stapnaam",
                    'type' => 'horizontalBar',
                    'legend' => true,
                    'legend_position' => 'right',
                    'fn' => 'gemiddeldeStapduurInDagenPerMaand',
                    'fn_attrs' => ['stap_id' => $stap->id],
                ]);
            }

        }
    }
    public function defineAsbestCharts(){
        //Inventarisatie
        $this->addGroup(['name' => 'Inventarisatie']);

        $this->addChart('inventarisatie', [
            'name' => 'Woningbezit geïnventariseerd',
            'type' => 'doughnut',
            'legend' => true,
            'legend_position' => 'right',
        ]);
    }


    //------Charts------//


    //Init
    public function initCharts(){
        $this->defineAlgemeneCharts();
        $this->defineProcessenCharts();
        $this->defineAsbestCharts();
    }

    //Inventarisatie
    public function woningbezitGenventariseerd(){
        $records = Projecten::whereBetween('aangemaakt_op', [$this->filters['start'], $this->filters['end']])
            ->whereIn('proces_id', $this->processen->pluck('id')->toArray())
            ->selectRaw('YEAR(aangemaakt_op) as year, COUNT(*) as count')
            ->groupBy('year')
            ->orderBy('year', 'DESC')
            ->get();

        $labels = $records->map(function($row){ return $row->year; });
        $data = $records->map(function($row){ return $row->count; });

        //TODO remove mock data
        return [
            'datasets' => [
                [
                    'label' => 'Aantal projecten',
                    'data' => [ 56, 24 ],
                    'backgroundColor' => [ '#238823', '#D2222D' ]
                ]
            ],
            'labels' => [ 'Geinventariseerd', 'Niet Geinventariseerd' ],
        ];
    }

}
