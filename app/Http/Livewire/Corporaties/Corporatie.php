<?php

namespace App\Http\Livewire\Corporaties;

use App\Models\Corporaties;
use App\Models\DomeinKoppel;
use App\Models\Files;
use Illuminate\Support\Facades\Hash;
use Livewire\Component;
use Livewire\WithFileUploads;

class Corporatie extends Component{
	use WithFileUploads;
	
	public $corporatie;
	public $database;
	
	public $logo;
	public $password = '';
	
	public $domeinKoppel;
	public $rules = [
	  'corporatie.domein' => 'required|max: 255',
	  'database.database_name' => 'required|max: 255',
	  'database.username' => 'required|max: 255',
	  'logo' => 'image',
	];
	
	public function mount(){
		$this->defineRules();
	}
	public function render(){
		return view('livewire.corporaties.corporatie');
	}
	
	public function store(){
		$this->validate();
		
		//Logo
		$this->storeLogo();
		
		//Domein
		$this->setDomeinKey();
		$this->corporatie->save();
		
		//Database
		$this->setDatabasePassword();
		$this->setDatabaseDomein();
		$this->database->save();
		
		DomeinKoppel::connect($this->corporatie->id, '1', '1');
		$this->emit('stored');
	}
	
	public function setDomeinKey(){
		if($this->corporatie->domein_key){
			return;
		}
		
		$key = str_replace(' ', '_', $this->corporatie->domein);
		$key = preg_replace('/[^a-zA-Z0-9_]/', '', $key);
		
		$this->corporatie->domein_key = strtolower($key);
	}
	public function setDatabasePassword(){
		if(!$this->password){
			return;
		}
		
		$this->database->password = ar_encrypt($this->password);
	}
	public function setDatabaseDomein(){
		$this->database->domein_id = $this->corporatie->id;
	}
	
	public function storeLogo(){
		if (!$this->logo) { return; }
		
		$file = Files::storeFile([
		  'file' => $this->logo,
		  'name' => $this->corporatie->domein . ' logo',
		  'path' => '/corporaties/logos'
		], 'public');
		
		$this->corporatie->logo_guid = $file->guid;
	}
	
	public function defineRules(){
		if(!$this->corporatie->id){
			$this->rules['password'] = 'required|max: 255';
		}
		
	}
	
}
