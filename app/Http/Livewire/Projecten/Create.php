<?php

namespace App\Http\Livewire\Projecten;

use App\Models\Adressen;
use App\Models\Projecten;
use App\Models\ProjectenAsbestSanering;
use App\Models\ProjectenBewoners;
use App\Models\ProjectenContacten;
use App\Models\ProjectenGebruikers;
use App\Models\ProjectenOpdrachtformulieren;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

use Log;
use ProjectStatus;

class Create extends Component{

    public $invulbaar = true;

    public $project;
    public $adres;
    public $proces;
    public $stap;

    public $project_inputs;
    public $adres_inputs;
    public $asbest_sanering;
    public $opdrachtformulier;

    public $gebruikers_toevoegen;
    public $gebruikers_contact;

    public $_gebruikers = [];
    public $_contacten = [];

    public $planmatig = false;
    public $planmatig_adressen = [];
    public $planmatig_projecten = [];

    public $adres_projecten = [];

    public $messages = [];
    public $rules = [
        'project.guid' => '',
    ];
    public $listeners = [
        'setAdres',
        'setPlanmatigAdres',
        'setGebruiker',
        'setContact'
    ];

    public function mount($manager){
        $this->proces = $manager->proces;
        $this->stap = $manager->stap;

        $this->project = new Projecten(['guid' => guid()]);
        $this->adres = new Adressen();

        $this->gebruikers_toevoegen = $manager->gebruikers_toevoegen;
        $this->gebruikers_contact = $manager->gebruikers_contact;

        $this->project_inputs = $manager->inputsByContext('project');
        $this->adres_inputs = $manager->inputsByContext('adres');

        $this->planmatig = $manager->planmatig;

        $this->setAdres(_get('adres'));
        $this->defineProcesInstances();
        $this->defineRules();
    }
    public function render(){
        return view('livewire.projecten.create');
    }

    public function store(){
        $this->validate();

        if($this->planmatig){
            $this->storeProjectenPlanmatig();
        }
        else{
            $this->storeAdres();
            $this->storeProject();
        }

        $this->storeBewoner();
        $this->storeUsers();
        $this->storeProcesInstances();

        $this->emit('stored');
        $this->invulbaar = false;

        Log::info("Project {$this->project->projectnummer} aangemaakt.", [
            'project_id' => $this->project->projectnummer,
            'stap_id' => $this->stap->id,
        ]);
    }
    private function storeAdres(){
        //Only store/save new adressen
        if(!$this->adres->id){
            $this->adres->save();
        }
    }
    private function storeProject(){
        $this->project->fill([
            'adres_id' => $this->adres->id,
            'proces_id' => $this->proces->id,
            'stap_id' => $this->stap->id
        ]);
        $this->project->save();
        $this->project->completeStap( $this->stap->id );
    }
    private function storeProjectenPlanmatig(){
        $planmatig_key = randomCharacters();

        $this->project->fill([
            'planmatig_key' => $planmatig_key,
            'proces_id' => $this->proces->id,
            'stap_id' => $this->stap->id
        ]);

        foreach($this->getAdressen() as $adres){
            $project = $this->project->replicate();
            $project->adres_id = $adres->id;
            $project->save();

            $project->completeStap( $this->stap->id );
        }

        $this->planmatig_projecten = Projecten::where('planmatig_key', $planmatig_key)->get();
    }
    private function storeBewoner(){
        foreach($this->getProjecten() as $project){
            $bewoner = new ProjectenBewoners(['project_id' => $project->id]);
            $existing_bewoner = ProjectenBewoners::whereHas('project', function($query) use ($project){
                $query->where('adres_id', $project->adres->id);
            })->first();

            if($existing_bewoner){
                $bewoner = $existing_bewoner->replicate();
                $bewoner->project_id = $project->id;
            }

            $bewoner->save();
        }
    }
    private function storeUsers(){
        $this->_gebruikers[_role()->id] = _user()->id;

        foreach($this->getProjecten() as $project){
            foreach($this->_gebruikers as $role_key => $user_id){
                ProjectenGebruikers::insert([
                    'project_id' => $project->id,
                    'user_id' => $user_id,
                    'role_id' => $role_key,
                ]);
            }
            foreach($this->_contacten as $role_key => $contact_ids){
                foreach($contact_ids as $contact_id){
                    ProjectenContacten::insert([
                        'project_id' => $project->id,
                        'contact_id' => $contact_id,
                        'role_id' => $role_key,
                    ]);
                }
            }
        }

    }
    private function storeProcesInstances(){
        foreach($this->getProjecten() as $project){

            if($this->asbest_sanering){
                $this->asbest_sanering->replicate()->fill([
                    'project_id' => $project->id,
                ])->save();
            }
            if($this->opdrachtformulier){
                $this->opdrachtformulier->replicate()->fill([
                    'project_id' => $project->id,
                    'adres_id' => $project->adres_id,
                    'guid' => guid(),
                ])->save();
            }

        }
    }

    //Instances
    public function getProjecten(){
        if($this->planmatig){
            return $this->planmatig_projecten;
        }
        else{
            return collect([ $this->project ]);
        }
    }
    public function getAdressen(){
        if($this->planmatig){
            return $this->planmatig_adressen;
        }
        else{
            return collect([ $this->adres ]);
        }
    }

    //Adressen
    public function setAdres($id){
        if(!$id){
            $this->adres = new Adressen();
            $this->adres_projecten = [];
            return;
        }

        $this->adres = Adressen::find($id);
        $this->adres_projecten = $this->adres->projecten->where('status', '!=', ProjectStatus::AFGEROND);
    }
    public function setPlanmatigAdres($adres_id){
        $adressen_ids = [$adres_id];

        if($this->planmatig_adressen instanceof Collection){
            $adressen_ids = array_merge(
                $adressen_ids,
                $this->planmatig_adressen->pluck('id')->toArray()
            );
        }

        $this->planmatig_adressen = Adressen::whereIn('id', $adressen_ids)->get();
    }
    public function removePlanmatigAdres($adres_id){
        $adressen_ids = $this->planmatig_adressen->pluck('id')->toArray();
        $adressen_ids = array_filter($adressen_ids, function($id) use ($adres_id){ return $id != $adres_id; });

        $this->planmatig_adressen = Adressen::whereIn('id', resetIndex($adressen_ids))->get();
    }

    //Gebruikers
    public function setGebruiker($user_id, $role_id){
        $this->_gebruikers[$role_id] = $user_id;
    }
    public function setContact($contact_ids, $role_id){
        $contact_ids = array_column($contact_ids, 'value');
        $this->_contacten[$role_id] = $contact_ids;
    }

    //Definitions
    private function defineProcesInstances(){
        if($this->proces->hasContext('asbest_sanering')){
            $this->asbest_sanering = new ProjectenAsbestSanering();
        }
        if($this->proces->hasContext('opdrachtformulier')){
            $this->opdrachtformulier = new ProjectenOpdrachtformulieren();
        }

    }
    private function defineRules(){
        //Project
        foreach($this->project_inputs as $input){
            $required = intval($input->verplicht) ? 'required' : '';
            $this->rules['project.' . $input->db_input->veldnaam] = $required;
        }

        //Roles
        foreach($this->gebruikers_toevoegen as $role){
            $this->rules['_gebruikers.' . $role->id] = 'required';
            $this->messages['_gebruikers.' . $role->id . '.required'] = "Selecteer een $role->name";
        }

        //Adres
        if(!$this->planmatig){
            foreach($this->adres_inputs as $input){
                $required = intval($input->verplicht) ? 'required' : '';
                $this->rules['adres.' . $input->db_input->veldnaam] = $required;
            }
        }
    }
}
