<?php

namespace App\Http\Livewire\Projecten\Stappen;

use App\Classes\BaseStapInvullenComponent;
use App\Models\Files;
use App\Models\AsbestBronnen;
use App\Models\AsbestBronnenReferenties;
use App\Models\AsbestBronnenReferentiesAdressen;
use App\Models\InstellingenStappenGebruikersModifiers;
use App\Models\Projecten;
use App\Models\ProjectenAsbestSanering;
use App\Models\ProjectenBewoners;
use App\Models\ProjectenContacten;
use App\Models\ProjectenFiles;
use App\Models\ProjectenGebruikers;
use App\Models\ProjectenOpdrachtformulieren;
use App\Models\ProjectenOpdrachtformulierenLocaties;
use App\Models\ProjectenOpdrachtformulierenLocatiesOmschrijvingen;
use App\Models\ProjectenStappen;
use App\Models\Stappen;
use App\Models\StappenApi;
use App\Models\User;

use Illuminate\Support\Collection;

use Livewire\WithFileUploads;
use Illuminate\Validation\ValidationException;
use ProjectStapContext;
use StappenApiType;

class Inputs extends BaseStapInvullenComponent{
    use WithFileUploads;

    public $project;
    public $next_stap;

    public $inputs;
    public $project_stap;

    public $stored = false;
    public $invulbaar;

    //Opdrachtformulier
    public $opdrachtformulier;
    public $opdrachtformulier_locaties_koppeling;

    //Gebruikers
    public $gebruikers;
    public $gebruikers_modifiers;
    public $contacten;

    //Inputs Instances
    public $adres;
    public $bewoner;
    public $projecten_files;
    public $asbest_sanering;

    //Asbest Proces
    public $asbest_bronnen;
    public $referentie_bronnen;
    public $historische_bronnen;
    public $referentie_bronnen_adressen;

    //Planmatig
    public $planmatige_projecten;
    public $planmatige_bewoners;

    //Livewire
    public $rules = [
        'project_stap.opmerking' => '',
    ];
    public $messages = [];
    public $listeners = [
        'setValue',
        'setGebruiker',
        'setContacten',
        'setReferentieBronAdres',
        'setOpdrachtformulierLocatie',
        'setOpdrachtformulierBron',
        'setPlanmatigeProjecten',
    ];

    //Lifecycle
    public function mount($manager){
        parent::baseMount($manager);

        $this->inputs = $manager->inputs;
        $this->gebruikers = $manager->gebruikers_toevoegen;
        $this->gebruikers_modifiers = $manager->gebruikers_modifiers;
        $this->contacten = $manager->gebruikers_contact;

        $this->defineProcesInstances();
        $this->defineOpdrachtformulierBronLocaties();
        $this->defineProjectStap();
        $this->defineProjectenFiles();
        $this->defineAsbestBronnen();
        $this->defineReferentieBronnen();
        $this->defineHistorischeBronnen();
        $this->definePlanmatigeProjecten();
        $this->definePlanmatigeBewoners();
        $this->defineRules();

        $this->prefillInputs();
    }
    public function updated($model, $value){
        $this->validateInvulbaar();

        $model_attrs = explode('.', $model);
        $instance_name = $model_attrs[0];

        if($this->targetedUpdateExists($instance_name)){
            return true;
        }

//        $this->validateOnly($model);


        $instance = data_get($this, $instance_name);
        if($instance instanceof Collection){
            $instance = $instance[$model_attrs[1]];
        }

        $instance->save();
    }
    public function render(){
        return view('livewire.projecten.stappen.inputs');
    }

    //Targeted Update Lifecycle
    public function updatedProjectenFiles($files, $file_key){
        foreach($files as $file){
            $file_instance = Files::storeFile([
                'file' => $file,
                'path' => "/{$file_key}"
            ]);
            ProjectenFiles::insert([
                'project_id' => $this->project->id,
                'file_id' => $file_instance->id,
                'file_key' => $file_key,
            ]);
        }
    }
    public function updatedAsbestBronnen($value, $model){
        list($index, $attribute) = explode('.', $model);
        $bron = $this->asbest_bronnen[$index];
        $bron->save();

        if($attribute == 'referentie'){
            $this->toggleReferentieAsbestBron($bron->id, $value);
        }
        if($this->planmatig){
            AsbestBronnen::where('planmatig_parent', $bron->id)->update([$attribute => $value]);
        }

    }

    //Validation
    public function validateUsers(){
        foreach($this->gebruikers as $gebruikers_role){
            $exists = ProjectenGebruikers::where([
                'role_id' => $gebruikers_role->id,
                'project_id' => $this->project->id,
            ])->exists();
            if(!$exists){
                throw ValidationException::withMessages(['name' => 'Selecteer een '.$gebruikers_role->name]);
            }
        }
        foreach($this->contacten as $contacten_role){
            $exists = ProjectenContacten::where([
                'role_id' => $contacten_role->id,
                'project_id' => $this->project->id,
            ])->exists();
            if(!$exists){
                throw ValidationException::withMessages(['name' => 'Selecteer een '.$contacten_role->name]);
            }
        }
    }
    public function validateInvulbaar(){
        if(!$this->invulbaar){
            $this->emit('notification', 'U kunt hier geen wijzigingen aanbrengen', 'warning');
            throw ValidationException::withMessages(['name' => 'U kunt hier geen wijzigingen aanbrengen']);
        }
    }
    public function validateDisabledSelectOptions(){
        $all_inputs = $this->inputs;
        foreach($this->inputs as $input){
            $all_inputs= $all_inputs->merge($input->sub_inputs);
        }

        foreach($all_inputs as $input){
            $db_input = $input->db_input;
            if($db_input->type != 'select'){ continue; }

            if(!$db_input->is_array){
                $value = data_get($this, $input->model());
                $is_disabled = $input->isOptionDisabled($value);

                if($is_disabled){
                    throw ValidationException::withMessages(['name' => "Geselecteerde optie is niet toegestaan! <b>{$db_input->titel}</b>"]);
                }

                continue;
            }

            foreach(data_get($this, $db_input->context) as $index => $row){
                $value = data_get($this, $input->model($index));
                $is_disabled = $input->isOptionDisabled($value);

                if($is_disabled){
                    throw ValidationException::withMessages(['name' => "Geselecteerde optie is niet toegestaan! <b>{$db_input->titel} Regel ".($index + 1)."</b>"]);
                }
            }

        }

    }
    public function validatePlanmatigProject(){
        if($this->planmatig && !$this->planmatige_projecten->count()){
            throw ValidationException::withMessages(['name' => "Selecteer minstens een planmatig project!"]);
        }

    }

    //Context
    public function contextVisible($context){
        $inputs = $this->inputs->where('db_input.context', $context);
        foreach($inputs as $input){
            if($this->inputVisible($input)){ return true; }
        }
        return false;
    }

    //Inputs
    public function inputExists($context, $veldnaam = null){
        $inputs = $this->inputs->where('db_input.context', $context);
        if(!$veldnaam){
            return !!$inputs->count();
        }

        $inputs = $inputs->where('db_input.veldnaam', $veldnaam);
        return !!$inputs->count();
    }
    public function inputVisible($input){
        $db_input = $input->db_input;
        $data = json_decode($db_input->data ?? '{}');

        if(!isset($data->visible)){ return true; }

        $parent = $data->visible->parent ?? null;
        $equals = $data->visible->equals ?? null;

        $value = data_get($this, $parent);
        return $value == $equals;
    }
    public function inputValue($input){
        $db_input = $input->db_input;
        return data_get($this, "{$db_input->context}.{$db_input->veldnaam}");
    }

    //Store
    public function store(){
        //Validation
        $this->validate();
        $this->validateUsers();
        $this->validateDisabledSelectOptions();
        $this->validatePlanmatigProject();

        //Definitions to refresh the data
        $this->defineNextStap();

        //Custom exceptions
        $this->checkSettingsExceptions();

        //Store
        $this->stored = true;
        $this->planmatig ? $this->storePlanmatig() : $this->storeRegular();

        $this->executeApis();

        $this->emit('stored');
    }
    private function storeRegular(){
        //Save all instances
        $this->project->save();
        $this->bewoner->save();
        $this->adres->save();
        if($this->opdrachtformulier){
            $this->opdrachtformulier->save();
        }

        $this->project->completeStap($this->stap->id, optional($this->next_stap)->id);
    }
    private function storePlanmatig(){
        $this->storePlanmatigUsers();
        $this->storePlanmatigContacten();
        $this->storePlanmatigInputs();
        $this->storePlanmatigOpdrachtformulierLocaties();

        foreach($this->planmatige_projecten as $project){
            $project->completeStap($this->stap->id, optional($this->next_stap)->id);
        }
    }
    private function storePlanmatigUsers(){
        $users = ProjectenGebruikers::where('project_id', $this->project->id)->get();

        foreach($this->planmatige_projecten as $project){
            foreach($users as $user){
                ProjectenGebruikers::updateOrInsert([
                    'role_id' => $user->role_id,
                    'project_id' => $project->id,
                ], [
                    'user_id' => $user->user_id,
                ]);
            }
        }
    }
    private function storePlanmatigContacten(){
        $contacten = ProjectenContacten::where('project_id', $this->project->id)->get();

        foreach($this->planmatige_projecten as $project){
            ProjectenContacten::where('project_id', $project->id)->delete();
            foreach($contacten as $contact){
                ProjectenContacten::insert([
                    'role_id' => $contact->role_id,
                    'contact_id' => $contact->contact_id,
                    'project_id' => $project->id,
                ]);
            }
        }

    }
    private function storePlanmatigOpdrachtformulierLocaties(){
        if(!$this->inputExists('opdrachtformulier')){ return; }

        foreach($this->planmatige_projecten as $project){
            if($project->id == $this->project->id){ continue; }

            $project->opdrachtformulier->clearLocaties();
            foreach($this->opdrachtformulier_locaties_koppeling as $locatie_koppeling){
                $locatie_koppeling_id = ProjectenOpdrachtformulierenLocaties::insertGetId([
                    'opdrachtformulier_id' => $project->opdrachtformulier->id,
                    'locatie_id' => $locatie_koppeling->locatie_id,
                    'opmerking' => $locatie_koppeling->opmerking,
                ]);

                $locaties_bronnen_data = $locatie_koppeling->bron_omschrijvingen_koppeling->map(function($locatie_bron) use ($locatie_koppeling_id) {
                    return [
                        'opdrachtformulier_locatie_id' => $locatie_koppeling_id,
                        'bron_id' => $locatie_bron->bron_id,
                    ];
                });

                ProjectenOpdrachtformulierenLocatiesOmschrijvingen::insert($locaties_bronnen_data->toArray());
            }
        }
    }
    private function storePlanmatigInputs(){
        foreach($this->rules as $model => $rule){

            list($instance) = explode('.', $model);
            switch($instance){
                case ProjectStapContext::PROJECT: $this->storePlanmatigInput($model, Projecten::class, 'id'); break;
                case ProjectStapContext::OPDRACHTFORMULIER: $this->storePlanmatigInput($model, ProjectenOpdrachtformulieren::class); break;
                case ProjectStapContext::ASBEST_SANERING: $this->storePlanmatigInput($model, ProjectenAsbestSanering::class); break;
                case ProjectStapContext::PROJECT_STAP: $this->storePlanmatigProjectStapInput($model); break;
                case ProjectStapContext::PROJECTEN_FILES: $this->storePlanmatigProjectFilesInput($model); break;
            }

        }
    }
    private function storePlanmatigInput($model, $class, $key = 'project_id'){
        $class::whereIn($key, $this->planmatigeProjectenIds() )->update( $this->modelToUpdateFormat($model) );
    }
    private function storePlanmatigProjectStapInput($model){
        foreach($this->planmatige_projecten as $project){
            ProjectenStappen::updateOrInsert(['project_id' => $project->id, 'stap_id' => $this->stap->id], ['user_id' => _user()->id]);
        }

        ProjectenStappen::whereIn('project_id', $this->planmatigeProjectenIds() )->where('stap_id', $this->stap->id)->update( $this->modelToUpdateFormat($model) );
    }
    private function storePlanmatigProjectFilesInput($model){
        list($instance, $attribute) = explode('.', $model);

        $files = $this->project->getFiles($attribute);
        ProjectenFiles::whereIn('project_id', $this->planmatigeProjectenIds() )->where('file_key', $attribute)->delete();

        $projecten_data = [];
        foreach($this->planmatige_projecten as $project){
            foreach($files as $file){
                $projecten_data[] = [
                    'project_id' => $project->id,
                    'file_id' => $file->id,
                    'file_key' => $attribute
                ];
            }
        }

        ProjectenFiles::insert($projecten_data);
    }

    //Revert
    public function revert($projecten, $stap_id, $reason){
        $stap = Stappen::find($stap_id);
        Projecten::whereIn('id', $projecten)->each(function($project) use ($stap, $reason){
            $project->revertStap($stap->id, $reason);
        });
    }

    //Reject
    public function reject($reason){
        $data = $this->getStapSettingData('STEP_REJECT');
        if(!isset($data->back_to_step_id)){
            $this->emit('notification', 'Afwijzen niet mogelijk, back_to_step_id is niet gedefinieerd!', 'danger');
            return;
        }

        $this->planmatig ? $this->rejectPlanmatig($reason) : $this->rejectRegular($reason);
        $this->emit('rejected');
    }
    public function rejectRegular($reason){
        $data = $this->getStapSettingData('STEP_REJECT');
        $this->project->rejectStap($data->back_to_step_id, $reason);
    }
    public function rejectPlanmatig($reason){
        $data = $this->getStapSettingData('STEP_REJECT');
        foreach($this->planmatige_projecten as $project){
            $project->rejectStap($data->back_to_step_id, $reason);
        }
    }

    //API's
    public function executeApis(){
        $apis = $this->project->stapApis( $this->stap->id, $this->planmatig, StappenApiType::OUTBOUND );

        if($this->planmatig){
            $apis->map(function($api){
                $api->execute([
                    'projecten' => $this->planmatige_projecten,
                    'planmatig' => true,
                ]);
            });
        }
        else{
            $apis->map(function($api){
                $api->execute([
                    'project' => $this->project
                ]);
            });
        }
    }

    //Gebruikers & Contacten
    public function getModifiedGebruikers($role_id){
        $users = getUsersByRol($role_id);

        $modifiers = $this->gebruikers_modifiers->where('role_id', $role_id);
        foreach($modifiers as $modifier){
            $users = $modifier->apply($users, $this->project->id);
        }

        return $users;
    }

    public function setGebruiker($user_id, $params){
        $this->project->setGebruiker($params['role_id'], $user_id);
    }
    public function hasGebruiker($user_id, $role_id){
        return ProjectenGebruikers::where([
            'user_id' => $user_id,
            'role_id' => $role_id,
            'project_id' => $this->project->id,
        ])->exists();
    }
    public function setContacten($contact_ids, $params){
        $contact_ids = array_column($contact_ids, 'value');

        ProjectenContacten::where(['role_id' => $params['role_id'], 'project_id' => $this->project->id])->delete();
        foreach($contact_ids as $user_id){
            ProjectenContacten::insert([
                'role_id' => $params['role_id'],
                'contact_id' => $user_id,
                'project_id' => $this->project->id,
            ]);
        }
    }
    public function hasContact($contact_id, $role_id){
        return ProjectenContacten::where([
            'contact_id' => $contact_id,
            'role_id' => $role_id,
            'project_id' => $this->project->id,
        ])->exists();
    }

    //Asbest Bronnen
    public function addAsbestBron(){
        $data = $this->planmatig
            ? ['planmatig_key' => $this->project->planmatig_key]
            : ['project_id' => $this->project->id,  'adres_id' => $this->project->adres_id];

        AsbestBronnen::insert($data);
        $this->defineAsbestBronnen();
    }
    public function deleteAsbestBron($bron_id){
        $this->validateInvulbaar();

        $bron = $this->asbest_bronnen->find($bron_id);
        $bron->setInactive();

        $this->toggleReferentieAsbestBron($bron->id, false);
        $this->defineAsbestBronnen();
    }

    //Asbest Bronnen Deelsanering
    public function deelsaneringAsbestBron($bron_id){
        $this->validateInvulbaar();

        $bron = $this->asbest_bronnen->find($bron_id);
        $bron->createDeelsanering();

        $this->defineAsbestBronnen();
    }
    public function deleteDeelsaneringAsbestBron($bron_id){
        $this->validateInvulbaar();

        $bron = $this->asbest_bronnen->find($bron_id);
        $bron->deleteDeelsanering();

        $this->defineAsbestBronnen();
    }
    public function setDeelsaneringAantal($bron_id, $aantal){
        $bron = $this->asbest_bronnen->find($bron_id);
        $deelsanering_bron = $bron->deelsanering_child;

        $diff = $deelsanering_bron->aantal - floatval($aantal);

        if($bron->aantal + $diff < 0){
            $this->emit('notification', 'Deelsanering aantal kan niet groter zijn dan het bron aantal!', 'warning');
            $this->emit('refreshInput', "[data-deelsanering_aantal={$bron_id}]");
        }
        else{
            $bron->aantal += $diff;
            $deelsanering_bron->aantal = $aantal;

            $bron->save();
            $deelsanering_bron->save();
        }

        $this->defineAsbestBronnen();
    }

    //Referentie bronnen
    public function hasReferentieBron($adres_id, $bron_id){
        // ->where([]) does not work on collections
        return !!$this->referentie_bronnen
            ->where('adres_id', $adres_id)
            ->where('bron_id', $bron_id)
            ->where('project_id', $this->project->id)
            ->count();
    }
    public function setReferentieBron($adres_id, $bron_id, $referentie_adres_id = null){
        //Delete temporary records
        $exists = $this->hasReferentieBron($adres_id, $bron_id);

        $query = [
            'adres_id' => $adres_id,
            'bron_id' => $bron_id,
            'project_id' => $this->project->id,
            'referentie_adres_id' => $referentie_adres_id,
        ];

        if($exists){
            AsbestBronnenReferenties::where($query)->delete();
        }
        else{
            AsbestBronnenReferenties::insert($query);
        }

        $this->defineReferentieBronnen();
    }
    public function toggleReferentieAsbestBron($bron_id, $state){
        if(!is_bool($state)){ $state = !!intval($state); }

        if($state){

            //Append all Complex adres / referentie bron relations
            $this->adres->complexAdressen()->map(function($complex_adres) use ($bron_id){
                AsbestBronnenReferenties::insert([
                    'project_id' => $this->project->id,
                    'adres_id' => $complex_adres->id,
                    'bron_id' => $bron_id,
                ]);
            });

            //Append all losse adres / referentie bron relations
            $this->referentie_bronnen_adressen->map(function($referentie_adres) use ($bron_id){
                AsbestBronnenReferenties::insert([
                    'project_id' => $this->project->id,
                    'adres_id' => $referentie_adres->adres_id,
                    'bron_id' => $bron_id,
                    'referentie_adres_id' => $referentie_adres->id,
                ]);
            });

        }
        else{
            AsbestBronnenReferenties::where([
                'project_id' => $this->project->id,
                'bron_id' => $bron_id,
            ])->delete();
        }

        $this->defineReferentieBronnen();
    }

    //Referentie bronnen Adressen
    public function addReferentieBronAdres(){
        $referentie_adres_id = AsbestBronnenReferentiesAdressen::insertGetId([
            'project_id' => $this->project->id,
        ]);
        $referentie_adres = AsbestBronnenReferentiesAdressen::find($referentie_adres_id);

        //Append all losse adres / referentie bron relations
        $this->asbest_bronnen->where('referentie', 1)->map(function($bron) use ($referentie_adres){
            AsbestBronnenReferenties::insert([
                'project_id' => $this->project->id,
                'adres_id' => $referentie_adres->adres_id,
                'bron_id' => $bron->id,
                'referentie_adres_id' => $referentie_adres->id,
            ]);
        });

        $this->defineReferentieBronnen();
    }
    public function deleteReferentieBronAdres($referentie_bron_adres_id){
        AsbestBronnenReferentiesAdressen::where('id', $referentie_bron_adres_id)->delete();
        AsbestBronnenReferenties::where('referentie_adres_id', $referentie_bron_adres_id)->delete();

        $this->defineReferentieBronnen();
    }
    public function resetReferentieBronAdres($referentie_bron_adres_id){
        AsbestBronnenReferentiesAdressen::where('id', $referentie_bron_adres_id)->update(['adres_id' => null]);
        AsbestBronnenReferenties::where('referentie_adres_id', $referentie_bron_adres_id)->update(['adres_id' => null]);
        $this->defineReferentieBronnen();
    }
    public function setReferentieBronAdres($adres_id, $params){
        $adres_in_complex = $this->referentie_bronnen->where('adres_id', $adres_id)->first();
        $adres_added = $this->referentie_bronnen_adressen->where('adres_id', $adres_id)->first();
        if($adres_in_complex || $adres_added){
            $this->emit('notification', 'Dit adres is al toegevoegd!', 'warning');
            return true;
        }

        $referentie_adres_id = $params['referentie_adres_id'];

        AsbestBronnenReferentiesAdressen::where('id', $referentie_adres_id)->update(['adres_id' => $adres_id]);
        AsbestBronnenReferenties::where('referentie_adres_id', $referentie_adres_id)->update(['adres_id' => $adres_id]);

        $this->defineReferentieBronnen();
    }

    //Historische bronnen
    public function transferHistorischeBron($bron_id, $toggle_referentie = false){
        $this->validateInvulbaar();

        $bron = $this->historische_bronnen->find($bron_id);

        $bron->transferToProject(
            $this->planmatig
                ? $this->project->planmatig_key
                : $this->project->id,
        );

        $this->defineAsbestBronnen();
        $this->defineHistorischeBronnen();

        if($toggle_referentie){
            $this->toggleReferentieAsbestBron($bron->id, $bron->referentie);
        }
    }
    public function revertHistorischeBron($bron_id){
        $this->validateInvulbaar();

        $bron = $this->asbest_bronnen->find($bron_id);

        $this->toggleReferentieAsbestBron($bron->id, false);
        $bron->revertLastTransfer();

        if($this->planmatig){
            AsbestBronnen::where(['planmatig_parent' => $bron->id, 'active' => 1])->get()->map(function($bron){ $bron->setInActive(); });
        }

        $this->defineAsbestBronnen();
        $this->defineHistorischeBronnen();
    }
    public function compleetHistorischeBron($bron_id){
        $this->validateInvulbaar();

        $bron = $this->historische_bronnen->find($bron_id);

        $bron->gesaneerd = 1;
        $bron->save();

        $this->defineHistorischeBronnen();
    }

    //Opdrachtformulier bronnen
    public function hasOpdrachtformulierBronLocatie($locatie_id, $bron_id){
        $locatie_koppeling = $this->opdrachtformulier_locaties_koppeling->where('locatie_id', $locatie_id)->first();
        if(!$locatie_koppeling){
            return false;
        }

        $bron_omschrijving_koppeling = $locatie_koppeling->bron_omschrijvingen_koppeling->where('bron_id', $bron_id)->first();
        if(!$bron_omschrijving_koppeling){
            return false;
        }

        return true;
    }
    public function addOpdrachtformulierLocatie(){
        ProjectenOpdrachtformulierenLocaties::insert([
            'opdrachtformulier_id' => $this->opdrachtformulier->id,
        ]);

        $this->defineOpdrachtformulierBronLocaties();
    }
    public function deleteOpdrachtformulierLocatie($opdrachtformulier_locatie_id){
        ProjectenOpdrachtformulierenLocaties::where([
            'id' => $opdrachtformulier_locatie_id,
        ])->delete();

        ProjectenOpdrachtformulierenLocatiesOmschrijvingen::where([
            'opdrachtformulier_locatie_id' => $opdrachtformulier_locatie_id,
        ])->delete();

        $this->defineOpdrachtformulierBronLocaties();
    }
    public function setOpdrachtformulierLocatie($locatie_id, $params){
        ProjectenOpdrachtformulierenLocaties::where('id', $params['locatie_koppeling_id'])->update([
            'locatie_id' => $locatie_id,
        ]);

        $this->defineOpdrachtformulierBronLocaties();
    }
    public function setOpdrachtformulierBron($locatie_id, $bron_id){
        $exists = $this->hasOpdrachtformulierBronLocatie($locatie_id, $bron_id);
        $locatie_koppeling = $this->opdrachtformulier_locaties_koppeling->where('locatie_id', $locatie_id)->first();

        if($exists){
            ProjectenOpdrachtformulierenLocatiesOmschrijvingen::where([
                'opdrachtformulier_locatie_id' => $locatie_koppeling->id,
                'bron_id' => $bron_id,
            ])->delete();
        }
        else{
            ProjectenOpdrachtformulierenLocatiesOmschrijvingen::insert([
                'opdrachtformulier_locatie_id' => $locatie_koppeling->id,
                'bron_id' => $bron_id,
            ]);
        }


        $this->defineOpdrachtformulierBronLocaties();
    }

    //Definitions
    private function defineProcesInstances(){
        if($this->proces->hasContext('asbest_sanering')){
            $this->asbest_sanering = ProjectenAsbestSanering::where('project_id', $this->project->id)->first();
        }
        if($this->proces->hasContext('opdrachtformulier')){
            $this->opdrachtformulier = ProjectenOpdrachtformulieren::where('project_id', $this->project->id)->first();
        }

    }
    private function defineOpdrachtformulierBronLocaties(){
        if(!$this->inputExists('opdrachtformulier')){ return; }

        $this->opdrachtformulier_locaties_koppeling = ProjectenOpdrachtformulierenLocaties::where('opdrachtformulier_id', $this->opdrachtformulier->id)->get();
    }
    private function defineProjectStap(){
        $this->project_stap = ProjectenStappen::where(['project_id' => $this->project->id, 'stap_id' => $this->stap->id])->first();
        if(!$this->project_stap){
            $this->project_stap = new ProjectenStappen([
                'project_id' => $this->project->id,
                'stap_id' => $this->stap->id,
                'user_id' => User::_id(),
            ]);
            $this->project_stap->save();
        }
    }
    private function defineProjectenFiles(){
        foreach($this->inputs->where('db_input.type', 'file') as $input){
            $db_input = $input->db_input;
            $id = $db_input->context . '.' . $db_input->veldnaam;

            data_set($this, "$id", $this->project->getFiles($db_input->veldnaam));
        }
    }
    private function defineAsbestBronnen(){
        if(!$this->inputExists('asbest_bronnen')){ return; }

        $data = $this->planmatig
            ? ['planmatig_key' => $this->project->planmatig_key, 'deelsanering' => 0]
            : ['project_id' => $this->project->id,  'deelsanering' => 0];

        $this->asbest_bronnen = AsbestBronnen::where($data)->orderBy('bronnummer', 'ASC')->get();
    }
    private function defineReferentieBronnen(){
        if(!$this->inputExists('referentie_bronnen')){ return; }

        $this->referentie_bronnen = AsbestBronnenReferenties::where('project_id', $this->project->id)->get();
        $this->referentie_bronnen_adressen = AsbestBronnenReferentiesAdressen::where('project_id', $this->project->id)->get();
    }
    private function defineHistorischeBronnen(){
        if(!$this->inputExists('historische_bronnen')){ return; }

        $input = $this->inputs->where('db_input.context', 'historische_bronnen')->first();
        $data = json_decode($input->db_input->data);

        $query = AsbestBronnen::where([
            'adres_id' => $this->adres->id,
            'gesaneerd' => 0,
            'asbesthoudend' => 1,
        ])
            ->where('project_id', '!=', $this->project->id)
            ->whereHas('project', function($query){ $query->where('status', 'AFGEROND'); });

        if($data->valid_report ?? false){
            $query = $query->where('created_at', '>=',  Carbon()->subYears($data->validity_in_years));
        }

        //TODO Compleet the query?
        $this->historische_bronnen = $query->orderBy('bronnummer', 'ASC')->get();
    }
    private function defineRules(){
        //Inputs
        foreach($this->inputs as $input){
            foreach($input->sub_inputs ?? [] as $sub_input){
                $this->defineInputRule($sub_input);
            }
            if(count($input->sub_inputs)){
                continue;
            }
            $this->defineInputRule($input);
        }

        if($this->inputExists('opdrachtformulier')){
            $this->rules['opdrachtformulier_locaties_koppeling.*.opmerking'] = '';
        }
    }
    private function defineInputRule($input){
        $db_input = $input->db_input;

        if(!$db_input->veldnaam || $db_input->type == 'readonly'){
            return;
        }

        $is_array = intval($db_input->is_array) ? '*.' : '';
        $required = intval($input->verplicht) ? 'required' : '';
        $model = $db_input->context . '.' . $is_array . $db_input->veldnaam;

        $this->rules[$model] = $required;
        $this->messages[$model . ".required"] = "$db_input->titel is verplicht!";
    }
    private function defineNextStap(){
        $this->next_stap = $this->project->nextStap();
    }
    private function definePlanmatigeProjecten(){
        if(!$this->planmatig){ return; }

        $this->planmatige_projecten = Projecten::where('id', $this->project->id)->get();
    }
    private function definePlanmatigeBewoners(){
        if(!$this->planmatig){ return; }

        $bewoners_ids = $this->planmatige_projecten->pluck('id')->toArray();
        $this->planmatige_bewoners = ProjectenBewoners::whereIn('project_id', $bewoners_ids)->get();
    }

    //Prefill
    private function prefillInputs(){
        if(!$this->invulbaar){ return; }

        foreach($this->inputs as $input){
            if(!$input->sub_inputs->count()){
                $this->prefillInput($input);
                continue;
            }
            foreach($input->sub_inputs as $sub_input){
                $this->prefillInput($sub_input);
            }
        }
    }
    private function prefillInput($input){
            if(!$input->dataValue('prefill')){ return; }
            if(data_get($this, $input->model())){ return; }

            $prefill_value = data_get($this, $input->dataValue('prefill'));

            data_set($this, $input->model(), $prefill_value);
            $this->updated($input->model(), $prefill_value);
    }

    //Files
    public function removeProjectenFile($file_key, $file_id){
        ProjectenFiles::where([
            'project_id' => $this->project->id,
            'file_id' => $file_id,
            'file_key' => $file_key,
        ])->delete();
        $this->defineProjectenFiles();
    }

    //Utilities
    private function modelToUpdateFormat($model){
        list($instance, $attribute) = explode('.', $model);

        return [
            $attribute => data_get($this, $model),
        ];
    }
    private function targetedUpdateExists($instance_name){
        $instance_name = str_replace('_', ' ', $instance_name);
        $instance_name = ucwords($instance_name);
        $instance_name = str_replace(' ', '', $instance_name);
        $fn_name = "updated$instance_name";

        return method_exists($this, $fn_name);
    }
    private function assert($condition, $message){
        if(!$condition){
            $this->throwMessage($message);
        }
    }
    private function throwMessage($message){
        throw ValidationException::withMessages(['name' => $message]);
    }

    //Exceptions
    private function checkSettingsExceptions(){
        $this->exceptionsSkipStep();
    }
    private function exceptionsSkipStep(){
        if(!$this->getStapSettingValue('SKIP_STEPS')){ return; }

        $data = $this->getStapSettingData('SKIP_STEPS');

        //Verify conditions, if met, skips to the given stap, Condition example:
        //condition.attribute: "asbest_bronnen.*.asbesthoudend"
        //condition.operator: "=="
        //condition.value: "0"
        foreach($data->conditions as $condition){
            $model_data = data_get($this, $condition->attribute);
            $this->assert($model_data, "Conditie <b>{$condition->attribute} {$condition->operator} {$condition->value}</b> kan niet worden geverifieerd");

            if(!is_array($model_data)){ $model_data = [$model_data]; }

            foreach($model_data as $attribute){
                if(!$attribute){ return; }
                $expression = "return '{$condition->value}' {$condition->operator} '{$attribute}';";

                if(!eval($expression)){ return; }
            }

            $next_stap = Stappen::find($data->skip_to);
            $this->assert($next_stap, "Stap <b>({$data->skip_to})</b> niet gevonden!");

            $this->next_stap = $next_stap;
        }
    }

    //Planmatige Projecten
    public function setPlanmatigeProjecten($values){
        $this->planmatige_projecten = Projecten::whereIn(
            'id',
            array_column($values, 'value')
        )->get();

        $this->definePlanmatigeBewoners();
    }
    public function planmatigeProjectenIds(){
        return $this->planmatige_projecten->pluck('id')->toArray();
    }

    //Planmatige Asbest Bronnen
    public function getPlanmatigeAsbestBron($bron_id, $project_id){
        return AsbestBronnen::where([
            'active' => 1,
            'project_id' => $project_id,
            'planmatig_parent' => $bron_id,
        ])->first();
    }
    public function setPlanatigAsbestBron($planmatig_project_id, $bron_id, $planmatig_bron_id, $aantal){
        if($planmatig_bron_id && !is_numeric($aantal)){
            //Delete if no aantal provided
            AsbestBronnen::where('id', $planmatig_bron_id)->first()->setInactive();
            return;
        }
        else if($planmatig_bron_id){
            //Update existing bron
            AsbestBronnen::where('id', $planmatig_bron_id)->update(['aantal'  => $aantal]);
            return;
        }

        //Create new bron
        $project = $this->planmatige_projecten->find($planmatig_project_id);
        $bron = $this->asbest_bronnen->find($bron_id);

        $bron = $bron->replicate();
        $bron->fill([
            'project_id' => $project->id,
            'adres_id' => $project->adres_id,
            'aantal' => $aantal,
            'planmatig_key' => null,
            'planmatig_parent' => $bron_id,
        ]);

        $bron->save();
    }

}
