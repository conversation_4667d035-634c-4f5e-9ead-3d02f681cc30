<?php

namespace App\Http\Livewire\Projecten\Stappen;

use App\Classes\BaseStapInvullenComponent;
use App\Models\InstellingenStappenVelden;
use App\Models\ProjectenAsbestSanering;
use App\Models\ProjectenGebruikers;
use App\Models\ProjectenOpdrachtformulieren;
use App\Models\Settings;
use App\Models\StappenVelden;
use Livewire\Component;

class Info extends BaseStapInvullenComponent{

    public $project;
    public $bewoner;
    public $adres;
    public $gebruikers;
    public $opdrachtformulier;
    public $asbest_sanering;
    public $projecten_files_inputs;

    public function mount($manager){
        parent::baseMount($manager);

        $this->gebruikers = $manager->gebruikers;

        $this->defineInputs();
        $this->defineProjectenFiles();
        $this->defineGebruikers();
        $this->defineProcesInstances();
    }
    public function render(){
        return view('livewire.projecten.stappen.info');
    }

    private function defineInputs(){
        $visible_proces_inputs = Settings::getValue('proces_'.$this->project->proces_id.'_overview_inputs', []);
        $this->inputs = StappenVelden::whereIn('id', $visible_proces_inputs)->whereNotIn('context', ['projecten_files'])->get();
    }
    private function defineProjectenFiles(){
        $visible_proces_inputs = Settings::getValue('proces_'.$this->project->proces_id.'_overview_inputs', []);
        $this->projecten_files_inputs = StappenVelden::whereIn('id', $visible_proces_inputs)->where('context', 'projecten_files')->get();
    }
    private function defineGebruikers(){
        $visible_roles = Settings::getValue('proces_'.$this->project->proces_id.'_overview_roles', []);
        $this->gebruikers = $this->gebruikers->whereIn('role.id', $visible_roles);
    }
    //Definitions
    private function defineProcesInstances(){
        if($this->proces->hasContext('asbest_sanering')){
            $this->asbest_sanering = ProjectenAsbestSanering::where('project_id', $this->project->id)->first();
        }
        if($this->proces->hasContext('opdrachtformulier')){
            $this->opdrachtformulier = ProjectenOpdrachtformulieren::where('project_id', $this->project->id)->first();
        }

    }

}
