<?php

namespace App\Http\Livewire\Rollen;

use App\Models\ClientRolesPermissions;
use App\Models\ClientUsersPermissions;
use App\Models\RolesPermissions;
use Livewire\Component;
use Log;

class Permissions extends Component{
	
	public $role;
	public $permissions;
	public $clientPermissions;

    //Lifecycle
	public function mount(){
		$this->definePermissions();
	}
	public function render(){
		return view('livewire.rollen.permissions');
	}

    //Role Permissions
	public function togglePermission($keyword){
        $has_permission = roleHasPermission($keyword, $this->role->id);
        ClientRolesPermissions::updateOrInsert([
            'role_id' => $this->role->id,
            'keyword' => $keyword
        ], [
            'active' => $has_permission ? 0 : 1
        ]);

        Log::notice("Permission van de rol {$this->role->name} is gewijzigd.", [
            'data' => [
                'permission' => RolesPermissions::name($keyword),
                'state' => !$has_permission
            ]
        ]);

		$this->definePermissions();
	}
	public function hasCategoryPermission($category){
        $permissions = $this->permissions->where('category', $category);
        $active_permissions = $permissions->filter(function($permission){
            return roleHasPermission($permission->keyword, $this->role->id);
        });

        if(!$active_permissions->count()){
            return 'NONE';
        }
        elseif($permissions->count() === $active_permissions->count()){
            return 'ALL';
        }
        else{
            return 'SOME';
        }
	}

    //Users permissions
    public function usersHavePermission($keyword, $based_on_difference = false){
        $model = new ClientUsersPermissions();
        $model = $model->where('keyword', $keyword)->whereIn('user_id', getUsersIdsByRol($this->role->id));

        if($based_on_difference){
            $model = $model->where(
                'active',
                roleHasPermission($keyword, $this->role->id) ? 0 : 1
            );
        }

        return $model->exists();
    }
    public function overrideUsersPermissions($keyword){
        ClientUsersPermissions::where(['keyword' => $keyword])
            ->whereIn('user_id', getUsersIdsByRol($this->role->id))
            ->update([
                'active' => roleHasPermission($keyword, $this->role->id) ? 1 : 0
            ]);

        Log::notice("Gebruiker specifieke permissions van de role {$this->role->name} zijn overschrijven.", [
            'data' => [
                'permission' => RolesPermissions::name($keyword),
                'state' => $this->usersHavePermission($keyword)
            ]
        ]);
    }
    public function deleteUsersPermissions($keyword){
        ClientUsersPermissions::where(['keyword' => $keyword])
            ->whereIn('user_id', getUsersIdsByRol($this->role->id))
            ->delete();

        Log::notice("Gebruiker specifieke permissions van de role {$this->role->name} zijn verwijderd.", [
            'data' => [
                'permission' => RolesPermissions::name($keyword),
            ]
        ]);
    }

    //Definitions
	private function definePermissions(){
		$this->permissions = RolesPermissions::orderBy('category', 'ASC')->orderBy('name', 'ASC')->get();
        $this->clientPermissions = ClientRolesPermissions::where([
            'role_id' => $this->role->id,
            'active' => 1
        ])->get();
    }

	
}
