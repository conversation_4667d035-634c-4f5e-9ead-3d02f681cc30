<?php

namespace App\Http\Livewire\Instellingen\Lijsten;

use App\Models\ProjectenBronlocaties;
use Livewire\Component;

class Locaties extends Component{
	
	public $locaties;
	
	protected $rules = [
		'locaties.*.locatie' => '',
		'locaties.*.binnen' => '',
		'locaties.*.bronnen' => '',
		'locaties.*.opdrachtformulier' => '',
	];
	
	public function mount(){
		$this->defineLocaties();
	}
	public function updated($model, $value){
		list($instance, $index, $attr) = explode('.', $model);
		$locatie = $this->locaties[$index];

		if(is_bool($value)){
			$locatie->setAttribute($attr, $value ? 1 : 0);
		}
		
		$locatie->save();
	}
	public function render(){
		return view('livewire.instellingen.lijsten.locaties');
	}
	
	//Crud
	public function addLocatie(){
		$locatie = new ProjectenBronlocaties();
		$locatie->save();
		
		$this->defineLocaties();
	}
	
	//Define
	public function defineLocaties(){
		$this->locaties = ProjectenBronlocaties::where('active', 1)->orderBy('locatie', 'ASC')->get();
		$this->locaties->map(function($locatie){
			$locatie->binnen = !!intval($locatie->binnen);
			$locatie->bronnen = !!intval($locatie->bronnen);
			$locatie->opdrachtformulier = !!intval($locatie->opdrachtformulier);
		});
	}
	public function deleteLocatie($id){
		ProjectenBronlocaties::where('id', $id)->update(['active' => 0]);
		$this->defineLocaties();
	}
	
}
