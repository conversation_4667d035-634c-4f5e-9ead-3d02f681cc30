<?php

namespace App\Http\Livewire\Instellingen\Lijsten;

use App\Models\ProjectenBronomschrijvingen;
use Livewire\Component;

class Bronnen extends Component{

	public $bronnen;
	
	protected $rules = [
		'bronnen.*.omschrijving' => '',
		'bronnen.*.kosten_binnen_excl' => '',
		'bronnen.*.kosten_buiten_excl' => '',
		'bronnen.*.kosten_binnen_incl' => '',
		'bronnen.*.kosten_buiten_incl' => '',
		'bronnen.*.bronnen' => '',
		'bronnen.*.opdrachtformulier' => '',
	];
	
	public function mount(){
		$this->defineBronen();
	}
	public function updated($model, $value){
		list($instance, $index, $attr) = explode('.', $model);
		$bron = $this->bronnen[$index];
		
		if(is_bool($value)){
			$bron->setAttribute($attr, $value ? 1 : 0);
		}
		
		$bron->save();
	}
	public function render(){
		return view('livewire.instellingen.lijsten.bronnen');
	}
	
	//Crud
	public function addBronOmschrijving(){
		$bron = new ProjectenBronomschrijvingen();
		$bron->save();
		
		$this->defineBronen();
	}
	public function deleteBronOmschrijving($id){
		ProjectenBronomschrijvingen::where('id', $id)->update(['active' => 0]);
		$this->defineBronen();
	}
	
	//Define
	public function defineBronen(){
		$this->bronnen = ProjectenBronomschrijvingen::where('active', 1)->orderBy('omschrijving', 'ASC')->get();
		$this->bronnen->map(function($bron){
			$bron->bronnen = !!intval($bron->bronnen);
			$bron->opdrachtformulier = !!intval($bron->opdrachtformulier);
		});
	}
	
}
