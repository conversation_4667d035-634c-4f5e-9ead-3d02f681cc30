<?php

namespace App\Http\Livewire\Instellingen\Lijsten;

use App\Models\ProjectenRisicoklassen;
use Livewire\Component;

class Risicoklassen extends Component{
	
		public $risicoklassen;
	
	protected $rules = [
		'risicoklassen.*.klasse' => '',
		'risicoklassen.*.bronnen' => '',
	];
	
	public function mount(){
		$this->defineRisicoklassen();
	}
	public function updated($model, $value){
		list($instance, $index, $attr) = explode('.', $model);
		$klasse = $this->risicoklassen[$index];
		
		if(is_bool($value)){
			$klasse->setAttribute($attr, $value ? 1 : 0);
		}
		
		$klasse->save();
	}
	public function render(){
		return view('livewire.instellingen.lijsten.risicoklassen');
	}
	
	//Crud
	public function addRisicoklasse(){
		$bron = new ProjectenRisicoklassen();
		$bron->save();
		
		$this->defineRisicoklassen();
	}
	public function deleteRisicoklasse($id){
		ProjectenRisicoklassen::where('id', $id)->update(['active' => 0]);
		$this->defineRisicoklassen();
	}
	
	//Define
	public function defineRisicoklassen(){
		$this->risicoklassen = ProjectenRisicoklassen::where('active', 1)->orderBy('klasse', 'ASC')->get();
		$this->risicoklassen->map(function($klasse){
			$klasse->bronnen = !!intval($klasse->bronnen);
		});
	}
	
	
}
