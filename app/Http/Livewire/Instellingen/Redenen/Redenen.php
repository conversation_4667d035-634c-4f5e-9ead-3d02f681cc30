<?php

namespace App\Http\Livewire\Instellingen\Redenen;

use App\Models\ProjectenRedenen;
use Livewire\Component;

class Redenen extends Component{

    public $redenen;
    protected $rules = [
        'redenen.*.reden' => '',
        'redenen.*.onderdeel' => '',
    ];
    protected $listeners = [
        'setOnderdeel'
    ];

    //Lifecycle
    public function updated($model, $value){
        list($instance, $index, $attr) = explode('.', $model);
        $reden = $this->redenen[$index];
        $reden->save();
    }
    public function mount(){
        $this->defineRedenen();
    }
    public function render(){
        return view('livewire.instellingen.redenen.redenen');
    }

    //Redenen
    public function addReden($onderdeel = null){
        ProjectenRedenen::create([
            'onderdeel' => $onderdeel,
        ]);
        $this->defineRedenen();
    }
    public function deleteReden($id){
        ProjectenRedenen::where('id', $id)->update(['active' => 0]);
        $this->defineRedenen();
    }

    //Onderdelen
    public function setOnderdeel($onderdeel, $params){
        $this->redenen->find($params['id'])->update(['onderdeel' => $onderdeel]);
    }
    public function getOnderdelen(){
        return $this->redenen
            ->pluck('onderdeel')
            ->unique()
            ->filter(function($onderdeel){ return isset($onderdeel); })
            ->toArray();
    }

    //Definitions
    private function defineRedenen(){
        $this->redenen = ProjectenRedenen::where('active', 1)->orderBy('onderdeel')->orderBy('reden')->get();
    }


}
