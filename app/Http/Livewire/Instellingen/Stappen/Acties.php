<?php

namespace App\Http\Livewire\Instellingen\Stappen;

use App\Models\InstellingenStappenActies;
use Livewire\Component;

class Acties extends Component{
	
	public $stap_id;
	public $proces_id;
	public $acties;
	
	protected $rules = [
		'acties.*.checked' => '',
	];

	
	public function updated(){
		InstellingenStappenActies::where('stap_id', $this->stap_id)->delete();
		foreach($this->acties as $actie){
			if(!$actie->checked){ continue; }
			
			InstellingenStappenActies::insert([
				'proces_id' => $this->proces_id,
				'stap_id' => $this->stap_id,
				'stap_actie_id' => $actie->id,
			]);
		}
	}
	public function mount($manager){
		$this->stap_id = $manager->stap_id;
		$this->proces_id = $manager->proces_id;
		$this->acties = $manager->acties;
	}
	
	public function render(){
		return view('livewire.instellingen.stappen.acties');
	}
}
