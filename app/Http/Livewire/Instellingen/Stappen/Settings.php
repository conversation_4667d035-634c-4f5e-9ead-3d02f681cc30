<?php

namespace App\Http\Livewire\Instellingen\Stappen;

use App\Models\InstellingenStappenSettings;
use Livewire\Component;

class Settings extends Component{
	
	public $proces_id;
	public $stap_id;
	
	public $settings;
	public $instellingen_settings;
	
	protected $rules = [
		'settings.*.value' => ''
	];
	
	public function updated($model, $value){
		list($var, $index, $attribute) = explode('.', $model);
		
		$setting = $this->settings[$index];
		$this->setValue($setting);
	}
	public function mount($manager){
		$this->proces_id = $manager->proces_id;
		$this->stap_id = $manager->stap_id;
		$this->settings = $manager->settings;
		$this->instellingen_settings = $manager->instellingen_settings;
		
		$this->defineValues();
	}
	public function render(){
		return view('livewire.instellingen.stappen.settings');
	}

	private function defineValues(){
		foreach($this->settings as $setting){
			$value = $this->instellingen_settings->where('keyword', $setting->keyword)->first()->value ?? null;
			
			if($setting->type == 'checkbox'){{
				$value = !!intval($value);
			}}
			
			$setting->value = $value;
		}
	}
	private function setValue($setting){
		InstellingenStappenSettings::updateOrInsert([
			'stap_id' => $this->stap_id,
			'proces_id' => $this->proces_id,
			'keyword' => $setting->keyword,
		], [
			'value' => $setting->value,
			'data' => $setting->data,
		]);
	}
	
}
