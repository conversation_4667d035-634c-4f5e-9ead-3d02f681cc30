<?php

namespace App\Http\Livewire\Instellingen\Stappen;

use App\Models\InstellingenStappenVelden;
use App\Models\StappenVelden;
use Livewire\Component;
use Log;

class Inputs extends Component{

    public $proces_id;
    public $stap;
    public $inputs;
    public $instellingen_inputs;

    public function mount($manager){
        $this->proces_id = $manager->proces_id;
        $this->stap = $manager->stap;
        $this->inputs = $manager->inputs;
        $this->defineInstellingenInputs();
        $this->instellingen_inputs = $manager->instellingen_inputs;
    }
    public function render(){
        return view('livewire.instellingen.stappen.inputs');
    }

    public function isChecked($id){
        return $this->instellingen_inputs->where('stap_veld_id', $id)->isNotEmpty();
    }
    public function isRequired($id){
        $input = $this->instellingen_inputs->where('stap_veld_id', $id)->first();
        return !!intval($input->verplicht ?? 0);
    }

    public function toggleInput($input_id){
        $input = StappenVelden::find($input_id);

        if($this->isChecked($input_id)){
            InstellingenStappenVelden::where('stap_veld_id', $input_id)->delete();
            Log::notice("Inputveld {$input->titel} is uitgezet.", [
                'data' => [
                    'stap' => $this->stap->stapnaam,
                ]
            ]);
        }
        else{
            InstellingenStappenVelden::updateOrInsert([
                'proces_id' => $this->proces_id,
                'stap_id' => $this->stap->id,
                'stap_veld_id' => $input->id,
                'verplicht' => 0,
                'is_sub' => $input->parent_input ? 1 : 0,
                'is_planmatig' => $input->is_planmatig,
                'parent_id' => $input->parent_input,
            ]);
            Log::notice("Inputveld {$input->titel} is aangezet.", [
                'data' => [
                    'stap' => $this->stap->stapnaam,
                ]
            ]);
        }

        $this->defineInstellingenInputs();
    }
    public function toggleRequired($input_id){
        $input = StappenVelden::find($input_id);

        //Toggle required
        InstellingenStappenVelden::where([
            'proces_id' => $this->proces_id,
            'stap_id' => $this->stap->id,
            'stap_veld_id' => $input_id,
        ])->update([
            'verplicht' => $this->isRequired($input_id) ? 0 : 1,
        ]);

        //Log
        $required_label = $this->isRequired($input_id) ? 'niet verplicht.' : 'verplicht.';
        Log::notice("Inputveld {$input->titel} is {$required_label}", [
            'data' => [
                'stap' => $this->stap->stapnaam,
            ]
        ]);

        $this->defineInstellingenInputs();
    }

    private function defineInstellingenInputs(){
        $this->instellingen_inputs = InstellingenStappenVelden::where(['stap_id' => $this->stap->id])->get();
    }

}
