<?php

namespace App\Http\Livewire\Instellingen\Stappen;

use App\Classes\stap\StapInstellingen;
use App\Models\InstellingenStappenGebruikers;
use App\Models\InstellingenStappenGebruikersModifiers;
use Livewire\Component;
use Log;

class Gebruikers extends Component{
    
    public $stap;
    public $proces;
    
    public $tables;
    
    public $gebruikers;
    public $contacten;
    public $available_gebruikers;
    public $instellingen_stap;
    public $instellingen_stappen;
    public $instellingen_gebruikers;
    
    protected $listeners = [
      'setInvullen',
      'setInzien',
      'setToevoegen',
      'setContact',
    ];
    
    public function mount($manager){
        $this->stap = $manager->stap;
        $this->proces = $manager->proces;
        
        $this->gebruikers = $manager->gebruikers;
        $this->contacten = $manager->contacten;
        $this->available_gebruikers = $manager->available_gebruikers;
        $this->instellingen_stap = $manager->instellingen_stap;
        $this->instellingen_stappen = $manager->instellingen_stappen;
    }
    public function render(){
        $this->updateStapTasks();
        $this->generateTasksTable();
        return view('livewire.instellingen.stappen.gebruikers');
    }
    
    
    public function setInvullen($roles){
        $this->setGebruikersTasks($roles, 'invullen');
    }
    public function setInzien($roles){
        $this->setGebruikersTasks($roles, 'inzien');
    }
    public function setToevoegen($roles){
        $this->setGebruikersTasks($roles, 'toevoegen');
    }
    public function setContact($roles){
        $this->setGebruikersTasks($roles, 'contact');
    }

    private function setGebruikersTasks($roles, $task){

        //Change all records back to 0
        InstellingenStappenGebruikers::where(['stap_id' => $this->stap->id, 'proces_id' => $this->proces->id])->update([$task => 0]);

        //Set task to 1 on the provided roles
        $role_ids = array_column($roles, 'value');
        $role_names = array_column($roles, 'name');
        foreach($role_ids as $role_id){
            $instellingen_gebruiker = InstellingenStappenGebruikers::where(['stap_id' => $this->stap->id, 'proces_id' => $this->proces->id, 'role_id' => $role_id])->where('role_id', $role_id)->first();
            
            if($instellingen_gebruiker){
                $instellingen_gebruiker->$task = 1;
                $instellingen_gebruiker->save();
            }
            else{
                InstellingenStappenGebruikers::insert([
                  'proces_id' => $this->proces->id,
                  'stap_id' => $this->stap->id,
                  'role_id' => $role_id,
                  $task => 1,
                ]);
            }
        }

        Log::notice("Gebruikers / Contacten van de stap {$this->stap->stapnaam} zijn gewijzigd.", [
            'data' => [
                'roles' => $role_names,
                'task' => $task,
            ]
        ]);

        //Remove records that with invullen & inzien 0
        $this->clearGebruikers();
    }

    public function toggleModifier($role_id, $modifier, $value){
        $query = [
            'proces_id' => $this->proces->id,
            'stap_id' => $this->stap->id,
            'role_id' => $role_id,
            'modifier' => $modifier,
            'value' => $value,
        ];

        if($this->hasModifier($role_id, $modifier, $value)){
            InstellingenStappenGebruikersModifiers::where($query)->delete();
        }
        else{
            InstellingenStappenGebruikersModifiers::insert($query);
        }
    }
    public function hasModifier($role_id, $modifier, $value){
        return InstellingenStappenGebruikersModifiers::where([
            'proces_id' => $this->proces->id,
            'stap_id' => $this->stap->id,
            'role_id' => $role_id,
            'modifier' => $modifier,
            'value' => $value,
        ])->exists();
    }


    private function clearGebruikers(){
        InstellingenStappenGebruikers::where([
          'stap_id' => $this->stap->id,
          'proces_id' => $this->proces->id,
          'invullen' => 0,
          'inzien' => 0,
          'toevoegen' => 0,
          'contact' => 0,
        ])->delete();
    }
    private function updateStapTasks(){
        $this->instellingen_gebruikers = InstellingenStappenGebruikers::where(['proces_id' => $this->proces->id, 'stap_id' => $this->stap->id])->get();
        foreach($this->available_gebruikers as $role){
            $role->_invullen = !!$this->instellingen_gebruikers->where('role_id', $role->id)->where('invullen', 1)->first();
        }
        foreach($this->gebruikers as $role){
            foreach(['toevoegen', 'inzien'] as $task){
                $col = "_$task";
                $role->$col = !!$this->instellingen_gebruikers->where('role_id', $role->id)->where($task, 1)->first();
            }
        }
        foreach($this->contacten as $role){
            $role->_contact = !!$this->instellingen_gebruikers->where('role_id', $role->id)->where('contact', 1)->first();
        }
    }

    //Table
    private function generateTasksTable(){
        $this->tables = [];

        foreach($this->proces->activeStappen(['include_parent' => false]) as $stap){
            if($stap->stapNr() > $this->stap->stapNr()){ return; }
            $this->tables[] = $this->generateStapTable($stap);
        }
    }
    private function generateStapTable($stap){
        $response = [
            'name' => "{$stap->stapNr()} {$stap->stapnaam}",
            'tasks' => [],
        ];

        foreach(['invullen', 'inzien', 'toevoegen', 'contact'] as $task){
            $ids = InstellingenStappenGebruikers::where(['proces_id' => $this->proces->id, 'stap_id' => $stap->id, $task => 1])->pluck('role_id')->toArray();
            $roles_type = ($task == 'contact') ? 'contacten' : 'gebruikers';

            $response['tasks'][$task] = $this->$roles_type->whereIn('id', $ids);
        }

        return $response;
    }
    
}
