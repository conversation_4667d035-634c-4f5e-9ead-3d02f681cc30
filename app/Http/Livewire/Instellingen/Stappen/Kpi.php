<?php
	
	namespace App\Http\Livewire\Instellingen\Stappen;
	
	use App\Classes\stap\StapInstellingen;
	use App\Models\InstellingenStappen;
	use Livewire\Component;
	use Log;

	class Kpi extends Component{
		public $kpi1;
		public $kpi2;
		public $stap;
		public $proces;
		
		protected $rules = [
			'kpi1' => 'required',
			'kpi2' => 'required',
		];
		
		public function updated($input, $value){
			InstellingenStappen::where(['proces_id' => $this->proces->id, 'stap_id' => $this->stap->id])->update([$input => $value]);
            Log::notice(strtoupper($input)." waarde is veranderd naar $value", [
                'data' => [
                    'stap' => $this->stap->stapnaam,
                ]
            ]);
		}
		
		public function mount($manager){
			$this->stap = $manager->stap;
			$this->proces = $manager->proces;
			$this->kpi1 = $manager->instellingen_stap->kpi1;
			$this->kpi2 = $manager->instellingen_stap->kpi2;
			
		}
		
		public function render(){
			return view('livewire.instellingen.stappen.kpi');
		}
	}
