<?php

namespace App\Http\Livewire\Instellingen\Stappen;

use App\Models\InstellingenStappenMails;
use Livewire\Component;
use Log;

class Mails extends Component{
	
	public $proces_id;
	public $stap;
	public $mails;
	
	protected $rules = [
		'mails.*.onderwerp' => '',
		'mails.*.mail' => '',
		'mails.*.trigger' => '',
	];
	protected $listeners = [
		'setOntvangers',
	];

	public function updated($model){
		list($var, $index, $attribute) = explode('.', $model);

		$mail = $this->mails[$index];

		$this->saveMail($mail);
	}
	public function mount($manager){
		$this->stap = $manager->stap;
		$this->proces_id = $manager->proces_id;
		$this->mails = $manager->mails;
	}
	public function render(){
		return view('livewire.instellingen.stappen.mails');
	}
	
	public function addMail(){
		$mail = new InstellingenStappenMails([
			'proces_id' => $this->proces_id,
			'stap_id' => $this->stap->id,
            'trigger' => 'STAP_CONFIRM',
		]);
		$mail->save();

		$this->mails->push($mail);
		$this->emit('addMail');

        Log::notice('Stap mail toegevoegd.', [
            'data' => [ 'stap' => $this->stap->stapnaam ]
        ]);
	}
	public function deleteMail($id){
		$this->mails = $this->mails->except($id);
		InstellingenStappenMails::where('id', $id)->update([
			'stap_id' => "-$this->stap->id",
			'proces_id' => "-$this->proces_id",
		]);

        Log::notice('Stap mail verwijderd.', [
            'data' => ['stap' => $this->stap->stapnaam ]
        ]);
	}
	public function saveMail($mail){
		$mail->is_concept = $mail->isValid() ? 0 : 1;
		$mail->save();

        Log::notice('Stap mail is gewijzigd.', [
            'data' => [
                'stap' => $this->stap->stapnaam,
                'mail' => $mail->toArray(),
            ]
        ]);
	}
	
	public function getMailContent($id){
		return $this->mails->find($id)->mail;
	}
	public function setMailContent($id, $text){
		$mail = $this->mails->find($id);
		
		$mail->mail = $text;
		$this->saveMail($mail);
	}

	public function setOntvangers($roles, $params){
		$mail = $this->mails->find($params['id']);
		$role_ids = array_column($roles, 'value');
		
		$mail->ontvangers = json_encode($role_ids);
		$this->saveMail($mail);

        Log::notice('Stap mail is gewijzigd.', [
            'data' => [
                'stap' => $this->stap->stapnaam,
                'ontvangers' => array_column($roles, 'name'),
            ]
        ]);
	}
	
}
