<?php

namespace App\Http\Livewire\Instellingen;

use App\Models\Settings;
use Livewire\Component;

class General extends Component{
    
    public $blades;
    public $blade = null;
    public $blades_data = [];

    public $values = [];
    
    //Settings types
    public $checkbox;
    public $boolean;
    public $select;

    //Livewire
    protected $rules = [
      'select.*' => '',
      'checkbox.*' => '',
      'boolean.*' => '',
    ];
    protected $listeners = [
        'setBlade'
    ];

    //Lifecycle
    public function updated(){
        $this->store();
    }
    public function mount(){
        $this->blade = firstValue($this->blades);
    }
    public function render(){
        return view('livewire.instellingen.general');
    }

    public function store(){

        //Checkboxes
        foreach($this->checkbox as $key => $array){
            $values = array_keys(($array ?? []), true);
            Settings::setValue($key, json_encode($values), 'checkbox', $this->blade);
        }

        //Booleans
        foreach($this->boolean as $key => $value){
            Settings::setValue($key, $value, 'boolean', $this->blade);
        }

        //Select
        foreach($this->select as $key => $value){
            Settings::setValue($key, $value, 'select', $this->blade);
        }

    }
    
    //Blade
    public function setBlade($blade){
        $this->blade = $blade;
        $this->setModels();
    }
    public function setModels(){
        $this->checkbox = [];
        $this->boolean = [];
        $this->select = [];

        $settings = Settings::where('blade', $this->blade)->get();
        foreach($settings as $setting){
            $type = $setting->type;
            $this->$type[$setting->key] = $this->formatSettingValue($setting);
        }
    }

    //Utility
    public function getValue($model){
        return data_get($this, $model);
    }
    private function formatSettingValue($setting){
        
        if($setting->type == 'checkbox'){
            $response = [];
            foreach (json_decode($setting->value ?? '[]') as $value) {
                $response[$value] = true;
            }
            return $response;
        }

        return $setting->value;
    }
    
}
