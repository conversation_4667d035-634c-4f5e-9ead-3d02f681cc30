<?php

namespace App\Http\Livewire\Instellingen\Processen;

use App\Models\InstellingenProcessen;
use Livewire\Component;
use App\Models\Processen as ModelProcessen;
use Log;

class Processen extends Component{

    public $processen;

    public function mount(){
        $this->defineProcessen();
    }
    public function render(){
        return view('livewire.instellingen.processen.processen');
    }

    //Procesen
    public function toggleProces($proces_id){
        $proces = ModelProcessen::find($proces_id);

        if(!$proces->isEnabled()){
            InstellingenProcessen::insert([
                'proces_id' => $proces->id,
            ]);
            Log::notice("Proces {$proces->naam} is aangezet.");
        }
        else{
            InstellingenProcessen::where([
                'proces_id' => $proces->id,
            ])->delete();
            Log::notice("Proces {$proces->naam} is uitgezet.");
        }

        $this->defineProcessen();
    }

    //Definitions
    public function defineProcessen(){
        $this->processen = ModelProcessen::get();
    }

}
