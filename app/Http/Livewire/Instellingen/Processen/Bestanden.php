<?php

namespace App\Http\Livewire\Instellingen\Processen;

use App\Models\InstellingenProcessenBestanden;
use Livewire\Component;

class Bestanden extends Component{

    public $proces;

    public $adres_bestanden;
    public $complex_bestanden;

    protected $rules = [
        'adres_bestanden.*.name' => '',
        'adres_bestanden.*.keyword' => '',
        'complex_bestanden.*.name' => '',
        'complex_bestanden.*.keyword' => '',
    ];

    //Lifecycle
    public function updated($model, $value){
        list($instance, $index, $attr) = explode('.', $model);

        $file_setting = $this->{$instance}[$index];
        if($attr == 'name'){
            $file_setting->keyword = strtolower(str_replace(' ', '_', removeSpecialCharacters($file_setting->name)));
        }

        $file_setting->save();
    }
    public function mount(){
        $this->defineBestanden();
    }
    public function render(){
        return view('livewire.instellingen.processen.bestanden');
    }

    //Files
    public function addFile($context){
        InstellingenProcessenBestanden::insert([
            'proces_type' => $this->proces->type,
            'context' => $context
        ]);
        $this->defineBestanden();
    }
    public function deleteFile($id){
        InstellingenProcessenBestanden::where('id', $id)->delete();
        $this->defineBestanden();
    }

    //Definitions
    public function defineBestanden(){
        $this->adres_bestanden = InstellingenProcessenBestanden::where(['proces_type' => $this->proces->type, 'context' => 'adres'])->get();
        $this->complex_bestanden = InstellingenProcessenBestanden::where(['proces_type' => $this->proces->type, 'context' => 'complex'])->get();

    }


}
