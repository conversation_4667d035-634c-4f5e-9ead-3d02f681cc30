<?php

namespace App\Http\Livewire\Instellingen\Processen;

use App\Models\InstellingenStappen;
use App\Models\Stappen as ModelStappen;
use Livewire\Component;
use Log;

class Stappen extends Component{

    public $proces;
    public $stappen;

    //Lifecycle
    public function mount(){
        $this->defineStappen();
    }
    public function render(){
        return view('livewire.instellingen.processen.stappen');
    }

    //Stappen
    public function toggleStap($stap_id){
        $stap = ModelStappen::find($stap_id);

        if(!$stap->isEnabled()){
            InstellingenStappen::insert([
                'proces_id' => $this->proces->id,
                'stap_id' => $stap_id,
                'stap_order' => $stap->stap_order,
                'sub_stap_of' => $stap->sub_stap_of,
            ]);
            Log::notice("Stap {$stap->stapnaam} is uitgezet.");
        }
        else{
            InstellingenStappen::where([
                'proces_id' => $this->proces->id,
                'stap_id' => $stap->id,
            ])->orWhere('sub_stap_of', $stap_id)->delete();
            Log::notice("Stap {$stap->stapnaam} is uitgezet.");
        }

        $this->defineStappen();
    }

    //Definitions
    public function defineStappen(){
        $this->stappen = $this->proces->stappen();
    }
}
