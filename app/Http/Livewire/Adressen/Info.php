<?php

namespace App\Http\Livewire\Adressen;

use App\Models\Adressen;
use Livewire\Component;
use Log;

class Info extends Component{

    public $adres;
    public $edit = false;

    protected $rules = [
        'adres.destructief_onderzoek_uitvoerende' => '',
        'adres.reikwijdte' => '',
        'adres.memo_geschiktheid' => '',
        'adres.uitsluitingen' => '',
    ];

    //Lifecycle
    public function render(){
        return view('livewire.adressen.info');
    }
    public function updated($model, $value){
        if(!$this->edit){ return; }

        list($instance, $attribute) = explode('.', $model);

        $this->adres->attributeChange($attribute, $value);
        $this->adres->save();

        Log::info("Adres gewijzigd: {$this->adres->addressLine()}.", [
            'data' => [
                'update' => [ $attribute => $value ]
            ]
        ]);
    }

    //Edit
    public function edit(){
        if(!hasPermission('adressen_edit')){ return; }

        $this->edit = true;
    }

    //Set
    public function setActive($active){
        if(!hasPermission('adressen_edit')){ return; }

        Adressen::where('id', $this->adres->id)->update(['actief' => $active]);
        $this->adres->refresh();
    }

}
