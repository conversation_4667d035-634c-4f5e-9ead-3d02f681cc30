<?php

namespace App\Http\Livewire\Adressen;

use App\Models\Settings;
use Livewire\Component;

class AdressenTable extends Component{

    public $adressen;
    public $files_keys = ['plattegrond', 'signaleringsformulier', 'opdrachtformulier'];

    //Lifecycle
    public function mount(){
        $this->files_keys = Settings::getValue('adressen_table_component_file', []);
    }
    public function render(){
        return view('livewire.adressen.adressen-table');
    }
}
