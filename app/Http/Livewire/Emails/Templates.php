<?php

namespace App\Http\Livewire\Emails;

use App\Models\EmailsTemplates;
use Livewire\Component;

class Templates extends Component{

    public $email_template;

    //Lifecycle
    public function render(){
        return view('livewire.emails.templates');
    }

    //Emails
    public function selectMail($template_key){
        $this->email_template = EmailsTemplates::where('template_key', $template_key)->firstOrCreate(['template_key' => $template_key]);
    }

    //Content
    public function getContent(){
        return optional($this->email_template)->content;
    }
    public function setContent($content){
        $this->email_template->content = $content;
        $this->email_template->save();

        $this->emit('notification', 'Tempalte succesvol gewijzigd!', 'success');
    }

}
