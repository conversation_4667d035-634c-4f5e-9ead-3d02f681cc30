<?php

namespace App\Http\Livewire\Contacten;

use Livewire\Component;
use Log;

class Contact extends Component{
    
    public $contact;
    
    protected $rules = [
      'contact.name' => 'required|max: 255',
      'contact.role_id' => 'required',
      'contact.email' => 'required|max: 255',
      'contact.telefoon' => 'max: 255',
      'contact.partijnaam' => 'max: 255',
    ];
    protected $listeners = [
      'setRole'
    ];

    //Lifecycle
    public function render(){
        return view('livewire.contacten.contact');
    }

    //Crud
    public function store(){
        $this->validate();
        $this->log();
        $this->contact->save();
        $this->emit('stored');
    }

    //Roles
    public function setRole($role){
        $this->contact->role_id = $role;
    }

    //Utility
    public function log(){
        if($this->contact->id){
            Log::info("Contact {$this->contact->name} is gewijzigd.");
        }
        else{
            Log::info("Contact {$this->contact->name} is aangemaakt.");
        }
    }
    
}
