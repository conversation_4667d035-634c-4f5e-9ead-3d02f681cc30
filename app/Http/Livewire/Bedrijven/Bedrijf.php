<?php

namespace App\Http\Livewire\Bedrijven;

use App\Models\Bedrijven;
use App\Models\Files;

use Livewire\Component;
use Livewire\WithFileUploads;

class Bedrijf extends Component{
    use WithFileUploads;
    
    public $logo;
    public $bedrijf;
    public $rules = [
      'bedrijf.naam' => 'required|max: 255',
      'bedrijf.email' => 'max: 255',
      'bedrijf.telefoon' => 'max: 255',
      'bedrijf.logo_guid' => 'max: 255',
      'logo' => 'image',
    ];
    
    public function render(){
        return view('livewire.bedrijven.bedrijf');
    }
    
    public function store(){
        $this->storeLogo();
        $this->validate();
        
        $this->bedrijf->save();
        $this->emit('stored');
    }
    
    public function storeLogo(){
        if (!$this->logo) { return; }

        $file = Files::storeFile([
          'file' => $this->logo,
          'name' => $this->bedrijf->naam . ' logo',
          'path' => '/bedrijven/logos'
        ], 'public');
				
        $this->bedrijf->logo_guid = $file->guid;
    }
    
}
