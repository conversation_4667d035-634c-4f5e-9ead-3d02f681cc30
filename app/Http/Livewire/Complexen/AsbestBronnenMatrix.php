<?php

namespace App\Http\Livewire\Complexen;

use App\Models\AsbestBronnen;
use ExportType;
use Livewire\Component;

use PriceType;

class AsbestBronnenMatrix extends Component{

    public $complex;
    public $bronnen;

    //Preview
    public $bron_preview;
    public $bron_preview_reference;

    //Total
    public $total;
    public $total_plain;

    //Options
    public $show_matrix = true;
    public $show_settings = true;
    public $show_legenda = true;
    public $show_total = false;
    public $show_export = true;

    //Settings
    public $settings = [
        'cost_incl_referentie' => false,
        'cost_incl_indicatie' => false,
        'indicatie_show' => false,
        'indicatie_percentage' => 80,
    ];

    //Livewire
    protected $rules = [
        'settings.cost_incl_referentie' => '',
        'settings.cost_incl_indicatie' => '',
        'settings.indicatie_show' => '',
        'settings.indicatie_percentage' => '',
    ];
    protected $listeners = [
      'setValues',
    ];

    //Lifecycles
    public function updated(){
        $this->defineTotal();
    }
    public function mount(){
        $this->defineBronnen();
        $this->defineTotal();
    }
    public function render(){
        return view('livewire.complexen.asbest-bronnen-matrix');
    }

    //Targeted lifecycles
    public function updatedSettings($value, $model){
        if($model == 'indicatie_show' && !$value){ $this->settings['cost_incl_indicatie'] = false; }
    }

    //Bronnen
    public function displayBron($bron_id, $referentie){
        $this->bron_preview = AsbestBronnen::find($bron_id);
        $this->bron_preview_reference = $referentie;
    }

    //Definitions
    public function defineBronnen(){
        $this->bronnen = $this->complex->uniekeAsbestBronnen();
    }
    public function defineTotal(){
        $total = $this->complex->asbestBronnenTotalCost($this->settings);

        $this->total = $total->get(PriceType::EXCL);
        $this->total_plain = $total->get(PriceType::EXCL, true);
    }

    //Export
    public function exportExcel(){
        return $this->complex->exportAsbestMatrix(ExportType::EXCEL, [
            'settings' => $this->settings,
        ]);
    }

    //Utility
    public function setValues($values){
        foreach($values as $key => $value){
            data_set($this, $key, $value);
        }

        $this->defineTotal();
    }

}
