<?php

namespace App\Http\Livewire\Complexen;

use App\Classes\Price;
use App\Models\Complexen;
use ExportType;
use Livewire\Component;
use PriceType;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Invoker\Exception;

class AsbestBronnenMatrixen extends Component{

    public $loaded = false;

    //Complexen
    public $complexen;
    public $loaded_matrixen;

    //Settings
    public $global_settings = [
        'cost_incl_referentie' => false,
        'cost_incl_indicatie' => false,
        'indicatie_show' => false,
        'indicatie_percentage' => 80,
    ];

    //Livewire
    protected $rules = [
        'global_settings.cost_incl_referentie' => '',
        'global_settings.cost_incl_indicatie' => '',
        'global_settings.indicatie_show' => '',
        'global_settings.indicatie_percentage' => '',
    ];

    //Lifecycle
    public function updated(){
        $this->emit('set-matrix-settings', $this->global_settings);
    }
    public function mount(){
        $this->defineComplexen();
        $this->defineLoadedMatrixen();
    }
    public function render(){
        return view('livewire.complexen.asbest-bronnen-matrixen');
    }

    //Init
    public function initLoaded(){
        $this->loaded = true;
        $this->emit('complexen-loaded');
    }

    //Matrixen
    public function loadMatrix($id){
        $this->loaded_matrixen[$id] = true;
        $this->emit('load-matrix', $id);
    }

    //Export
    public function exportExcel(){
        return Complexen::exportAsbestKosten(ExportType::EXCEL, [
            'settings' => $this->global_settings,
        ]);
    }

    //Definitions
    public function defineComplexen(){
        return $this->complexen = Complexen::getAll();
    }
    public function defineLoadedMatrixen(){
        foreach($this->complexen as $complex){
            $this->loaded_matrixen[$complex->id] = false;
        }
    }


}
