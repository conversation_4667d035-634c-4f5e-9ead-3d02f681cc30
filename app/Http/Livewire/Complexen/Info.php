<?php

namespace App\Http\Livewire\Complexen;

use App\Classes\ARLivewireComponent;
use App\Models\Adressen;

class Info extends ARLivewireComponent{

    public $complex;
    public $edit = false;

    protected $rules = [
        'complex.uitvoerjaar' => '',
    ];
    protected $listeners = [
        'setValue'
    ];

    //Lifecycle
    public function render(){
        return view('livewire.complexen.info');
    }

    //Targeted lifecycle
    public function updatedComplex($value, $attribute){
        if(!$this->edit){ return; }

        Adressen::where('complexnummer', $this->complex->complexnummer)->update([
            $attribute => $value,
        ]);
    }

    //Edit
    public function edit(){
        if(!hasPermission('complexen_edit')){ return; }
        $this->edit = true;
    }

}
