<?php

namespace App\Http\Livewire\Complexen;

use App\Models\ComplexenFiles;
use App\Models\Files;
use Livewire\Component;
use Livewire\WithFileUploads;

use Log;

class Documenten extends Component{
    use WithFileUploads;

    public $complex;
    public $proces_type;

    //Files
    public $files = [];
    public $instellingen_files;

    public $rules = [
        'files.*' => 'max:65536',
    ];

    //Lifecycle
    public function mount(){
        $this->instellingen_files = $this->complex->getInstellingenFiles($this->proces_type);

        $this->defineFiles();
    }
    public function render(){
        return view('livewire.complexen.documenten');
    }

    //Targeted lifecycle
    public function updatedFiles($files, $file_key){
        if(!hasPermission('complexen_edit')){ return; }

        foreach($files as $file){
            $file_instance = Files::storeFile([
                'file' => $file,
                'path' => "/{$file_key}"
            ]);
            ComplexenFiles::insert([
                'complexnummer' => $this->complex->complexnummer,
                'file_id' => $file_instance->id,
                'proces_type' => $this->proces_type,
                'file_key' => $file_key,
            ]);

            Log::info("Document {$file_key} toegevoegd aan het {$this->complex->complexnummer} ".label('complex').'.', [
                'data' => ['file' => $file_instance->url()]
            ]);
        }
    }

    //Definitions
    public function defineFiles(){
        foreach($this->instellingen_files as $file){
            $this->files[$file->keyword] = null;
        }
    }

}
