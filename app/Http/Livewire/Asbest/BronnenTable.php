<?php

namespace App\Http\Livewire\Asbest;

use App\Models\AsbestBronnen;
use Livewire\Component;

class BronnenTable extends Component{

    public $single_adres = true;
    public $referentie = false;
    public $bronnen;
    public $bron;

    public function mount(){
        $this->defineBronnen();
    }
    public function render(){
        return view('livewire.asbest.bronnen-table');
    }

    //Definitions
    public function defineBronnen(){
        if(!$this->bronnen && $this->bron){
            $this->bronnen = AsbestBronnen::where('id', $this->bron->id)->get();
        }
    }
}
