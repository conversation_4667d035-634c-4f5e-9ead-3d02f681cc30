<?php

namespace App\Http\Livewire\Gebruikers;

use App\Models\ClientRolesPermissions;
use App\Models\ClientUsersPermissions;
use App\Models\RolesPermissions;
use Livewire\Component;

use Log;

class Permissions extends Component{


    public $user;
    public $permissions;
    public $clientPermissions;

    //Lifecycle
    public function mount(){
        $this->definePermissions();
    }
    public function render(){
        return view('livewire.gebruikers.permissions');
    }

    //Userpermissions
    public function togglePermission($keyword){
        $has_permission = hasPermission($keyword, $this->user->id);
        ClientUsersPermissions::updateOrInsert([
            'user_id' => $this->user->id,
            'keyword' => $keyword
        ], [
            'active' => $has_permission ? 0 : 1
        ]);

        Log::notice("Permission van de Gebruiker {$this->user->username} is gewijzigd.", [
            'data' => [
                'permission' => RolesPermissions::name($keyword),
                'state' => !$has_permission
            ]
        ]);

        $this->definePermissions();
    }
    public function removePermission($keyword){
        ClientUsersPermissions::where([
            'user_id' => $this->user->id,
            'keyword' => $keyword
        ])->delete();

        Log::notice("Gebruiker specifieke permission van {$this->user->username} is verwijderd.", [
            'data' => [
                'permission' => RolesPermissions::name($keyword),
            ]
        ]);

        $this->definePermissions();
    }
    public function hasCategoryPermission($category){
        $permissions = $this->permissions->where('category', $category);
        $active_permissions = $permissions->filter(function($permission){
            return hasPermission($permission->keyword, $this->user->id);
        });

        if(!$active_permissions->count()){
            return 'NONE';
        }
        elseif($permissions->count() === $active_permissions->count()){
            return 'ALL';
        }
        else{
            return 'SOME';
        }
    }

    //Definitions
    private function definePermissions(){
        $this->permissions = RolesPermissions::orderBy('category', 'ASC')->orderBy('name', 'ASC')->get();
        $this->clientPermissions = ClientUsersPermissions::where([
            'user_id' => $this->user->id,
            'active' => 1
        ])->get();
    }

}
