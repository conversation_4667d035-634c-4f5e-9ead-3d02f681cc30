<?php

namespace App\Http\Livewire\Gebruikers;

use App\Classes\ARLivewireComponent;
use App\Models\DomeinKoppel;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

use Log;

class Gebruiker extends ARLivewireComponent{

    public $domain_id;
    public $role_id;

    public $edit;
    public $user;

    public $rules = [
        'role_id' => 'required|max: 255',
        'user.id' => '',
        'user.email' => 'required|max: 255',
        'user.username' => 'required|max: 255',
        'user.name' => 'required|max: 255',
        'user.telefoon' => 'required|max: 255',
        'user.bedrijf_id' => '',
        'user.is_supervisor' => '',
        'user.is_sso' => '',
    ];
    public $listeners = [
        'setValue',
    ];

    //Lifecycle
    public function mount(){
        $this->domain_id = _domain()->id;

        $this->edit = isset($this->user->id);
        $this->role_id = $this->user->role->id ?? null;

        $this->checkUser();
    }
    public function render(){
        return view('livewire.gebruikers.gebruiker');
    }

    //Crud
    public function store(){
        $this->checkUser(false);
        $this->validate();
        $this->log();

        if(!$this->user->id){
            $data = $this->user->toArray();

            $data = array_merge($data, [
                'active_domain' => $this->domain_id,
                'password' => Hash::make($this->user->pass),
            ]);

            unset($data['pass']);

            $this->user->id = User::insertGetId($data);
        }

        DomeinKoppel::connect($this->domain_id, $this->user->id, $this->role_id);
        $this->emit('notification', 'Gebruiker succesvol opgeslagen!', 'success');
        $this->emit('redirect', "/dashboard/gebruikers/edit/{$this->user->id}", 250);
    }

    //User
    public function checkUser($clear = true){
        $user_check = User::where('username', $this->user->username)->first();

        $this->user->id = $user_check->id ?? null;
        if($user_check){
            $this->user->email = $user_check->email;
            $this->user->name = $user_check->name;
            $this->user->telefoon = $user_check->telefoon;
            $this->user->bedrijf_id = $user_check->bedrijf_id;
            $this->user->is_supervisor = (int)$user_check->is_supervisor;
        }
        elseif($clear){
            $this->user->email = null;
            $this->user->name = null;
            $this->user->telefoon = null;
            $this->user->bedrijf_id = null;
            $this->user->is_supervisor = 0;
            $this->user->is_sso = 0;
        }

        $this->rules['user.pass'] = $this->user->id || $this->user->is_sso
            ? 'max: 255'
            : 'required|max: 255';

        $this->emit('checkUser', $this->user);
    }

    //Utility
    public function log(){
        if($this->user->id){
            Log::info("Gebruiker {$this->user->username} is gewijzigd.");
        }
        else{
            Log::info("Gebruiker {$this->user->username} is aangemaakt.");
        }
    }

}
