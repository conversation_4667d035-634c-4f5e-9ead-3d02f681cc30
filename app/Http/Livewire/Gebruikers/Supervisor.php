<?php

namespace App\Http\Livewire\Gebruikers;

use App\Models\SupervisorUsers;
use Livewire\Component;

class Supervisor extends Component{

    public $supervisor;
    public $supervised_users;

    protected $listeners = [
        'setUser'
    ];


    //Lifecycle
    function render(){
        return view('livewire.gebruikers.supervisor');
    }

    //Child users
    public function setUser($user_id){
        SupervisorUsers::updateOrInsert([
            'supervisor_id' => $this->supervisor->id,
            'user_id' => $user_id,
        ]);
        $this->supervisor->refresh();
    }
    public function removeUser($user_id){
        SupervisorUsers::where([
            'supervisor_id' => $this->supervisor->id,
            'user_id' => $user_id,
        ])->delete();
        $this->supervisor->refresh();
    }


}
