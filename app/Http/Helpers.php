<?php

use App\Models\Adressen;
use App\Models\AsbestBronnen;
use App\Models\ClientRolesPermissions;
use App\Models\ClientUsersPermissions;
use App\Models\Errors;
use App\Models\ProjectenBronlocaties;
use App\Models\ProjectenBronomschrijvingen;
use App\Models\ProjectenRisicoklassen;
use App\Models\Settings;
use App\Models\StappenApi;
use App\Models\User;
use App\Models\Domeinen;
use App\Models\Roles;
use App\Models\InstellingenStappen;
use App\Models\InstellingenProcessen;
use App\Models\Stappen;
use App\Models\Processen;
use App\Models\Bedrijven;
use Carbon\Carbon;
use Illuminate\Routing\Route;
use Illuminate\Support\Facades\Auth;

//Guid
function guid(){
    //Segments:
    //1-4 timestamp based random guid
    //5 Domain ID

    mt_srand((double)microtime() * 10000);
    $charid = strtoupper(md5(uniqid(rand(), true)));

    $domain_guid_segment = guidSegment(_domain()->id ?? 0);

    $hyphen = chr(45);
    $uuid = substr($charid, 0, 8) . $hyphen
        . substr($charid, 8, 4) . $hyphen
        . substr($charid, 12, 4) . $hyphen
        . substr($charid, 16, 4) . $hyphen
        . substr($charid, 20, 12) . $hyphen
        . $domain_guid_segment;
    return $uuid;
}
function guidSegment($string){
    $encoded = ar_encrypt($string);
    $bin = base64_decode($encoded);
    $hex = bin2hex($bin);
    return strtoupper($hex);
}
function decodeGuidSegment($guid){
    $bin = hex2bin($guid);
    $encoded = base64_encode($bin);
    return ar_decrypt($encoded);
}

//Session
function _user(){
    if(!Auth::check()){ return new User(); }
    return Auth::user();
}
function _get($name, $default = null){
    return $_GET[$name] ?? $default;
}
function _getAssert($name, $equals){
    return _get($name) === $equals;
}
function _cookie($name){
    return $_COOKIE[$name] ?? null;
}
function _req($name){
    return request()->get($name) ?? null;
}
function _route($name){
    return request()->route($name);
}
function _file($guid){
    return url('dashboard/api/file/get/' . $guid);
}
function _domain(){
    if(_user()->id){
        return optional(Domeinen::find( _user()->active_domain ));
    }

    return optional(Domeinen::find( Config::get('active_domain') ));
}
function _cachedDomainName(){
    return Config::get('active_domain_name');
}
function _role(){
    return _user()->role;
}

//Validation
function isMobile(){
    $userAgent = $_SERVER['HTTP_USER_AGENT'];
    $mobileKeywords = array('Android', 'iPhone', 'iPad', 'Windows Phone', 'BlackBerry', 'webOS', 'Mobile');

    foreach($mobileKeywords as $keyword){
        if(stripos($userAgent, $keyword) !== false){
            return true;
        }
    }

    return false;
}
function isJson($string){
    json_decode($string);
    return json_last_error() === JSON_ERROR_NONE;
}
function isRoute($route){
    return $route == getRoute();
}

//Utiliy
function extensions(){
    return $extensions = [
        'img' => ['jpg' => true, 'jpeg' => true, 'png' => true, 'gif' => true, 'svg' => true, 'webp' => true, 'tiff' => true, 'Bitmap' => true, 'EPS' => true],
        'mp3' => ['mp3' => true, 'aac' => true, 'flac' => true, 'alac' => true, 'wav' => true, 'aiff' => true, 'dsd' => true, 'pcm' => true],
        'mp4' => ['mp4' => true, 'mov' => true, 'wmv' => true, 'avi' => true, 'avchd' => true, 'flv' => true, 'f4v' => true, 'swf' => true, 'mkv' => true, 'webm' => true],
        'pdf' => ['pdf' => true],
        'stat' => ['csv' => true, 'dat' => true, 'db' => true, 'dbf' => true, 'log' => true, 'mdb' => true, 'sav' => true, 'sql' => true, 'tar' => true, 'xml' => true],
        'txt' => ['doc' => true, 'docx' => true, 'odt' => true, 'rtf' => true, 'tex' => true, 'txt' => true, 'wpd' => true],
        'xlsx' => ['ods' => true, 'xls' => true, 'xlsm' => true, 'xlsx' => true],
        'zip' => ['7z' => true, 'arj' => true, 'deb' => true, 'pkg' => true, 'rar' => true, 'rpm' => true, 'tar' => true, 'gz' => true, 'z' => true, 'zip' => true]
    ];
}
function randomString($length = 10){
    $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for($i = 0; $i < $length; $i++){
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}
function randomCharacters($length = 10){
    $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $charactersLength = strlen($characters);
    $randomString = '';
    for($i = 0; $i < $length; $i++){
        $randomString .= $characters[rand(0, $charactersLength - 1)];
    }
    return $randomString;
}
function cacheClear(){
    return '?cache=' . env('BUILD_GUID');
}
function bladeExists($blade){
    return view()->exists($blade);
}
function isUCFist($string) {
    $firstChar = mb_substr($string, 0, 1);
    return ctype_upper($firstChar);
}

//Data
function getUsers(){
    return User::whereHas('domeinen', function($query){
        $query->where('domeinen.id', _domain()->id);
    })->orderBy('name')->get();
}
function getRoles($login = null){
    $model = new Roles();

    if($login !== null){
        $model = $model->where(['login' => $login]);
    }

    return $model->orderBy('login', 'DESC')->orderBy('name', 'ASC')->get();
}
function getRole($role_id){
    return Roles::findOrNew($role_id);
}
function getRoleByKey($key){
    return Roles::where('key', $key)->first();
}
function getBedrijven(){
    return Bedrijven::orderBy('naam', 'ASC')->get();
}
function getUsersByRol($role){
    $users = User::with('domeinen')->whereHas('domeinen', function($query) use ($role){
        $query->where([
            'role_id' => $role,
            'domain_id' => _domain()->id,
        ]);
    })->orderBy('name')->get();

    return $users;
}
function getUsersIdsByRol($role){
    return getUsersByRol($role)->pluck('id')->toArray();
}
function getRoute(){
    return str_replace(url("/"), "", url()->current());
}
function getHost(){
    $host = request()->getHost();
    $hostParts = explode('.', $host);

    if (count($hostParts) > 2) {
        $host = implode('.', array_slice($hostParts, -2));
    }

    return $host;
}
function getMaanden($maand = null){
    $maanden = [];
    $maanden[1] = "Januari";
    $maanden[2] = "Februari";
    $maanden[3] = "Maart";
    $maanden[4] = "April";
    $maanden[5] = "Mei";
    $maanden[6] = "Juni";
    $maanden[7] = "Juli";
    $maanden[8] = "Augustus";
    $maanden[9] = "September";
    $maanden[10] = "Oktober";
    $maanden[11] = "November";
    $maanden[12] = "December";
    if($maand !== null){
        return $maanden[$maand] ?? '';
    }
    return $maanden;
}
function getShortMaanden($maand = null){
    $maanden = [];
    $maanden[1] = "Jan";
    $maanden[2] = "Feb";
    $maanden[3] = "Mrt";
    $maanden[4] = "Apr";
    $maanden[5] = "Mei";
    $maanden[6] = "Jun";
    $maanden[7] = "Jul";
    $maanden[8] = "Aug";
    $maanden[9] = "Sept";
    $maanden[10] = "Okt";
    $maanden[11] = "Nov";
    $maanden[12] = "Dec";
    if($maand !== null){
        return $maanden[$maand] ?? '';
    }
    return $maanden;
}
function getActiveProcessen(){
    $processen_ids = InstellingenProcessen::orderBy('proces_id', 'ASC')->pluck('proces_id')->toArray();
    return Processen::with('instellingen_stappen')->whereIn('id', $processen_ids)->get();
}
function getActiveProcessenTypes(){
    $processen = getActiveProcessen();
    return $processen->unique('type')->pluck('type')->toArray();
}
function getActiveStappen($proces_id, $data = []){
    $complex = Processen::findOrFail($proces_id);
    return $complex->activeStappen($data);
}
function getBronLocaties($options = []){
    $model = new ProjectenBronlocaties();
    $model = $model->where('active', 1)->orderBy('locatie', 'ASC');

    if(isset($options['where'])){
        $model->where($options['where']);
    }

    return $model->get();
}
function getBronOmschrijvingen($options = []){
    $model = new ProjectenBronomschrijvingen();
    $model = $model->where('active', 1)->orderBy('omschrijving', 'ASC');

    if(isset($options['where'])){
        $model->where($options['where']);
    }

    return $model->get();
}
function getBronRisicoklassen($options = []){
    $model = new ProjectenRisicoklassen();
    $model = $model->where('active', 1)->orderBy('klasse', 'ASC');

    if(isset($options['where'])){
        $model->where($options['where']);
    }

    return $model->get();
}
function getExistingBronEenheiden(){
    return AsbestBronnen::select('eenheid')->whereNotNull('eenheid')->groupBy('eenheid')->orderBy('eenheid', 'ASC')->pluck('eenheid')->toArray();
}
function getPDFPages($pdf){
    $pdf->output();
    return $pdf->getDomPDF()->getCanvas()->get_page_count();
}
function getAdressen($options = []){
    $query = new Adressen();

    if(isset($options['where'])){
        foreach($options['where'] as $key => $value){
            $query = $query->where($key, $value);
        }
    }

    return $query->get();
}
function getComplexAdressen($complexnummer){
    return Adressen::where('complexnummer', $complexnummer)->get();
}
function getSSODomains(){
    return Domeinen::select(['domein', 'domein_key'])->whereHas('sso_driver')->orderBy('domein', 'ASC')->get();
}
function getIP(){
    $ip = request()->ip();

    //Proxies
    if (request()->hasHeader('X-Forwarded-For')) {
        $forwardedIps = explode(',', request()->header('X-Forwarded-For'));
        $ip = trim($forwardedIps[0]);
    }

    return $ip;
}
function getIPv4(){
    $ip = getIP();

    if (!filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
        return null;
    }

    return $ip;
}
function getIPv6(){
    $ip = getIP();

    if (!filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)) {
        return null;
    }

    return $ip;
}

//Route
function currentRoute(){
    return str_replace(url("/"), "", url()->current());
}
function routePrefix(){
    $arr = explode("/", Route::getCurrentRoute()->uri);
    return $arr[0] ?? null;
}
function redirectHome(){
    if(_user()->is_supervisor){
        return redirect('/dashboard/supervision');
    }
    else if(hasPermission('projecten_index')){
        return redirect('/dashboard/projecten?status=ACTIELIJST');
    }
    else if(hasPermission('adressen_index')){
        return redirect('/dashboard/adressen');
    }

    return redirect('/dashboard/welcome');
}
function redirectUnauthorized(){
    return redirectHome()->with('danger', 'Toegang geweigerd.');
}
function supervisorUrl($path = ''){
    $path = ltrim($path, '/');
    $host = getHost();

    return "https://supervision.{$host}/{$path}";
}

//Encryptions
function ar_encrypt($string){
    return openssl_encrypt($string, "AES-256-CBC", env("DATABASE_PRIVATE_KEY"), 0, env("DATABASE_IV"));
}
function ar_decrypt($hash){
    return openssl_decrypt($hash, "AES-256-CBC", env("DATABASE_PRIVATE_KEY"), 0, env("DATABASE_IV"));
}

//Arrays
function find($index, $search, $arr, $return_index = false){
    foreach($arr ?? [] as $row_index => $row){
        $x = $row[$index] ?? null;
        if($x == $search){
            if($return_index){ return $row_index; }
            return $row;
        }
    }
    return null;
}
function firstValue($arr){
    foreach($arr as $row){
        return $row;
    }
    return null;
}
function lastValue($arr){
    $x = 1;
    foreach($arr ?? [] as $key => $value){
        if($x == countObject($arr)){
            return $value;
        }
        $x++;
    }
    return null;
}
function countObject($object){
    return count((array)$object);
}
function resetIndex($array){
    $temp = [];

    foreach($array as $row){
        $temp[] = $row;
    }

    return $temp;
}
function arrayToObject($array){
    return json_decode(json_encode($array));
}

//Srtings
function stringToBool($data){
    if(is_array($data)){
        foreach($data as $index => $value){
            if($value == 'true'){ $data[$index] = true; }
            if($value == 'false'){ $data[$index] = false; }
        }
        return $data;
    }

    if($data == 'true'){ return true; }
    else if($data == 'false'){ return false; }

    return $data;
}
function stringToHexColor($string, $opacity = 1) {
    if (!$string) {
        return '#404040';
    }

    $hash = 0;
    $length = strlen($string);

    for ($i = 0; $i < $length; $i++) {
        $hash = ord($string[$i]) + (($hash << 5) - $hash);
    }

    $color = '#';
    for ($i = 0; $i < 3; $i++) {
        $value = ($hash >> ($i * 8)) & 0xff;
        $color .= sprintf('%02x', $value);
    }

    return $color.opacitytoHex($opacity);
}
function opacityToHex($opacity){
    $hex = round($opacity * 255);
    $hex = str_pad(dechex($hex), 2, '0', STR_PAD_LEFT);
    return strtoupper($hex);
}

//String cases
function snakeToCamelCase($string){
    $string = str_replace('_', ' ', $string);
    $string = ucwords($string);
    $string = str_replace(' ', '', $string);
    return lcfirst($string);
}
function snakeToSpaceCase($string){
    $string = str_replace('_', ' ', $string);
    return ucfirst($string);
}
function camelToSnakeCase($string){
    $string = removeSpecialCharacters($string);
    return strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $string));
}
function camelToSpaceCase($string) {
    $string = removeSpecialCharacters($string);
    $spaced = preg_replace('/(?<!^)(?=[A-Z])/', ' ', $string);
    return ucfirst(strtolower($spaced));
}
function spaceToSnakeCase($string) {
    $string = removeSpecialCharacters($string);
    return strtolower(str_replace(' ', '_', $string));
}
function spaceToCamelCase($string){
    $string = removeSpecialCharacters($string);
    return lcfirst(str_replace(' ', '', ucwords($string)));
}
function removeSpecialCharacters($string) {
    return preg_replace('/[^A-Za-z0-9 \-]/', '', $string);
}

//Apis
function getKoppelingen(){
    return StappenApi::where('domain_id', _domain()->id)->orderBy('koppeling', 'ASC')->groupBy('koppeling')->pluck('koppeling')->toArray();
}
function getKoppelingenApis(){
    return StappenApi::where('domain_id', _domain()->id)->orderBy('koppeling', 'ASC')->get();
}

//Errors
function handleError($e){

    try{
        storeError($e);
        handleErorrByTessa($e);

        return response(['message' => $e->getMessage()], 500);
    }
    catch(Throwable $e){
        return response(['message' => 'Er is iets foutgegaan'], 500);
    }

}
function storeError($e){
    Errors::insert([
        'domain_id' => _domain()->id ?? null,
        'user_id' => _user()->id ?? null,
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'message' => $e->getMessage(),
        'method' => request()->getMethod(),
        'url' => request()->getPathInfo(),
        'request' => json_encode(request()->all()),
        'json' => json_encode(request()->json()->all()),
    ]);
}
function handleErorrByTessa($e){
    if(!env('TESSA_DEBUG')){ return; }

    try{
        $tokens = getTessaDevelopersTokens();
        $user = User::_name();
        $domain = _domain();

        pushToTessa('( AR 2.0 ) Er is een fout opgetreden!', "Pagina: " . currentRoute() . "\r\nFoutmelding: " . $e->getMessage() . "\r\nBestand: " . lastValue(explode('/', $e->getFile())) . "\r\nRegel: " . $e->getLine()."\r\nUser: {$user}\r\nCorporatie: {$domain->domein}", $tokens);
    }
    catch(Exception $ex){}
}
function getTessaDevelopersTokens(){
    return json_decode(file_get_contents('https://infordb.ikbentessa.nl/api/developertokens/get') ?? '[]');
}
function pushToTessa($title, $message, $tokens){
    $msg = ['title' => $title, 'body' => $message];
    $headers = ['Authorization: key=' . env('TESSA_FCM_API_KEY'), 'Content-Type: application/json'];
    $fields = ['registration_ids' => $tokens, 'notification' => $msg];

    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, 'https://fcm.googleapis.com/fcm/send');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($fields));

    $result = curl_exec($ch);
    curl_close($ch);
    return json_decode($result);
}

//Date
function Carbon($date = null){
    if($date){
        return Carbon::parse($date);
    }

    return Carbon::now();
}
function displayDate($date){
    if(!$date || $date == '0000-00-00'){
        return '';
    }

    return Carbon::parse($date)->format('d-m-Y');
}

//Permissions
function isAdmin(){
    return !!(int)_user()->is_admin;
}
function hasPermission($key, $user_id = null){
    if(!Auth::check()){
        return false;
    }

    if(is_array($key)){
        foreach($key as $permission_key){
            if(hasPermission($permission_key, $user_id)){ return true; }
        }

        return false;
    }

    $user = User::find($user_id ?? _user()->id);
    if(!$user){ return false; }

    if(userHasPermission($key, $user->id) !== null){
        return userHasPermission($key, $user->id);
    }

    return roleHasPermission($key, $user->role->id);
}
function roleHasPermission($key, $role_id){
    return ClientRolesPermissions::where([
        'keyword' => $key,
        'role_id' => $role_id,
        'active' => 1,
    ])->exists();
}
function userHasPermission($key, $user_id){
    $record = ClientUsersPermissions::where([
        'keyword' => $key,
        'user_id' => $user_id,
    ])->first();
    if(!$record){ return null; }

    return $record->active === '1';
}

function setting($key, $def = null){
    return Settings::getValue($key, $def);
}

//Eloquent
function queryFromBulder($model){
    $sql = $model->toSql();
    foreach($model->getBindings() as $binding){
        if (is_string($binding)) {
            $binding = "'".addslashes($binding)."'";
        }
        $sql = preg_replace('/\?/', $binding, $sql, 1);
    }
    return $sql;
}

//Env
function setEnv($key, $value){

    $env_path = base_path('.env');
    $env = file_get_contents($env_path);
    $env = preg_replace("/^{$key}=.*/m", "{$key}={$value}", $env);

    file_put_contents($env_path, $env);
}

//Labels
function label($label){
    $adres_label = [ 'adres', 'adressen', 'complex', 'complexen' ];

    if(in_array(strtolower($label), $adres_label)){ return adresLabel($label); }
    return $label;
}
function adresLabel($label){
    $is_uc = isUCFist($label);
    $setting = Settings::getValue('adressen_formaat', 'complex_adres');

    if($setting == 'complex_adres'){ return $label; }
    else if($setting == 'gebouw_ruimte'){
        switch(strtolower($label)){
            case 'complex': $label = 'gebouw'; break;
            case 'complexen': $label = 'gebouwen'; break;
            case 'adres': $label = 'ruimte'; break;
            case 'adressen': $label = 'ruimtes'; break;
        }
    }

    if($is_uc){ $label = ucfirst($label); }
    return $label;
}
