<?php

namespace App\Http\Middleware;

use App\Models\ClientRolesPermissions;
use Illuminate\Http\Request;
use Closure;
use Log;

class HasPermission{

	public function handle(Request $request, Closure $next, $permissions){
		$permissions = explode(' ', $permissions);
		$has_permission = ClientRolesPermissions::whereIn('keyword', $permissions)->where([
			'role_id' => _role()->id,
            'active' => 1,
		])->exists();
		
		if(!$has_permission){
            Log::warning("Toegang geweigerd", [
                'data' => ['route' => getRoute()]
            ]);

            if($request->isMethod('get')){
                return redirectHome()->with('danger', "Uw rol heeft niet de benodigde permissions om deze pagina te bekijken.");
            }

			return response([
                'message' => "Uw rol heeft niet de benodigde permissions."
            ], 401);
		}
	
		return $next($request);
	}

}
