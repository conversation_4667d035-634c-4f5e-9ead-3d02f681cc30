<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class SecurityHeaders{

    private array $script_src = [];
    private array $style_src = [ 'https://fonts.googleapis.com' ];
    private array $fonts_src = [ 'https://fonts.gstatic.com', 'https://fonts.googleapis.com' ];

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next){

        $response = $next($request);
        $host = getHost();

        $response->headers->set('Permissions-Policy', 'geolocation=(self), microphone=()');
        $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
        $response->headers->set('Referrer-Policy', 'no-referrer-when-downgrade');
        $response->headers->set('Cache-Control', 'private, no-cache, no-store, max-age=0');
        $response->headers->set('X-Frame-Options', 'SAMEORIGIN');
        $response->headers->set('Content-Security-Policy',
            "default-src 'self'; " .
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' " . implode(' ', $this->script_src) . "; " .
            "style-src 'self' 'unsafe-inline' " . implode(' ', $this->style_src) . "; " .
            "font-src 'self' " . implode(' ', $this->fonts_src) . "; " .
            "img-src 'self' data:;" .
            "connect-src 'self' wss:; " .
            "frame-src 'self' https://*.{$host} https://{$host}; " .
            "frame-ancestors 'self' https://*.{$host} https://{$host};"
        );

        $response->headers->set('X-Content-Type-Options', 'nosniff');

        return $response;
    }
}
