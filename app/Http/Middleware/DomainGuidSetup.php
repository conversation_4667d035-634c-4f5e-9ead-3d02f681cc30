<?php

namespace App\Http\Middleware;

use App\Models\Domeinen;
use App\Models\Files;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class DomainGuidSetup{
	/**
	 * Handle an incoming request.
	 *
	 * @param \Illuminate\Http\Request $request
	 * @param \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse) $next
	 * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
	 */
	public function handle(Request $request, Closure $next){
        if($request->isMethod('get')){
            $guid = $request->route()->parameter('guid');
        }
        else{
            $guid = $request->input('guid');
        }


		if(!$guid){ abort(404, 'Domain not found'); }
		
		$segments = explode('-', $guid);
		$domain_id = decodeGuidSegment($segments[5]);
		
		$domain = Domeinen::with('database')->find($domain_id);
		if(!$domain){ abort(404, 'Domain not found'); }
		
		$domain->setConfig();
		$domain->database->setConfig();
		
		return $next($request);
	}
}
