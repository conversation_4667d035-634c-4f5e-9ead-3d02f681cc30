<?php

namespace App\Http\Middleware;

use App\Models\StappenApi;
use Closure;
use Illuminate\Http\Request;

class StappenApiInbound{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse) $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next){
        $api = StappenApi::where([
            'type' => 'inbound',
            'method' => $request->route('method'),
        ])->first();

        //Method not found
        if(!$api){
            StappenApi::staticAbort([
                'success' => false,
                'notification' => 'Onjuiste methode.'
            ]);
        }

        $api->inboundAuth($request);

        return $next($request);
    }
}
