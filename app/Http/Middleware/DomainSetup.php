<?php

namespace App\Http\Middleware;

use Closure;
use <PERSON>ie;
use Illuminate\Http\Request;

use DB;
use Config;

class DomainSetup
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next){
        $this->setDomain();
        return $next($request);
    }
    public function setDomain(){
        $user = _user();
        $domein = $user->domeinen->first();

        if($user->active_domain != 0){
            $domein = $user->domeinen->where('id', $user->active_domain)->first();
        }
        if(!$domein){ abort(404); }

        $domein->setConfig();
        $domein->database->setConfig();
    }
}
