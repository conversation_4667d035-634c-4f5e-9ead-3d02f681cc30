<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;
use Closure;
use Auth;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array<int, string>
     */
    protected $except = [
        '/logout',
        'api/*',
        'get100tempbronnen'
    ];

    public function handle($request, Closure $next){
        $ipv4 = getIPv4();
        $ipv6 = getIPv6();

        if(!$ipv4 && !$ipv6){
            return parent::handle($request, $next);
        }

        //Check if ips are the same
        $logout = false;
        if (session('ip_address_v4') && $ipv4 && session('ip_address_v4') !== $ipv4) {
            $logout = true;
        }
        else if (session('ip_address_v6') && $ipv6 && session('ip_address_v6') !== $ipv6) {
            $logout = true;
        }

        //Set IP's
        if(!session('ip_address_v4')){
            session(['ip_address_v4' => $ipv4]);
        }
        if(!session('ip_address_v6')){
            session(['ip_address_v6' => $ipv6]);
        }

        //Logout if ips changed
        if($logout && Auth::check()){
            auth()->logout();
            session()->invalidate();
            session()->regenerateToken();
            return redirect('dashboard/login')->with('danger', 'Uw sessie is verlopen');
        }

        return parent::handle($request, $next);
    }

}
