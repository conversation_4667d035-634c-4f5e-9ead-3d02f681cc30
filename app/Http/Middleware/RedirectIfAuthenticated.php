<?php

namespace App\Http\Middleware;

use App\Models\Domeinen;
use App\Models\User;
use App\Providers\RouteServiceProvider;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RedirectIfAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @param  string|null  ...$guards
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, ...$guards){
        $guards = empty($guards) ? [null] : $guards;
        $redirect = _get('redirect') ? urldecode(_get('redirect')) : RouteServiceProvider::HOME;

        foreach ($guards as $guard) {
            if (Auth::guard($guard)->check()) {
                User::setDomainFromGUID( _get('domain') );
                return redirect($redirect);
            }
        }

        return $next($request);
    }
}
