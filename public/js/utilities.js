function ajax(route, data = {csrf: null}){
  const csrf = $('[name="csrf-token"]').attr('content');
  const url = window.location.origin;

  if(route.slice(0, 1) === '/'){
    route = route.slice(1, route.length);
  }

  if(!data){data = {}}

  if(Array.isArray(data)){
    data.push({name: '_token', value: csrf})
  }
  else{
    data._token = csrf;
  }

  return $.ajax({
    type: "POST",
    url: `${url}/${route}`,
    data: data,
  });
}

const delay = ms => new Promise(res => setTimeout(res, ms))

function _file(guid) {
  return `${window.location.origin}/dashboard/api/file/get/${guid}`;
}

function redirect(url, timeout = 0, blank = false) {
  if (url.startsWith('/')) {
    url = url.substring(1);
  }
  if (url.startsWith('dashboard')) {
    url = url.substring(9);
  }
  if (url.startsWith('/')) {
    url = url.substring(1);
  }

  url = `${window.origin}/dashboard/${url}`

  redirectURL(url, timeout, blank);
}
function redirectURL(url, timeout = 0, blank = false) {
  setTimeout(() => {
    if (blank) {
      window.open(url, '_blank');
      return
    }

    location.href = url;
  }, timeout);
}

function showModal(element) {
  if (typeof element == 'object') {
    $(element).modal("show");
  } else {
    $(`#${element}`).modal("show");
  }
  bodyModalClass();
}
function hideModal(element) {
  if (typeof element == 'object') {
    $(element).modal("hide");
  } else {
    $(`#${element}`).modal("hide");
  }
}
function bodyModalClass(time = 400) {
  setTimeout(() => {
    $('body').addClass('modal-open');
  }, time)
}

function getLocation(postcode = null, huisnummer = null) {
  return $.ajax({
    type: "POST",
    url: `${_global.url}/dashboard/api/location`,
    data: {
      postcode: postcode,
      huisnummer: huisnummer,
    },
  });
}
function copyText() {
  const textToCopy = document.getElementById("queryOutput").textContent;
  const textArea = document.createElement("textarea");
  textArea.value = textToCopy;
  document.body.appendChild(textArea);
  textArea.select();
  document.execCommand("copy");
  document.body.removeChild(textArea);
}
function parseDate(target = null) {
  const d = target ? new Date(target) : new Date();

  const time = `0${d.getHours()}`.slice(-2) + ':' + `0${d.getMinutes()}`.slice(-2);
  const date = {
    us: +d.getFullYear() + '-' + `0${d.getMonth() + 1}`.slice(-2) + '-' + `0${d.getDate()}`.slice(-2),
    eu: `0${d.getDate()}`.slice(-2) + '-' + `0${d.getMonth() + 1}`.slice(-2) + '-' + d.getFullYear(),
  };
  date.timestamp = new Date(date.us).getTime();

  const year = d.getFullYear();
  const month = d.getMonth() + 1;
  const day = d.getDate();
  const dayOfWeek = d.getDay();

  // Calculate the week number
  const januaryFirst = new Date(year, 0, 1);
  const daysDifference = Math.ceil((d - januaryFirst) / (24 * 60 * 60 * 1000));
  const weekNumber = Math.ceil((daysDifference + januaryFirst.getDay() + 1) / 7);

  const isWeekend = dayOfWeek == 0 || dayOfWeek == 6;

  return {
    time: time,
    timestamp: d.getTime(),
    date: date,
    year: year,
    month: month,
    month_name: getMaanden(month),
    month_short_name: getShortMaanden(month),
    day: day,
    day_full: `0${day}`.slice(-2),
    dayOfWeek: dayOfWeek,
    weekNumber: weekNumber,
    isWeekend: isWeekend,
    addMinutes: minutes => {
      const _date = d.setMinutes(d.getMinutes() + Number(minutes));
      return now(_date)
    },
    subMinutes: minutes => {
      const _date = d.setMinutes(d.getMinutes() - Number(minutes));
      return now(_date)
    },
    addHours: hours => {
      const _date = d.setHours(d.getHours() + Number(hours));
      return now(_date)
    },
    subHours: hours => {
      const _date = d.setHours(d.getHours() - Number(hours));
      return now(_date)
    },
    addDays: days => {
      const _date = d.setDate(d.getDate() + Number(days));
      return now(_date)
    },
    subDays: days => {
      const _date = d.setDate(d.getDate() - Number(days));
      return now(_date)    },
    addMonths: months => {
      const _date = d.setMonth(d.getMonth() + Number(months));
      return now(_date)
    },
    subMonths: months => {
      const _date = d.setMonth(d.getMonth() - Number(months));
      return now(_date)
    },
    addYears: years => {
      const _date = d.setFullYear(d.getFullYear() + Number(years));
      return now(_date)
    },
    subYears: years => {
      const _date = d.setFullYear(d.getFullYear() - Number(years));
      return now(_date)
    }
  };
}

function keyType(key_code){
  if (key_code >= 48 && key_code <= 57) {
    return 'number'
  }
  else if (key_code >= 65 && key_code <= 90 || key_code >= 97 && key_code <= 122) {
    return 'letter'
  }

  return null;
}

function toPrice(price){
  price = Number(price);
  return price.toLocaleString('de-DE', { maximumFractionDigits: 2, minimumFractionDigits: 2 })
}

function htmlEncode(string){
  const tempElement = document.createElement("div");
  tempElement.textContent = string;
  return tempElement.innerHTML;
}

//Strings
function isJson(string){
  try{
    JSON.parse(string);
    return true;
  }
  catch (e) {
    return false;
  }
}

//Livewire
function livewireEmitPromise(key, value){
  return new Promise(resolve => {
    Livewire.on('emit-response', resolve);
    Livewire.emit(key, value);
  });
}

//Internal messages
function _dispatchEvent(key, data){
  const event = new CustomEvent(key, { detail: data });
  document.dispatchEvent(event);
}
function internalMessageSend(key, data){
  return new Promise(resolve => {
    const id = randomString();

    $(document).on(`IM-RESPONSE-${id}`, (e) => {
      $(document).off(`IM-RESPONSE-${id}`);
      resolve(e.detail);
    })

    _dispatchEvent(`IM-MESSAGE-${key}`, {
      id: id,
      data: data,
    })

  });
}
function internalMessageListen(key, fn){
  $(document).on(`IM-MESSAGE-${key}`, async (e) => {
    const {id, data} = e.detail;
    const response_data = await fn(data);

    _dispatchEvent(`IM-RESPONSE-${id}`, response_data);

  })
}

//Date
function getMaanden(maand = null){
  const maanden = {};
  maanden[1] = "Januari";
  maanden[2] = "Februari";
  maanden[3] = "Maart";
  maanden[4] = "April";
  maanden[5] = "Mei";
  maanden[6] = "Juni";
  maanden[7] = "Juli";
  maanden[8] = "Augustus";
  maanden[9] = "September";
  maanden[10] = "Oktober";
  maanden[11] = "November";
  maanden[12] = "December";
  if(maand !== null){
    return maanden[maand] ?? '';
  }
  return maanden;
}
function getShortMaanden(maand = null){
  const maanden = {};
  maanden[1] = "Jan";
  maanden[2] = "Feb";
  maanden[3] = "Maa";
  maanden[4] = "Apr";
  maanden[5] = "Mei";
  maanden[6] = "Jun";
  maanden[7] = "Jul";
  maanden[8] = "Aug";
  maanden[9] = "Sep";
  maanden[10] = "Okt";
  maanden[11] = "Nov";
  maanden[12] = "Dec";
  if(maand !== null){
    return maanden[maand] ?? '';
  }
  return maanden;
}
