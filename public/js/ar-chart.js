const _charts = {
    instances: [],
}

function initChart(id, config){
    let canvas;

    if(['.', '#', '['].includes(id.slice(0, 1))){
        canvas = $(id).get(0);
    }
    else{
        const canvas_by_id = $(`#${id}`).get(0);
        const canvas_by_class = $(`.${id}`).get(0);
        const canvas_by_data = $(`[${id}]`).get(0);

        canvas = canvas_by_id || canvas_by_class || canvas_by_data

    }

    if(!canvas){
        notification('Er is iets fout gegaan!');
        return;
    }

    for(const dataset of config.data.datasets){

        if(!dataset.borderRadius){
            dataset.borderRadius = 6
        }
        if(!dataset.borderWidth){
            dataset.borderWidth = 1
        }
        if(!dataset.borderColor){
            dataset.borderColor = '#FFFFFF'
        }
        if(!dataset.backgroundColor){
            dataset.backgroundColor = '#01689bBF';
        }
    }

    const existing_chart = _charts.instances.find(chart => chart.id == id);
    if(existing_chart){
        const { chart } = existing_chart

        chart.data.labels = config.data.labels;

        for(const d in config.data.datasets){
            const dataset = config.data.datasets[d];

            if(!chart.data.datasets[d]){
                chart.data.datasets[d] = dataset;
                continue;
            }

            chart.data.datasets[d].labels = dataset.labels
            chart.data.datasets[d].data = dataset.data
        }


        chart.update();
        return;
    }

    const container = findContainer('chart-container', canvas);
    if(container){
        container.find('.chart-loader').remove();
    }

    const chart = new Chart(canvas, config);
    _charts.instances.push({
        id: id,
        chart: chart,
    })
}
function initChartOptions(params){
    let { options, title, legend, legend_position } = params;

    if(options){ return options; }

    return {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: legend ?? false,
                position: legend_position ?? 'top',
            },
            title: {
                display: !!title,
                text: title,
            }
        }
    }
}

function initDyamicChart(data){
    switch (data.type) {
        case 'bar': initBarChart(data); break;
        case 'horizontalBar': initHorizontalBarChart(data); break;
        case 'stackedHorizontalBar': initStackedHorizontalBarChart(data); break;
        case 'doughnut': initDoughnutChart(data); break;
        default:
            notification(`Chart type ${data.type} niet gevonden!`);
            console.log(data);
            break;
    }

}
function initBarChart(params){
    let { id, labels, datasets } = params;

    const options = initChartOptions(params);

    initChart(id, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: datasets,
        },
        options: options,
    });
}
function initHorizontalBarChart(params){
    let { id, labels, datasets } = params;

    const options = initChartOptions(params);
    options.indexAxis = 'y';

    initChart(id, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: datasets,
        },
        options: options,
    });
}
function initStackedHorizontalBarChart(params){
    let { id, labels, datasets } = params;

    const options = initChartOptions(params);
    options.indexAxis = 'y';
    options.scales = {
        x: {
            stacked: true,
        },
        y: {
            stacked: true
        }
    };

    initChart(id, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: datasets,
        },
        options: options,
    });
}

function initDoughnutChart(params){
    let { id, labels, datasets } = params;

    const options = initChartOptions(params);

    initChart(id, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: datasets,
        },
        options: options,
    });
}
