/**
 * Minified by jsDelivr using UglifyJS v3.1.10.
 * Original file: /npm/jsonview@1.2.0/dist/jquery.jsonview.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
!function(e){var n,t,l,r;l=function(){function e(e){null==e&&(e={}),this.options=e}return e.prototype.htmlEncode=function(e){return null!==e?e.toString().replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/</g,"&lt;").replace(/>/g,"&gt;"):""},e.prototype.jsString=function(e){return e=JSON.stringify(e).slice(1,-1),this.htmlEncode(e)},e.prototype.decorateWithSpan=function(e,n){return'<span class="'+n+'">'+this.htmlEncode(e)+"</span>"},e.prototype.valueToHTML=function(e,n){var t;return null==n&&(n=0),t=Object.prototype.toString.call(e).match(/\s(.+)]/)[1].toLowerCase(),this[t+"ToHTML"].call(this,e,n)},e.prototype.nullToHTML=function(e){return this.decorateWithSpan("null","null")},e.prototype.numberToHTML=function(e){return this.decorateWithSpan(e,"num")},e.prototype.stringToHTML=function(e){var n,t;return/^(http|https|file):\/\/[^\s]+$/i.test(e)?'<a href="'+this.htmlEncode(e)+'"><span class="q">"</span>'+this.jsString(e)+'<span class="q">"</span></a>':(n="",e=this.jsString(e),this.options.nl2br&&(t=/([^>\\r\\n]?)(\\r\\n|\\n\\r|\\r|\\n)/g).test(e)&&(n=" multiline",e=(e+"").replace(t,"$1<br />")),'<span class="string'+n+'">"'+e+'"</span>')},e.prototype.booleanToHTML=function(e){return this.decorateWithSpan(e,"bool")},e.prototype.arrayToHTML=function(e,n){var t,l,r,o,s,i,a,p;for(null==n&&(n=0),l=!1,s="",o=e.length,r=a=0,p=e.length;a<p;r=++a)i=e[r],l=!0,s+="<li>"+this.valueToHTML(i,n+1),o>1&&(s+=","),s+="</li>",o--;return l?(t=0===n?"":" collapsible",'[<ul class="array level'+n+t+'">'+s+"</ul>]"):"[ ]"},e.prototype.objectToHTML=function(e,n){var t,l,r,o,s,i;null==n&&(n=0),l=!1,o="",r=0;for(s in e)r++;for(s in e)i=e[s],l=!0,o+='<li><span class="prop"><span class="q">"</span>'+this.jsString(s)+'<span class="q">"</span></span>: '+this.valueToHTML(i,n+1),r>1&&(o+=","),o+="</li>",r--;return l?(t=0===n?"":" collapsible",'{<ul class="obj level'+n+t+'">'+o+"</ul>}"):"{ }"},e.prototype.jsonToHTML=function(e){return'<div class="jsonview">'+this.valueToHTML(e)+"</div>"},e}(),"undefined"!=typeof module&&null!==module&&(module.exports=l),t={bindEvent:function(e,n){var t;if(t=document.createElement("div"),t.className="collapser",t.innerHTML=n?"+":"-",t.addEventListener("click",function(e){return function(n){return e.toggle(n.target)}}(this)),e.insertBefore(t,e.firstChild),n)return this.collapse(t)},expand:function(e){var n,t;return t=this.collapseTarget(e),n=t.parentNode.getElementsByClassName("ellipsis")[0],t.parentNode.removeChild(n),t.style.display="",e.innerHTML="-"},collapse:function(e){var n,t;return t=this.collapseTarget(e),t.style.display="none",n=document.createElement("span"),n.className="ellipsis",n.innerHTML=" &hellip; ",t.parentNode.insertBefore(n,t),e.innerHTML="+"},toggle:function(e){return"none"===this.collapseTarget(e).style.display?this.expand(e):this.collapse(e)},collapseTarget:function(e){var n;if((n=e.parentNode.getElementsByClassName("collapsible")).length)return n[0]}},n=e,r={collapse:function(e){if("-"===e.innerHTML)return t.collapse(e)},expand:function(e){if("+"===e.innerHTML)return t.expand(e)},toggle:function(e){return t.toggle(e)}},n.fn.JSONView=function(){var e,o,s,i,a,p,c;return e=arguments,null!=r[e[0]]?(a=e[0],this.each(function(){var t,l;return t=n(this),null!=e[1]?(l=e[1],t.find(".jsonview .collapsible.level"+l).siblings(".collapser").each(function(){return r[a](this)})):t.find(".jsonview > ul > li > .collapsible").siblings(".collapser").each(function(){return r[a](this)})})):(i=e[0],p=e[1]||{},o={collapsed:!1,nl2br:!1},p=n.extend(o,p),s=new l({nl2br:p.nl2br}),"[object String]"===Object.prototype.toString.call(i)&&(i=JSON.parse(i)),c=s.jsonToHTML(i),this.each(function(){var e,l,r,o,s,i;for((e=n(this)).html(c),i=[],o=0,s=(r=e[0].getElementsByClassName("collapsible")).length;o<s;o++)"LI"===(l=r[o]).parentNode.nodeName?i.push(t.bindEvent(l.parentNode,p.collapsed)):i.push(void 0);return i}))}}(jQuery);
//# sourceMappingURL=/sm/2eadfe96794908cc7494cbdb3b1b7d45b2ca47e3bbf51ecd31d3a117f19901bc.map
