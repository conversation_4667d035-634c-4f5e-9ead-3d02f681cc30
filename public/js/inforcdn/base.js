const __infor_base={regex_ignore:/[`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/g,}
$.fn.rotate=function(degree){$(this).addClass('transition-03');if(degree===!1){$(this).css({transform:``})}
    const css=$(this).css('transform');if(css=='none'||!css){$(this).css({transform:`rotate(${degree}deg)`})}else{$(this).css({transform:``})}}
const _timer=ms=>new Promise(res=>setTimeout(res,ms))
function _url(path=''){if(path&&path.slice(0,1)!='/'){path='/'+path}
    return window.location.origin+path}
function _window(location,def=null,execute=!0){if(!location){return def}
    const parts=location.split('.');let element=window;for(const part of parts){element=element[part];if(element===undefined){return def}}
    if(typeof element==='function'){return execute?(element()||def):element}
    return element||def}
function _get(name,def=null){let result=def;let tmp=[];location.search.substr(1).split("&").forEach(function(item){tmp=item.split("=");if(tmp[0]===name)result=decodeURIComponent(tmp[1]);});return result}
function _cookie(name,def=null){var nameEQ=name+"=";var ca=document.cookie.split(';');for(var i=0;i<ca.length;i++){var c=ca[i];while(c.charAt(0)==' ')c=c.substring(1);if(c.indexOf(nameEQ)!=-1)return c.substring(nameEQ.length,c.length);}
    return def}
function _cookieSet(name,value,days=30){const date=new Date();date.setTime(date.getTime()+(days*24*3600*1000))
    document.cookie=`${name}=${value};expires=${date.toUTCString()};path=/`}
function _cookieRemove(name){document.cookie=`${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`}
function _ajax(route,data={csrf:null}){const csrf=$('[name="csrf-token"]').attr('content');const url=window.location.origin;if(route.slice(0,1)==='/'){route=route.slice(1,route.length)}
    if(!data){data={}}
    if(Array.isArray(data)){data.push({name:'_token',value:csrf})}else{data._token=csrf}
    return $.ajax({type:"POST",url:`${url}/${route}`,data:data,})}
function findContainer(cls,element){let container=null;$(element).parents().each(function(){if($(this).hasClass(cls)){container=$(this);return!1}});return container}
function findContainerByAttr(attr,element,value=null){let container=null
    $(element).parents().each(function(){const div=$(this);$.each(this.attributes,function(){if(this.name==attr){if(!value){container=div}
        if(value==this.value){container=div}}});if(container){return!1}});return container}
function findContainerByTag(tag,element){let container=null;$(element).parents().each(function(){if(this.tagName==tag.toUpperCase()){container=$(this);return!1}});return container}
function pageInteractive(func=()=>{}){if(document.readyState=='interactive'){func()}
    document.addEventListener('readystatechange',(event)=>{if(document.readyState=='interactive'){func()}})}
function pageComplete(func=()=>{}){if(document.readyState=='complete'){func()}
    document.addEventListener('readystatechange',(event)=>{if(document.readyState=='complete'){func()}})}
function randomString(length=10){var result='';var characters='ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';var charactersLength=characters.length;for(var i=0;i<length;i++){result+=characters.charAt(Math.floor(Math.random()*charactersLength))}
    localStorage.randomString=result;return result}
function lastString(){return localStorage.randomString}
function parseInforLivewireParams(string){if(!string){return null}
    try{if(string.slice(0,1)!==' '){string=` ${string}`}
        const formatValue=(s)=>{const number=Number(s);if(!isNaN(number)){return number}
            return s}
        const pairs=string.split(' :');const response={};for(const pair of pairs){if(!pair){continue}
            const equalIndex=pair.indexOf('=');const key=pair.substring(0,equalIndex);const value=pair.substring(equalIndex+1);if(!key){continue}
            response[key]=formatValue(value)}
        return response}catch(e){return null}}
function parseInforElementParams(string){if(!string){return''}
    const params=parseInforLivewireParams(string)
    let element_params='';for(const i in params){element_params+=`data-${i}="${params[i]}" `}
    return element_params}
$(document).click(function(){overflowXOnlyRemove()});function overflowXOnlyRemove(){$('[data-overflow-x-only]').css({height:''})}
function overflowXOnlyInit(){$('[data-overflow-x-only]').each(function(){const div=$(this);const height=div.outerHeight();const scroll=div.prop("scrollHeight");if(scroll>Math.ceil(height)){div.css({height:scroll+35})}})}
