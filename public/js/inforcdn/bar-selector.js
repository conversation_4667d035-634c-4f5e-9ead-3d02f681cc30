const _inforBarSelector={instances:{},get:(id)=>{return _inforBarSelector.instances[id]},instanceByContainer:container=>{const id=container.attr('id');return _inforBarSelector.instances[id]}}
function initInforBarSelector(){$('infor-bar-selector').each((index,element)=>{const container=$(element);const id=container.attr('id')||randomString(10);const can_be_empty=container.attr('data-can-be-empty')!==undefined;const options=container.find('infor-bar-selector-option');const initiated=container.attr('data-initiated');if(initiated!==undefined){return!0}
    if(!options.filter('[data-selected]').length){options.eq(0).attr('data-selected','')}
    const option=options.filter('[data-selected]');if(!options.length&&!can_be_empty){container.html('<infor-bar-selector-placeholder data-empty-bs >Geen Data</infor-bar-selector-placeholder>')}
    container.append(`<infor-bar-selector-indicator></infor-bar-selector-indicator>`)
    container.attr('id',id);container.attr('data-initiated',!0);const indicator=container.find('infor-bar-selector-indicator');_inforBarSelector.instances[id]={container:container,indicator:indicator,value:option.attr('data-value'),options:()=>{return container.find('infor-bar-selector-option')},onchange:(value)=>{},select:(value,trigger=!0)=>{selectInforBarOption(container.find(`infor-bar-selector-option[data-value="${value}"]`),trigger)},selectEq:(index,trigger=!0)=>{selectInforBarOption(container.find(`infor-bar-selector-option`).eq(index),trigger)},values:()=>{const arr=[];container.find('infor-bar-selector-option').each(function(){arr.push({name:$(this).text(),value:$(this).attr('data-value'),})})
            return arr},removeValue:(value)=>{container.find(`infor-bar-selector-option[data-value="${value}"]`).remove();const options=container.find('infor-bar-selector-option');if(!options.length){container.html('<infor-bar-selector-placeholder data-empty-bs >Geen Data</div>')}},removeAllValues:()=>{container.find(`infor-bar-selector-option[data-value]`).remove();if(!options.length){container.html('<infor-bar-selector-placeholder data-empty-bs >Geen Data</div>')}},addValue:(options)=>{const{name,value,classes}=options;if(!name||value===undefined||value===null){return}
            container.find('[data-empty-bs]').remove();container.append(`<infor-bar-selector-option class="${classes || ''}" data-value="${value}">${name}</infor-bar-selector-option>`)}}})}
function movePointerToOption(option){const container=findContainerByTag('infor-bar-selector',option);const instance=_inforBarSelector.instanceByContainer(container);const{options,indicator}=instance;const dimensions={h:option.outerHeight(),w:option.outerWidth(),left:option.position().left+Number(option.css('margin-left').replace('px',''))+container.scrollLeft(),}
    const{h,w,left}=dimensions;options().removeAttr('data-selected');options().removeAttr('data-highlight');option.attr('data-selected','');option.attr('data-highlight','');if(left.toFixed(1)===Number(indicator.css('left').replace('px','')).toFixed(1)){return}
    if(indicator.hasClass('initial')){indicator.attr('style',`width: ${w}px; height: ${h}px; top: 50%; left: ${left}px; transform: translateY(-50%);`);return}
    indicator.attr('style',`width: ${w}px; height: ${h}px; top: 50%; left: ${left}px; transform: translateY(-50%) scaleX(1.1) scaleY(.9);`);setTimeout(()=>{indicator.attr('style',`width: ${w}px; height: ${h}px; top: 50%; left: ${left}px; transform: translateY(-50%) scaleX(1) scaleY(1);`)},150)}
function correctInforBarSelector(){$.each(_inforBarSelector.instances,(id,instance)=>{instance.select(instance.value)})}
function silentCorrectInforBarSelector(){$.each(_inforBarSelector.instances,(id,instance)=>{instance.select(instance.value,!1)})}
function initialInforBarCorrection(){$.each(_inforBarSelector.instances,(id,instance)=>{const trigger=instance.container.attr('data-trigger')!=='false';instance.indicator.addClass('initial');instance.select(instance.value,trigger);setTimeout(()=>{instance.indicator.removeClass('initial')},50)})}
function selectInforBarOption(option,trigger=!0){const container=findContainerByTag('infor-bar-selector',option);const instance=_inforBarSelector.instanceByContainer(container);if(!instance){console.log(`Display select instance not found, see _inforBarSelector for more informations`);return}
    movePointerToOption(option);instance.value=option.attr('data-value');if(trigger){instance.onchange(instance.value)}}
$(document).on('click','infor-bar-selector-option',function(){const option=$(this);selectInforBarOption(option)});pageInteractive(initInforBarSelector);pageComplete(()=>{setTimeout(initialInforBarCorrection,50)})
