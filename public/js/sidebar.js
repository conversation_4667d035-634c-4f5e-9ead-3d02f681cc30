
const _sidebar = {
  transition_css: 200,
  transition: 300,
}

$(document).on('click', '.nav-item', async function(){


  const container = findContainer('nav-item-container', this);
  const is_sub_nav = findContainer('nav-item-dropdown', this);
  const has_dropdown = container.find('.nav-item-dropdown').length !== 0;
  const is_active = container.attr('data-active') !== undefined;

  const sidebar = $('.sidebar');
  const hidden = sidebar.attr('data-hidden') !== undefined;

  if(is_sub_nav){ return; }
  if(hidden && has_dropdown){
    $('.nav-toggle-btn').trigger('click');
  }

  is_active
    ? container.removeAttr('data-active')
    : container.attr('data-active', true);

  container.find('.nav-item-dropdown').eq(0).toggle(_sidebar.transition);
  container.find('.fa-chevron-right').eq(0).rotate(90);

  const containers = $('.nav-item-container[data-active]').not(container);
  containers.find('.nav-item-dropdown').eq(0).toggle(_sidebar.transition);
  containers.find('.fa-chevron-right').eq(0).rotate(90);
  containers.removeAttr('data-active');
});
$(document).on('mouseup', '.nav-logo', (event) => {
  const { button } = event;

  if(button === 0){
    redirect('/home')
  }
  else if(button === 1){
    redirect('/home', 0, true);
  }

});
$(document).on('click', '.nav-toggle-btn', function(){
  const sidebar = $('.sidebar');
  const main = $('main');
  const hidden = sidebar.attr('data-hidden') !== undefined;

  if(hidden){
    sidebar.removeAttr('data-hidden')
    main.removeAttr('data-sidebar-hidden');
  }
  else{
    sidebar.find('.nav-item-container[data-active]').find('.nav-item').eq(0).trigger('click');
    sidebar.attr('data-hidden', true);
    main.attr('data-sidebar-hidden', true);
  }

  _cookieSet('sidebar_hidden', !hidden);
})

function toggleNav(state){
  const sidebar = $('.sidebar');
  const visible = sidebar.attr('data-hidden') === undefined;

  if (state !== visible){
        $('.nav-toggle-btn').trigger('click');
  }
}
