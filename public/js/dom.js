$(document).ready(() => {
    tippyInit();
    Livewire.on('notification', notification);
    Livewire.on('redirect', redirect);
    Livewire.hook('message.sent', () => {
      $('.corner-loader').html('<span class="text-primary" >Syncing...</span>')
    } )
    Livewire.hook('message.processed', (message, component) => {
      tippyInit();
      tippyUpdate();
      setTimeout(clearLoader, 100)
      
      initInforSearch();
      initInforSelectSearch();
      initInforSelectMultiple();
      initInforSelectEdit();
    })
    Livewire.hook('message.failed', (message, component) => {
      console.log(message, component);
    })
})
$(document).on('submit', '[data-main-form]', function(){
    const form  = $(this);
    const color = form.attr('data-main-form') || 'success';

    if(form.find('[data-main-form-spinner]').length){ return }

    form.find('[type=submit]').addClass('d-none').after(`<div class="spinner-border text-${color}" data-main-form-spinner role="status"><span class="sr-only"></span></div>`);
})

//Page loader
document.addEventListener('readystatechange', (event) => {
    if(document.readyState == 'interactive'){
      $('[data-page-loader]').remove();
      $('[data-main-content]').removeClass('opacity-0');
    }
});

const _tippy = {
    instances: [],
}
function tippyInit(){
  $('[data-tippy-content]').each(function () {
    const instance = tippy(this, {
      allowHTML: true,
    });

    if (instance) {
      _tippy.instances.push(instance);
    }
  });
}
function tippyUpdate(){
  for(const ins of _tippy.instances){
    if(!ins){continue;}
    ins.setContent($(ins.reference).attr('data-tippy-content'));
  }
}


function notification(text = '', bg = 'danger', duration = 5){
    const string = randomString();

    $('.corner-notifications').append(
        `<div class="w-100 transition-03 text-white" id="${string}" style="display: none" >
            <div class="bg-${bg} shadow d-flex justify-content-between mb-1 overflow-hidden rounded-3" >
              <div class="p-2" >${text}</div>
              <div class="p-2" ><a onclick="deleteNotification('#${string}')" class="px-1 cursor-pointer" ><i class="fa fa-xmark m-0"></i></a></div>
          </div>
        </div>`
    )


    const element = $(`#${string}`)

    element.toggle(250);

    setTimeout(() => {
        element.toggle(150);
        setTimeout(() => {
            element.remove();
        },150);
    },(duration * 1000))
}
function deleteNotification(string){
    $(string).toggle(250);
    setTimeout(() => { $(string).remove() },250)
}

function loader(text = ''){
  if (text){text = `<span class="mr-2" >${text}</span>`}
  $(".corner-loader").html(`<div class="corner-loader-content text-primary" >${text} <div class="loader"></div>`)
}
function successLoader(text = '', time = 1.5){
  const corner_loader = $('.corner-loader');

  corner_loader.html(`<div class="corner-loader-content text-success" >${text}<i class="fa-solid fa-check scale-2 m-2"></i></div>`);
  setTimeout(() => {
    corner_loader.empty();
  }, time * 1000)
}
function errorLoader(error = 'Er is iets foutgegaan!', time = 5){
  const corner_loader = $('.corner-loader');

  error = error || 'Er is iets foutgegaan!';
  if (error){error = `<span class="mr-2" >${error}</span>`}

  corner_loader.html(`<div class="corner-loader-content text-danger" >${error}<i class="fa-solid fa-xmark scale-2 m-2"></i></div>`);
  setTimeout(() => {
    corner_loader.empty();
  }, time * 1000)}
function clearLoader(){
  $(".corner-loader").empty();
}

function viewJson(json){
    confirmModal({
        text: '<div data-json class="codeblock" ></div>',
        hideFooter: true,
        large: true,
    })

    isJson(json)
        ? $('[data-json]').JSONView(json)
        : $('[data-json]').html(htmlEncode(json));
}

//Livewire
const _livewireContentBlocker = {
  pending_messages: 0,
  timout: null,
}
function livewireContentBlocker() {
  Livewire.hook('message.sent', livewireContentBlockerSet);
  Livewire.hook('message.processed', livewireContentBlockerRemove)
}
function livewireContentBlockerSet(){
    if (!_livewireContentBlocker.pending_messages && !$('[data-livewire-content-blocker]').length) {
      $('body').append(`<div class="position-fixed w-100 h-100 top-0 start-0 z-index-9999 bg-dark opacity-66 rounded flex-center flex-column scale-15" data-livewire-content-blocker > <div class="loader"></div> <div class="text-muted mt-2" data-livewire-content-blocker-text ></div> </div>`)
    }
    if (_livewireContentBlocker.timout) {
      clearTimeout(_livewireContentBlocker.timout);
      _livewireContentBlocker.timout = null;
    }

    _livewireContentBlocker.pending_messages++;
}
function livewireContentBlockerRemove(){
    _livewireContentBlocker.pending_messages += -1;

    if (_livewireContentBlocker.timout) {
      clearTimeout(_livewireContentBlocker.timout);
      _livewireContentBlocker.timout = null;
    }

    if (!_livewireContentBlocker.pending_messages) {
      _livewireContentBlocker.timout = setTimeout(() => {
        $('[data-livewire-content-blocker]').remove();
      }, 1000);
    }
}
function livewireContentBlockerText(string){
    $('[data-livewire-content-blocker-text]').html(string);
}

//Dynamic confirm modal
const _dynamicConfirmModal = {
  modal: $('#dynamic-confirm-modal'),
  content: $('#dynamic-confirm-modal').find('.modal-body'),
  footer: $('#dynamic-confirm-modal').find('.modal-footer'),
}
const _dynamicConfirmModalLarge = {
  modal: $('#dynamic-confirm-modal-large'),
  content: $('#dynamic-confirm-modal-large').find('.modal-body'),
  footer: $('#dynamic-confirm-modal-large').find('.modal-footer'),
}
function confirmModal(options = {text: '', large: false, btnColor: '', btnText: '', btnRejectState: false, btnRejectColor: false, btnRejectText: false, hideFooter: false, execute: null}){
    const string = randomString();

    let {text, large, btnColor, btnText, btnRejectState, btnRejectColor, btnRejectText, hideFooter, execute} = options;

    large = large || false;

    btnColor = btnColor || 'btn-success';
    btnText = btnText || 'Bevestigen';

    btnRejectState = btnRejectState || false;
    btnRejectColor = btnRejectColor || 'btn-danger';
    btnRejectText = btnRejectText || 'Annuleren';

    hideFooter = hideFooter || false;

    const modal = large ? _dynamicConfirmModalLarge : _dynamicConfirmModal;

    modal.content.html(text);
    modal.footer.html(
        `<div>
      ${btnRejectState ? `<a class="btn ${btnRejectColor} text-white reject-${string}" >${btnRejectText}</a>` : ''}
      <a class="btn ${btnColor} text-white resolve-${string}" >${btnText}</a>
    </div>`
    );

    modal.footer.toggleClass('d-none', hideFooter);
    modal.modal.modal('show');

    if(execute){
        execute();
    }

    return new Promise(function(resolve){
        $(`.reject-${string}`).click(() => {
            modal.modal.modal('hide');
            resolve({status: false})
        });
        $(`.resolve-${string}`).click(() => {
            modal.modal.modal('hide');

            const inputs = {};

            modal.modal.find('input, textarea, select').each((index, input) => {
                input = $(input);

                let name = input.attr('name');
                let type = input.attr('type');
                if(!name){ return true; }



                let value = input.val();
                if(type == 'checkbox'){
                    value = {
                        checked: input.prop('checked'),
                        value: input.val(),
                    };
                }
                else if(type == 'file'){
                    value = input.prop('files');
                }

                if(name.endsWith('[]')){
                    name = name.slice(0, -2);
                    if(!inputs[name]){ inputs[name] = []; }

                    inputs[name].push(value);
                }
                else{
                    inputs[name] = value;
                }

            });

            resolve({
                status: true,
                inputs: inputs
            })
        });

        modal.modal.on('hidden.bs.modal', () => {
            resolve({status: false})
        });
    })


}

//Draggable modals
const _draggableModals = {
  grab: false,
  modal: null,
  offset: {
    x: null,
    y: null,
  }
}
$(document).ready(() => {
  $('.modal-content').not('[data-no-grab]').each(function(){
    $(this).addClass('transition-02').css({opacity: '1'})
    $(this).prepend(
      `<div class="m-2" >
          <div class="hover-shadow rounded cursor-pointer modal-grab text-center" ><i class="fa-solid fa-grip m-0 pointer-event-none"></i></div>
      </div>`)
  })
})
$(document).on('mousedown', '.modal-grab', function(event){
  _draggableModals.grab = true;
  _draggableModals.modal = findContainer('modal-dialog', this);

  _draggableModals.modal.find('.modal-content').css({opacity: '.25'})

  const { offsetX, offsetY } = event
  console.log(event);
  _draggableModals.offset.x = offsetX;
  _draggableModals.offset.y = offsetY;
})
$(document).on('mouseup', function(){
  if(!_draggableModals.grab){ return }

  _draggableModals.grab = false
  _draggableModals.modal.find('.modal-content').css({opacity: '1'})
})
$(document).on('mousemove', function(event){
  if(!_draggableModals.grab){ return }

  const { offset } = _draggableModals;
  const grab = _draggableModals.modal.find('.modal-grab');

  const container = findContainer('modal', _draggableModals.modal);
  const scrollY = container.scrollTop();

  const {clientX, clientY} = event;
  const x = clientX - grab.position().left - offset.x;
  const y = clientY - grab.position().top - offset.y + scrollY;

  _draggableModals.modal.css({
    margin: '0 0 100px 0',
    paddingBottom: '100px',
    positon: 'absolute',
    top: y,
    left: x,
  })
})

//Dynamic container toggle
const _dynamicToggle = {
  toggling: {}
}
$(document).on('click', '[data-toggle-btn]', function(){
  const btn = $(this);
  let container = $(this);

  if(!container.attr('data-toggle-container')){
    container = findContainerByAttr('data-toggle-container', container);
  }

  let id_string = container.attr('data-toggle-container');
  if (!id_string){
    id_string = randomString();
    container.attr('data-toggle-container', id_string);
  }

  if (_dynamicToggle.toggling[id_string]){ return; }

  const content = container.find('[data-toggle-content]').eq(0);

  _dynamicToggle.toggling[id_string] = true;
  setTimeout(() => { _dynamicToggle.toggling[id_string] = false }, 310);

  content.toggle(300);
  btn.find('.btn i').rotate(180);
});
