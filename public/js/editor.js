
class ckEditorUploadAdapter {
    constructor( loader ) {
        this.loader = loader;
    }

    upload() {
        return this.loader.file
            .then( file => new Promise( ( resolve, reject ) => {
                this._initRequest();
                this._initListeners( resolve, reject);
                this._sendRequest( file );
            } ) );
    }

    _initRequest() {
        const xhr = this.xhr = new XMLHttpRequest();

        xhr.open( 'POST', `https://${window.location.hostname}/dashboard/api/file/upload`, true );
        xhr.responseType = 'json';
    }
    _initListeners( resolve, reject) {
        const xhr = this.xhr;

        xhr.addEventListener( 'error', (err) => {
            console.log(err);
        } );
        xhr.addEventListener( 'load', () => {
            const file = xhr.response;

            if(!file?.guid) {
                return reject();
            }

            resolve( { default: `https://${window.location.hostname}/dashboard/api/file/get/${file.guid}`} );
        } );
    }
    _sendRequest( file ) {
        const data = new FormData();
        data.append( 'file', file );
        data.append( 'path', '/editor' );

        this.xhr.send( data );
    }
}



//Dynamic editor text
const _editor = {
    modal: $('#dynamic-editor-text-modal'),
    iframe: $("[data-dynamic-editor-iframe]"),
}
function editorInit(id, value = null){
    try{
        if(!$(id).length){return;}

        return new Promise((resolve) => {
            ClassicEditor.create( document.querySelector(id) ,{
                toolbar: [ 'heading',"|",'bold', 'italic', 'link', "|", 'undo', 'redo',"|", 'numberedList', 'bulletedList',"insertTable", '|', 'imageUpload', "imageStyle:wrapText", "imageStyle:breakText"],
            })
                .then((ckEditor) => {
                    if(value){ ckEditor.setData(value); }
                    ckEditor.plugins.get( 'FileRepository' ).createUploadAdapter = ( loader ) => { return new ckEditorUploadAdapter( loader ); };
                    resolve(ckEditor);
                })
                .catch((error) => console.log(error));
        })
    }
    catch(e){
        console.log('Editor catch: ' + id);
    }
}
function getEditorText(options = {}){
    const editor = _editor.iframe.get(0).contentWindow.editor

    editor.setData(options?.text || '');
    showModal(_editor.modal);

    $(document).off('click', '[data-editor-text-modal-reject]')
    $(document).off('click', '[data-editor-text-modal-resolve]')
    return new Promise(resolve => {
        $(document).on('click', '[data-editor-text-modal-reject]', () => {
            hideModal(_editor.modal);
            resolve({status: false})
        })
        $(document).on('click', '[data-editor-text-modal-resolve]', () => {
            const data = editor.getData();

            hideModal(_editor.modal);
            resolve({ status: true, text: data })
        })
    });

}
