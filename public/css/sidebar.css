/*Sidebar*/

:root{
    --sidebar_width: 250px;
    --sidebar_hidden_width: 60px;
    --toggle_width: 35px;
    --transition: .2s;
}

main{
    width: calc(100% - var(--sidebar_width) - var(--toggle_width));
    transition: var(--transition);
}
main[data-sidebar-hidden]{
    width: calc(100% - var(--sidebar_hidden_width) - var(--toggle_width));
}

.sidebar{
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #01689b;
    width: var(--sidebar_width);
    margin-left: 0px;
    transition: var(--transition);
    position: relative;
    z-index: 9;
}

.nav-logo{
    padding: 1rem;
    background-color: white;
    cursor: pointer;
    height: 80px;
    display: flex;
    align-items: center;
    overflow: hidden;
}
.nav-logo [data-logo]{
}
.nav-logo [data-logo="main"]{
    width: calc(var(--sidebar_width) - 2rem);
    display: block;
}
.nav-logo [data-logo="compact"]{
    width: calc(var(--sidebar_hidden_width) - .5rem);
    display: none;
}

.nav-user{
    display: flex;
    align-items: center;
    margin: 1rem;
    border-radius: 10rem;
}
.nav-user-initials{
    padding: 0.25rem;
    border: 1px solid var(--border);
    border-radius: 10rem;
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #596581;
    color: #ffffff;
    font-size: 1rem;
    font-weight: 600;
}
.nav-user-name{
    display: flex;
    flex-direction: column;
    justify-content: center;

    text-decoration: none;
    color: white;
    font-size: 1rem;
}

.nav-item{
    display: flex;
    align-items: center;
    padding: .25rem .75rem .25rem .25rem;
    cursor: pointer;
    text-decoration: none!important;
    color: white!important;
    font-size: 1.1rem;
    white-space: nowrap;
    overflow: hidden;
}
.nav-item:hover{
    color: white;
}
.nav-item-icon i{
    margin: 0 .5rem!important;
    font-size: 1rem;
}
.nav-direction-icon{
    margin-left: auto!important;
}

.nav-item-container{
    font-size: .85rem;
    margin: .1rem .5rem;
    border-radius: 1rem;
    overflow: hidden;
}
.nav-item-container:hover{
    background-color: rgba(255, 255, 255, .15);
}
.nav-item-container:first-of-type{
    margin-top: .50rem;
}
.nav-item-container[data-active]{
    background-color: rgba(255, 255, 255, .15);
}

form.nav-item-container{
    margin: .1rem .5rem!important;
}
form.nav-item-container button[type=submit]{
    background-color: unset;
    width: 100%;
    outline: none;
    border: none;
}


.nav-item-dropdown{
    display: none;
    width: 100%!important;
    padding-top: .5rem;
}
.nav-item-dropdown .nav-item{
    padding-left: .75rem;
    justify-content: start;
    font-size: .95rem;
}
.nav-item-dropdown .nav-item:hover{
    background-color: rgba(255, 255, 255, .15);
    color: #cecece;
}

.nav-item-divider{
    margin: 0.5rem 1rem;
    border-bottom: 1px solid rgba(192, 194, 195, 0.2)!important;
}
.nav-text{
    width: calc(var(--sidebar_width) - 2rem);
    font-size: .85rem;
    margin: .5rem 1rem;
    color: white;
}
.nav-item-container-bottom{
    margin-top: auto;
}

/*Inactive*/
.sidebar[data-hidden]{
    width: var(--sidebar_hidden_width);
}
.sidebar[data-hidden] .nav-text,
.sidebar[data-hidden] .nav-user,
.sidebar[data-hidden] .nav-item .nav-item-label,
.sidebar[data-hidden] .nav-item .nav-direction-icon{
    display: none;
}
.sidebar[data-hidden] .nav-item{
    padding: .25rem;
}
.sidebar[data-hidden] .nav-logo{
    padding: .25rem;
    margin-bottom: .5rem;
}
.sidebar[data-hidden] [data-logo="main"]{
    display: none;
}
.sidebar[data-hidden] [data-logo="compact"]{
    display: block;
}
.sidebar[data-hidden] .nav-item-icon{
    margin: 0 auto;
}


/*Toggle*/
.nav-toggle-container{
    width: var(--toggle_width);
    z-index: 2;
}
.nav-toggle-btn{
    margin-top: 80px;
    transform: translateX(-55%) translateY(16%) rotate(45deg);
    /*transform: translateX(-1.25rem) rotate(45deg);*/
    padding: calc(2rem - 20px) calc(2rem - 17.5px);
    padding-bottom: 50%;
    padding-left: 50%;
    border-radius: 0;
    border-top-right-radius: .5rem;
    background-color: #01689b;
    color: white!important;
}
.nav-toggle-btn i{
    display: inline-block;
    transform: rotate(-45deg) translateX(.45rem);
    font-size: 1.25rem;
}

@media (max-width: 768px){
    main{
        margin-right: calc( -1 * ( var(--sidebar_width) + var(--toggle_width) ) );
        width: 100%;
    }
    main[data-sidebar-hidden]{
        margin-right: 0px;
        width: calc(100% - var(--sidebar_hidden_width) - var(--toggle_width));
    }
    .main-container{
        overflow: hidden;
    }
}
