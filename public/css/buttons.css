.btn-inverse-primary {
    color: #2196f3;
    background-color: rgba(33, 150, 243, 0.2);
    background-image: none;
    border-color: rgba(33, 150, 243, 0); }
.btn-inverse-primary:hover {
    color: #ffffff;
    background-color: #2196f3;
    border-color: #2196f3; }
.btn-inverse-primary.focus, .btn-inverse-primary:focus {
    -webkit-box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.5);
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.5); }
.btn-inverse-primary.disabled, .btn-inverse-primary:disabled {
    color: #2196f3;
    background-color: transparent; }
.btn-inverse-primary.active, .btn-inverse-primary:active,
.show > .btn-inverse-primary.dropdown-toggle {
    color: #ffffff;
    background-color: #2196f3;
    border-color: #2196f3; }

.btn-inverse-secondary {
    color: #c0c2c3;
    background-color: rgba(192, 194, 195, 0.2);
    background-image: none;
    border-color: rgba(192, 194, 195, 0); }
.btn-inverse-secondary:hover {
    color: #ffffff;
    background-color: #c0c2c3;
    border-color: #c0c2c3; }
.btn-inverse-secondary.focus, .btn-inverse-secondary:focus {
    -webkit-box-shadow: 0 0 0 3px rgba(192, 194, 195, 0.5);
    box-shadow: 0 0 0 3px rgba(192, 194, 195, 0.5); }
.btn-inverse-secondary.disabled, .btn-inverse-secondary:disabled {
    color: #c0c2c3;
    background-color: transparent; }
.btn-inverse-secondary.active, .btn-inverse-secondary:active,
.show > .btn-inverse-secondary.dropdown-toggle {
    color: #ffffff;
    background-color: #c0c2c3;
    border-color: #c0c2c3; }

.btn-inverse-success {
    color: #19d895;
    background-color: rgba(25, 216, 149, 0.2);
    background-image: none;
    border-color: rgba(25, 216, 149, 0); }
.btn-inverse-success:hover {
    color: #ffffff;
    background-color: #19d895;
    border-color: #19d895; }
.btn-inverse-success.focus, .btn-inverse-success:focus {
    -webkit-box-shadow: 0 0 0 3px rgba(25, 216, 149, 0.5);
    box-shadow: 0 0 0 3px rgba(25, 216, 149, 0.5); }
.btn-inverse-success.disabled, .btn-inverse-success:disabled {
    color: #19d895;
    background-color: transparent; }
.btn-inverse-success.active, .btn-inverse-success:active,
.show > .btn-inverse-success.dropdown-toggle {
    color: #ffffff;
    background-color: #19d895;
    border-color: #19d895; }

.btn-inverse-info {
    color: #8862e0;
    background-color: rgba(136, 98, 224, 0.2);
    background-image: none;
    border-color: rgba(136, 98, 224, 0); }
.btn-inverse-info:hover {
    color: #ffffff;
    background-color: #8862e0;
    border-color: #8862e0; }
.btn-inverse-info.focus, .btn-inverse-info:focus {
    -webkit-box-shadow: 0 0 0 3px rgba(136, 98, 224, 0.5);
    box-shadow: 0 0 0 3px rgba(136, 98, 224, 0.5); }
.btn-inverse-info.disabled, .btn-inverse-info:disabled {
    color: #8862e0;
    background-color: transparent; }
.btn-inverse-info.active, .btn-inverse-info:active,
.show > .btn-inverse-info.dropdown-toggle {
    color: #ffffff;
    background-color: #8862e0;
    border-color: #8862e0; }

.btn-inverse-warning {
    color: #ffaf00;
    background-color: rgba(255, 175, 0, 0.2);
    background-image: none;
    border-color: rgba(255, 175, 0, 0); }
.btn-inverse-warning:hover {
    color: #ffffff;
    background-color: #ffaf00;
    border-color: #ffaf00; }
.btn-inverse-warning.focus, .btn-inverse-warning:focus {
    -webkit-box-shadow: 0 0 0 3px rgba(255, 175, 0, 0.5);
    box-shadow: 0 0 0 3px rgba(255, 175, 0, 0.5); }
.btn-inverse-warning.disabled, .btn-inverse-warning:disabled {
    color: #ffaf00;
    background-color: transparent; }
.btn-inverse-warning.active, .btn-inverse-warning:active,
.show > .btn-inverse-warning.dropdown-toggle {
    color: #ffffff;
    background-color: #ffaf00;
    border-color: #ffaf00; }

.btn-inverse-danger {
    color: #ff6258;
    background-color: rgba(255, 98, 88, 0.2);
    background-image: none;
    border-color: rgba(255, 98, 88, 0); }
.btn-inverse-danger:hover {
    color: #ffffff;
    background-color: #ff6258;
    border-color: #ff6258; }
.btn-inverse-danger.focus, .btn-inverse-danger:focus {
    -webkit-box-shadow: 0 0 0 3px rgba(255, 98, 88, 0.5);
    box-shadow: 0 0 0 3px rgba(255, 98, 88, 0.5); }
.btn-inverse-danger.disabled, .btn-inverse-danger:disabled {
    color: #ff6258;
    background-color: transparent; }
.btn-inverse-danger.active, .btn-inverse-danger:active,
.show > .btn-inverse-danger.dropdown-toggle {
    color: #ffffff;
    background-color: #ff6258;
    border-color: #ff6258; }

.btn-inverse-light {
    color: #fbfbfb;
    background-color: rgba(251, 251, 251, 0.2);
    background-image: none;
    border-color: rgba(251, 251, 251, 0); }
.btn-inverse-light:hover {
    color: #ffffff;
    background-color: #fbfbfb;
    border-color: #fbfbfb; }
.btn-inverse-light.focus, .btn-inverse-light:focus {
    -webkit-box-shadow: 0 0 0 3px rgba(251, 251, 251, 0.5);
    box-shadow: 0 0 0 3px rgba(251, 251, 251, 0.5); }
.btn-inverse-light.disabled, .btn-inverse-light:disabled {
    color: #fbfbfb;
    background-color: transparent; }
.btn-inverse-light.active, .btn-inverse-light:active,
.show > .btn-inverse-light.dropdown-toggle {
    color: #ffffff;
    background-color: #fbfbfb;
    border-color: #fbfbfb; }

.btn-inverse-dark {
    color: #252C46;
    background-color: rgba(37, 44, 70, 0.2);
    background-image: none;
    border-color: rgba(37, 44, 70, 0); }
.btn-inverse-dark:hover {
    color: #ffffff;
    background-color: #252C46;
    border-color: #252C46; }
.btn-inverse-dark.focus, .btn-inverse-dark:focus {
    -webkit-box-shadow: 0 0 0 3px rgba(37, 44, 70, 0.5);
    box-shadow: 0 0 0 3px rgba(37, 44, 70, 0.5); }
.btn-inverse-dark.disabled, .btn-inverse-dark:disabled {
    color: #252C46;
    background-color: transparent; }
.btn-inverse-dark.active, .btn-inverse-dark:active,
.show > .btn-inverse-dark.dropdown-toggle {
    color: #ffffff;
    background-color: #252C46;
    border-color: #252C46; }

/* Inverse Outlined Buttons */
.btn-inverse-outline-primary {
    color: #2196f3;
    background-image: none;
    background: transparent;
    border-color: rgba(33, 150, 243, 0.2); }
.btn-inverse-outline-primary:hover {
    color: #2196f3;
    background-color: rgba(33, 150, 243, 0.2);
    border-color: rgba(33, 150, 243, 0.2); }
.btn-inverse-outline-primary.focus, .btn-inverse-outline-primary:focus {
    -webkit-box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.5);
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.5); }
.btn-inverse-outline-primary.disabled, .btn-inverse-outline-primary:disabled {
    color: #2196f3;
    background-color: transparent; }
.btn-inverse-outline-primary.active, .btn-inverse-outline-primary:active,
.show > .btn-inverse-outline-primary.dropdown-toggle {
    color: #ffffff;
    border-color: #2196f3; }

.btn-inverse-outline-secondary {
    color: #c0c2c3;
    background-image: none;
    background: transparent;
    border-color: rgba(192, 194, 195, 0.2); }
.btn-inverse-outline-secondary:hover {
    color: #c0c2c3;
    background-color: rgba(192, 194, 195, 0.2);
    border-color: rgba(192, 194, 195, 0.2); }
.btn-inverse-outline-secondary.focus, .btn-inverse-outline-secondary:focus {
    -webkit-box-shadow: 0 0 0 3px rgba(192, 194, 195, 0.5);
    box-shadow: 0 0 0 3px rgba(192, 194, 195, 0.5); }
.btn-inverse-outline-secondary.disabled, .btn-inverse-outline-secondary:disabled {
    color: #c0c2c3;
    background-color: transparent; }
.btn-inverse-outline-secondary.active, .btn-inverse-outline-secondary:active,
.show > .btn-inverse-outline-secondary.dropdown-toggle {
    color: #ffffff;
    border-color: #c0c2c3; }

.btn-inverse-outline-success {
    color: #19d895;
    background-image: none;
    background: transparent;
    border-color: rgba(25, 216, 149, 0.2); }
.btn-inverse-outline-success:hover {
    color: #19d895;
    background-color: rgba(25, 216, 149, 0.2);
    border-color: rgba(25, 216, 149, 0.2); }
.btn-inverse-outline-success.focus, .btn-inverse-outline-success:focus {
    -webkit-box-shadow: 0 0 0 3px rgba(25, 216, 149, 0.5);
    box-shadow: 0 0 0 3px rgba(25, 216, 149, 0.5); }
.btn-inverse-outline-success.disabled, .btn-inverse-outline-success:disabled {
    color: #19d895;
    background-color: transparent; }
.btn-inverse-outline-success.active, .btn-inverse-outline-success:active,
.show > .btn-inverse-outline-success.dropdown-toggle {
    color: #ffffff;
    border-color: #19d895; }

.btn-inverse-outline-info {
    color: #8862e0;
    background-image: none;
    background: transparent;
    border-color: rgba(136, 98, 224, 0.2); }
.btn-inverse-outline-info:hover {
    color: #8862e0;
    background-color: rgba(136, 98, 224, 0.2);
    border-color: rgba(136, 98, 224, 0.2); }
.btn-inverse-outline-info.focus, .btn-inverse-outline-info:focus {
    -webkit-box-shadow: 0 0 0 3px rgba(136, 98, 224, 0.5);
    box-shadow: 0 0 0 3px rgba(136, 98, 224, 0.5); }
.btn-inverse-outline-info.disabled, .btn-inverse-outline-info:disabled {
    color: #8862e0;
    background-color: transparent; }
.btn-inverse-outline-info.active, .btn-inverse-outline-info:active,
.show > .btn-inverse-outline-info.dropdown-toggle {
    color: #ffffff;
    border-color: #8862e0; }

.btn-inverse-outline-warning {
    color: #ffaf00;
    background-image: none;
    background: transparent;
    border-color: rgba(255, 175, 0, 0.2); }
.btn-inverse-outline-warning:hover {
    color: #ffaf00;
    background-color: rgba(255, 175, 0, 0.2);
    border-color: rgba(255, 175, 0, 0.2); }
.btn-inverse-outline-warning.focus, .btn-inverse-outline-warning:focus {
    -webkit-box-shadow: 0 0 0 3px rgba(255, 175, 0, 0.5);
    box-shadow: 0 0 0 3px rgba(255, 175, 0, 0.5); }
.btn-inverse-outline-warning.disabled, .btn-inverse-outline-warning:disabled {
    color: #ffaf00;
    background-color: transparent; }
.btn-inverse-outline-warning.active, .btn-inverse-outline-warning:active,
.show > .btn-inverse-outline-warning.dropdown-toggle {
    color: #ffffff;
    border-color: #ffaf00; }

.btn-inverse-outline-danger {
    color: #ff6258;
    background-image: none;
    background: transparent;
    border-color: rgba(255, 98, 88, 0.2); }
.btn-inverse-outline-danger:hover {
    color: #ff6258;
    background-color: rgba(255, 98, 88, 0.2);
    border-color: rgba(255, 98, 88, 0.2); }
.btn-inverse-outline-danger.focus, .btn-inverse-outline-danger:focus {
    -webkit-box-shadow: 0 0 0 3px rgba(255, 98, 88, 0.5);
    box-shadow: 0 0 0 3px rgba(255, 98, 88, 0.5); }
.btn-inverse-outline-danger.disabled, .btn-inverse-outline-danger:disabled {
    color: #ff6258;
    background-color: transparent; }
.btn-inverse-outline-danger.active, .btn-inverse-outline-danger:active,
.show > .btn-inverse-outline-danger.dropdown-toggle {
    color: #ffffff;
    border-color: #ff6258; }

.btn-inverse-outline-light {
    color: #fbfbfb;
    background-image: none;
    background: transparent;
    border-color: rgba(251, 251, 251, 0.2); }
.btn-inverse-outline-light:hover {
    color: #fbfbfb;
    background-color: rgba(251, 251, 251, 0.2);
    border-color: rgba(251, 251, 251, 0.2); }
.btn-inverse-outline-light.focus, .btn-inverse-outline-light:focus {
    -webkit-box-shadow: 0 0 0 3px rgba(251, 251, 251, 0.5);
    box-shadow: 0 0 0 3px rgba(251, 251, 251, 0.5); }
.btn-inverse-outline-light.disabled, .btn-inverse-outline-light:disabled {
    color: #fbfbfb;
    background-color: transparent; }
.btn-inverse-outline-light.active, .btn-inverse-outline-light:active,
.show > .btn-inverse-outline-light.dropdown-toggle {
    color: #ffffff;
    border-color: #fbfbfb; }

.btn-inverse-outline-dark {
    color: #252C46;
    background-image: none;
    background: transparent;
    border-color: rgba(37, 44, 70, 0.2); }
.btn-inverse-outline-dark:hover {
    color: #252C46;
    background-color: rgba(37, 44, 70, 0.2);
    border-color: rgba(37, 44, 70, 0.2); }
.btn-inverse-outline-dark.focus, .btn-inverse-outline-dark:focus {
    -webkit-box-shadow: 0 0 0 3px rgba(37, 44, 70, 0.5);
    box-shadow: 0 0 0 3px rgba(37, 44, 70, 0.5); }
.btn-inverse-outline-dark.disabled, .btn-inverse-outline-dark:disabled {
    color: #252C46;
    background-color: transparent; }
.btn-inverse-outline-dark.active, .btn-inverse-outline-dark:active,
.show > .btn-inverse-outline-dark.dropdown-toggle {
    color: #ffffff;
    border-color: #252C46; }


.btn-inverse-danger {
    color: #ff6258!important;
}
.btn-inverse-success {
    color: #19d895!important;
}
.btn-inverse-warning {
    color: #ffaf00!important;
}
.btn-inverse-primary {
    color: #2196f3!important;
}
.btn-inverse-info {
    color: #8862e0!important;
}
.btn-inverse-facebook{
    color: #648ACA;
    background-color: rgba(100, 138, 202, 0.2);
    background-image: none;
    border-color: rgba(255, 175, 0, 0);
}
.btn-inverse-facebook:hover{
    color: white;
    background-color: #648ACA;
}

.btn-dark, .btn-inverse-danger:hover, .btn-inverse-success:hover, .btn-inverse-warning:hover, .btn-inverse-primary:hover, .btn-inverse-dark:hover, .btn-inverse-info:hover,
.btn-inverse-danger:active, .btn-inverse-success:active, .btn-inverse-warning:active, .btn-inverse-primary:active, .btn-inverse-dark:active .btn-inverse-info:active{
    color: white!important;
}
