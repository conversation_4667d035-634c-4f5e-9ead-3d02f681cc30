@page {
    size: A4;
    width: 90%;
    margin: 100px 5% 120px;
}
body {
    font-family: "Open Sans", sans-serif;
    font-size: 15px;
    line-height: 1.25;
}
ul{
    padding-left: 1rem;
}

header {
    position: fixed;
    top: -50px;
    transform: translateY(-50%);
    left: 0;
    z-index: -1;
    width: 100%;
}
footer{
    position: fixed;
    bottom: -60px;
    left: 0;
    transform: translateY(50%);
    z-index: -1;
    width: 100%;
}



.page-counter {
    position: fixed;
    bottom: -60px;
    right: 0;
    transform: translateY(50%);
    z-index: -1;
    width: 100%;
    text-align: right;
}
.page-counter .counter:after {
    content: counter(page, decimal);
}

table{
    border-collapse: collapse;
    width: 100%;
}
table td, table th{
    padding: .15rem .25rem;
}
.table-bordered th, .table-bordered td{
    border: 1px solid black;
}

.page-break{
    page-break-after: always;
}
.nobr{
    white-space: nowrap;
}

.content-m-0 *{
    margin: 0!important;
}
.content-p-0 *{
    padding: 0!important;
}
.content-border-0 *{
    border: none!important;
}


