
todo{
    display: block;
    color: rgba(255, 0, 0, .6);
    font-family: monospace;
}
todo::before{
    content: '//TODO';
    color: red;
    margin-right: .25rem;
    font-weight: 600;
}
a{
    cursor: pointer;
}
h4, h5{
    margin-top: 1rem;
    margin-bottom: 0;
}

.container-fluid{
    min-height: 100%;
}

.label{
    font-size: 0.8rem;
    margin: 0;
    color: #6c757d;
}
.floating-label{
    font-size: 0.8rem;
    margin: 0;
    color: #6c757d;
    transform: translateY(50%) translateX(.5rem);
    padding: 0 .15rem;
    background: white;
}
.label-value{
    border-bottom: 1px solid #dee2e6;
    min-height: 25px;
    font-size: 1rem;
}

.min-w-0{
    min-width: 0;
}
.min-w-25{
    min-width: 25px;
}
.min-w-50{
    min-width: 50px;
}
.min-w-75{
    min-width: 75px;
}
.min-w-100{
    min-width: 100px;
}
.min-w-125{
    min-width: 125px;
}
.min-w-150{
    min-width: 150px;
}
.min-w-200{
    min-width: 200px;
}
.min-w-250{
    min-width: 250px;
}
.min-w-300{
    min-width: 300px;
}

.w-0{
    width: 0;
}

.w-px-25{
    width: 25px;
}
.w-px-35{
    width: 35px;
}
.w-px-50{
    width: 50px;
}
.w-px-75{
    width: 75px;
}
.w-px-100{
    width: 100px;
}
.w-px-125{
    width: 125px;
}
.w-px-150{
    width: 150px;
}
.w-px-200{
    width: 200px;
}
.w-px-250{
    width: 250px;
}
.w-px-300{
    width: 300px;
}
.w-px-400{
    width: 400px;
}
.w-px-500{
    width: 500px;
}
.w-px-600{
    width: 600px;
}

.h-px-25{
    height: 25px;
}
.h-px-35{
    height: 35px;
}
.h-px-50{
    height: 50px;
}
.h-px-75{
    height: 75px;
}
.h-px-100{
    height: 100px;
}
.h-px-125{
    height: 125px;
}
.h-px-150{
    height: 150px;
}
.h-px-200{
    height: 200px;
}
.h-px-250{
    height: 250px;
}
.h-px-300{
    height: 300px;
}

.h-max{
    height: calc(100vh - 3.75rem);
}

.min-h-px-25{
    min-height: 25px;
}
.min-h-px-50{
    min-height: 50px;
}
.min-h-px-75{
    min-height: 75px;
}
.min-h-px-100{
    min-height: 100px;
}
.min-h-px-200{
    min-height: 200px;
}
.min-h-px-300{
    min-height: 300px;
}
.min-h-px-400{
    min-height: 400px;
}
.min-h-px-500{
    min-height: 500px;
}

.transition-0 {
    transition: 0s cubic-bezier(0.22, 0.61, 0.36, 1)!important;
}
.transition-01 {
    transition: .1s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.transition-02 {
    transition: .2s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.transition-025 {
    transition: .25s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.transition-03 {
    transition: .3s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.transition-04 {
    transition: .4s cubic-bezier(0.22, 0.61, 0.36, 1);
}
.transition-05 {
    transition: .5s cubic-bezier(0.22, 0.61, 0.36, 1);
}

.scale-125{
    transform: scale(1.25);
}
.scale-15{
    transform: scale(1.5);
}
.scale-175{
    transform: scale(1.75);
}
.scale-2{
    transform: scale(2);
}

.m-05{
    margin: .125rem !important;
}
.mt-05{
    margin-top : .125rem!important;
}
.mb-05{
    margin-bottom : .125rem!important;
}
.mr-05{
    margin-right : .125rem!important;
}
.ml-05{
    margin-left : .125rem!important;
}
.mx-05{
    margin-left: .125rem !important;
    margin-right: .125rem !important;
}
.my-05 {
    margin-top: .125rem !important;
}

.m--1{
    margin: -0.25rem !important;
}
.mt--1{
    margin-top : -.25rem!important;
}
.mb--1{
    margin-bottom : -.25rem!important;
}
.mr--1{
    margin-right : -.25rem!important;
}
.ml--1{
    margin-left : -.25rem!important;
}
.mx--1{
    margin-left: -0.25rem !important;
    margin-right: -0.25rem !important;
}
.my--1{
    margin-top: -0.25rem !important;
    margin-bottom: -0.25rem !important;
}


.m--2{
    margin: -0.5rem !important;
}
.mt--2{
    margin-top : -.5rem!important;
}
.mb--2{
    margin-bottom : -.5rem!important;
}
.mr--2{
    margin-right : -.5rem!important;
}
.ml--2{
    margin-left : -.5rem!important;
}
.mx--2{
    margin-left: -0.5rem !important;
    margin-right: -0.5rem !important;
}
.my--2{
    margin-top: -0.5rem !important;
    margin-bottom: -0.5rem !important;
}

.m--25{
    margin: -75rem !important;
}
.mt--25{
    margin-top : -.75rem!important;
}
.mb--25{
    margin-bottom : -.75rem!important;
}
.mr--25{
    margin-right : -.75rem!important;
}
.ml--25{
    margin-left : -.75rem!important;
}
.mx--25{
    margin-left: -75rem !important;
    margin-right: -75rem !important;
}
.my--25{
    margin-top: -75rem !important;
    margin-bottom: -75rem !important;
}

.m--3{
    margin: -1rem !important;
}
.mt--3{
    margin-top : -1rem!important;
}
.mb--3{
    margin-bottom : -1rem!important;
}
.mr--3{
    margin-right : -1rem!important;
}
.ml--3{
    margin-left : -1rem!important;
}
.mx--3{
    margin-left: -1rem !important;
    margin-right: -1rem !important;
}
.my--3{
    margin-top: -1rem !important;
    margin-bottom: -1rem !important;
}

.m--4{
    margin: -1.5rem !important;
}
.mt--4{
    margin-top : -1.5rem!important;
}
.mb--4{
    margin-bottom : -1.5rem!important;
}
.mr--4{
    margin-right : -1.5rem!important;
}
.ml--4{
    margin-left : -1.5rem!important;
}
.mx--4{
    margin-left: -1.5rem !important;
    margin-right: -1.5rem !important;
}
.my--4{
    margin-top: -1.5rem !important;
    margin-bottom: -1.5rem !important;
}

.form-switch-custom, .form-switch{
    cursor: pointer;
    width: 2em;
    min-width: 2em;
    background-image: url("https://regisseur.nl/dashboard/svg/switch.svg");
    background-position: left center;
    border-radius: 2em;
    transition: background-position .15s ease-in-out;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0;
    margin-top: 0.25rem;
    height: 1em;
    vertical-align: top;
    background-color: #fff;
    background-repeat: no-repeat;
    background-size: contain;
    border: 1px solid rgba(0,0,0,.25);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
}
.form-switch-custom-md, .form-switch-md{
    width: 3em;
    height: 1.5em;
}
.form-switch-custom:checked[type=checkbox], .form-switch:checked[type=checkbox] {
    background-color: #0d6efd;
    border-color: #0d6efd;
    background-position: right center;
    background-image: url("https://regisseur.nl/dashboard/svg/switch-checked.svg");
}
.form-switch-custom:active, .form-switch:active {
    filter: brightness(90%);
}
.form-switch-custom:focus, .form-switch:focus {
    background-image: url(data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%2386b7fe'/%3e%3c/svg%3e);
    border-color: #86b7fe;
    outline: 0;
}
.form-switch-custom:disabled, .form-switch:disabled {
    pointer-events: none;
    filter: none;
    opacity: .5;
}

.form-switch-label{
    cursor: pointer;
    margin: 0;
    padding: 0.375rem 0.75rem;
    border-radius: .4rem;
    width: 100%;
}
.form-switch-label:hover{
    background: rgba(192, 194, 195, 0.2)!important;
}

.form-control-divider{
    width: auto;
    border-left: none;
    border-right: none;
    border-radius: 0;
    padding: 0;

    display: flex;
    justify-content: center;
    align-items: center;
}
.form-control-divider:after{
    content: "";
    border-right: 1px solid #ced4da;
    left: calc(50% - 1px);
    width: 1px;
    height: 20px;
}

.hover-border-dark:hover{
    border-color: #252C46 !important;
}
.hover-border-success:hover{
    border-color: #19d895 !important;
}
.hover-border-danger:hover{
    border-color: #ff6258 !important;
}
.hover-border-primary:hover{
    border-color: #2196f3 !important;
}
.hover-border-warning:hover{
    border-color: #ffaf00 !important;
}
.hover-border-danger:hover {
    border-color: #ff6258!important;
}
.hover-border-success:hover {
    border-color: #19d895!important;;
}
.hover-border-primary:hover {
    border-color: #2196f3!important;;
}

.hover-bg-gray:hover{
    background-color: #DEDEDF;
}
.hover-bg-unset:hover {
    background-color: unset!important;
}
.hover-bg-danger:hover {
    background-color: #ff6258!important;
}
.hover-bg-success:hover {
    background-color: #19d895!important;
}
.hover-bg-inverse-primary:hover{
    background: rgba(33, 150, 243, 0.2)!important;
}
.hover-bg-inverse-success:hover{
    background: rgba(25, 216, 149, 0.2)!important;
}
.hover-bg-inverse-danger:hover{
    background: rgba(255, 98, 88, 0.2)!important;
}
.hover-bg-inverse-secondary:hover,
.hover-mark:hover{
    background: rgba(192, 194, 195, 0.2)!important;
}
.hover-mark:active{
    background: rgba(192, 194, 195, 0.4)!important;
}

.hover-translatey-up{
    transition: .2s;
}
.hover-translatey-up:hover{
    transform: translateY(-.25rem);
}

.max-w{
    max-width: 100%;
}

.max-w-100{
    max-width: 100px;
}
.max-w-125{
    max-width: 125px;
}
.max-w-150{
    max-width: 150px;
}
.max-w-175{
    max-width: 175px;
}
.max-w-200{
    max-width: 200px;
}
.max-w-250{
    max-width: 250px;
}
.max-w-300{
    max-width: 300px;
}
.max-w-400{
    max-width: 400px;
}
.max-w-500{
    max-width: 500px;
}

.max-h-100{
    max-height: 100px;
}
.max-h-150{
    max-height: 150px;
}
.max-h-200{
    max-height: 200px;
}
.max-h-300{
    max-height: 300px;
}
.max-h-400{
    max-height: 400px;
}
.max-h-500{
    max-height: 500px;
}
.max-h-750{
    max-height: 750px;
}
.max-h-1000{
    max-height: 1000px;
}

.z-index-9{
    z-index: 9;
}
.z-index-99{
    z-index: 99;
}
.z-index-999{
    z-index: 999;
}
.z-index-9999{
    z-index: 9999;
}

.flex-between{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.flex-align{
    display: flex;
    align-items: center;
}
.flex-center{
    display: flex;
    align-items: center;
    justify-content: center;
}
.flex-start{
    display: flex;
    align-items: center;
    justify-content: start;
}
.flex-end{
    display: flex;
    align-items: center;
    justify-content: end;
}

.pointer-event-none{
    pointer-events: none;
}
.cursor-pointer{
    cursor: pointer;
}
.cursor-unset{
    cursor: unset;
}

.corner-notifications{
    position: fixed;
    right: 1rem;
    top: 1rem;
    z-index: 9999;
}


#sidebar.active-mobile{
display: block!important;
position: absolute;
top: 0;
left: 0;
z-index: 999;
width: 100%;
max-height: 85vh;
box-shadow: 0 0 50rem 50rem rgba(0, 0, 0, 0.65);
border-bottom-right-radius: 1.5rem;
border-bottom-left-radius: 1.5rem;
height: auto;
overflow: auto;
}
#sidebar.active-mobile .sidemenu{
height: auto!important;
}
#sidebar.active-mobile .mobile-navbar-close{
display: block!important;
position: absolute;
top: 0;
width: 100%;
}
#sidebar.active-mobile .mobile-navbar-close .btn{
color: black!important;
font-size: 1.25rem;
}

.opacity-1 {
    opacity: 1 !important;
}
.opacity-85 {
    opacity: 0.85 !important;
}
.opacity-75 {
    opacity: 0.75 !important;
}
.opacity-66 {
    opacity: 0.66 !important;
}
.opacity-50 {
    opacity: 0.5 !important;
}
.opacity-33 {
    opacity: 0.33 !important;
}
.opacity-25 {
    opacity: 0.25 !important;
}
.opacity-0 {
    opacity: 0 !important;
}

.hover-opacity-1:hover{
    opacity: 1!important;
}

.bg-gray{
    background-color: #DEDEDF;
}
.bg-regisseur{
    background-color: #01689b;
}

.bg-inverse-success {
background: rgba(25, 216, 149, 0.2);
}
.bg-inverse-danger {
background: rgba(255, 98, 88, 0.2);
}
.bg-inverse-primary{
background-color: #13AFF933!important;
}
.bg-inverse-warning {
background: rgba(255, 175, 0, 0.2);
}
.bg-inverse-secondary{
background: #C0C2C333!important;
}

.dots-loader {
    display: inline-block;
    position: relative;
    width: 80px;
    height: 80px;
}
.dots-loader div {
position: absolute;
width: 13px;
height: 13px;
border-radius: 50%;
background: #01689b;
animation-timing-function: cubic-bezier(0, 1, 1, 0);
top: 50%;
transform: translateY(-50%);
}
.dots-loader div:nth-child(1) {
left: 8px;
animation: lds-ellipsis1 0.6s infinite;
}
.dots-loader div:nth-child(2) {
left: 8px;
animation: lds-ellipsis2 0.6s infinite;
}
.dots-loader div:nth-child(3) {
left: 32px;
animation: lds-ellipsis2 0.6s infinite;
}
.dots-loader div:nth-child(4) {
left: 56px;
animation: lds-ellipsis3 0.6s infinite;
}
@keyframes lds-ellipsis1 {
0% {
transform: scale(0);
}
100% {
transform: scale(1);
}
}
@keyframes lds-ellipsis3 {
0% {
transform: scale(1);
}
100% {
transform: scale(0);
}
}
@keyframes lds-ellipsis2 {
0% {
transform: translate(0, 0);
}
100% {
transform: translate(24px, 0);
}
}

.hover-shadow, .hover-shadow-inset{
transition: 0.2s;
}
.hover-shadow:hover{
box-shadow: 0px 3px 6px 0px rgb(0 0 0 / 10%), 0px 1px 3px 0px rgb(0 0 0 / 8%);
}
.hover-shadow-inset:hover{
box-shadow: inset 0px 3px 6px 0px rgb(0 0 0 / 10%), 0px 1px 3px 0px rgb(0 0 0 / 8%);
}
.hover-shadow-active{
box-shadow: 0px 3px 6px 0px rgb(0 0 0 / 10%), 0px 1px 3px 0px rgb(0 0 0 / 8%);
}
.hover-shadow-inset-active{
box-shadow: inset 0px 3px 6px 0px rgb(0 0 0 / 10%), 0px 1px 3px 0px rgb(0 0 0 / 8%);
}

.hover-text-shadow:hover{
    text-shadow: 0 0 0.05rem rgba(0, 0, 0, .45);
}

.text-regisseur{
    color: #01689b
}
.text-primary-light{
    color: #56baed!important;
}
a.text-primary-light:hover{
    color: #4e9cdb!important;
}


.btn-primary-light{
    color: white;
    background-color: #56baed;
    border-color: #56baed;
}
.btn-primary-light:hover{
    background-color: #4e9cdb;
    border-color: #4e9cdb;
}
.btn-regisseur{
    color: #FFF;
    background-color: #01689b;
    border-color: #01689b;
}
.btn-regisseur:hover{
    color: #FFF;
    background-color: #015b87;
    border-color: #015b87;
}

.btn-td-danger{
    border-radius: .4rem !important;
    --bs-text-opacity: 1;
    color: #dc3545!important;
}
.btn-td-danger:hover{
    background: #FF625833 !important;
}
.btn-td-success{
    border-radius: .4rem !important;
    --bs-text-opacity: 1;
    color: #0bc066!important;
}
.btn-td-success:hover{
    background: #0bc06633 !important;
}
.btn-td-primary{
    border-radius: .4rem !important;
    --bs-text-opacity: 1;
    color: #0D6EFDFF!important;
}
.btn-td-primary:hover{
    background: #13AFF933 !important;
}
.btn-td-warning{
    border-radius: .4rem !important;
    --bs-text-opacity: 1;
    color: #ffc107!important;
}
.btn-td-warning:hover{
    background: #ffc10733 !important;
}

.shadow-light {
box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 5%) !important;
}
.shadow-inset {
    box-shadow: inset -0.4rem -0.4rem 1rem rgb(0 0 0 / 15%) !important;
}
.shadow-inset-even {
    box-shadow: inset 0 0 1rem rgb(0 0 0 / 15%) !important;
}
.shadow-inset-even-sm{
    box-shadow: inset 0 0 .5rem rgb(0 0 0 / 15%) !important;
}

.shadow-regular{
box-shadow: 0 0rem 1rem rgb(0 0 0 / 20%) !important;
}
.shadow-hover:hover{
box-shadow: 0 0.5rem 1rem rgb(0 0 0 / 15%) !important;
}
.shadow-inset-hover:hover{
box-shadow: inset -0.4rem -0.4rem 1rem rgb(0 0 0 / 15%) !important;
}

.rounded-right-0{
border-top-right-radius: 0!important;
border-bottom-right-radius: 0!important;
}
.rounded-left-0{
border-top-left-radius: 0!important;
border-bottom-left-radius: 0!important;
}
.rounded-top-0{
border-top-left-radius: 0!important;
border-top-right-radius: 0!important;
}
.rounded-bottom-0{
border-bottom-left-radius: 0!important;
border-bottom-right-radius: 0!important;
}

.rounded-4{
border-radius: .4rem!important;
}
.rounded-5{
border-radius: .5rem!important;
}
.rounded-6{
border-radius: .6rem!important;
}
.rounded-7{
border-radius: .7rem!important;
}
.rounded-8{
border-radius: .8rem!important;
}
.rounded-9{
border-radius: .9rem!important;
}
.rounded-10{
border-radius: 1rem!important;
}

.font-size-unset{
font-size: unset!important;
}
.font-size-025{
font-size: .25rem!important;
}
.font-size-05{
font-size: .5rem!important;
}
.font-size-055{
font-size: .55rem!important;
}
.font-size-06{
font-size: .6rem!important;
}
.font-size-065{
font-size: .65rem!important;
}
.font-size-07{
font-size: .7rem!important;
}
.font-size-075{
font-size: .75rem!important;
}
.font-size-08{
font-size: .8rem!important;
}
.font-size-085{
font-size: .85rem!important;
}
.font-size-09{
font-size: .9rem!important;
}
.font-size-095{
font-size: .95rem!important;
}
.font-size-1{
font-size: 1rem!important;
}
.font-size-105{
font-size: 1.05rem!important;
}
.font-size-11{
font-size: 1.1rem!important;
}
.font-size-12{
font-size: 1.2rem!important;
}
.font-size-125{
font-size: 1.25rem!important;
}
.font-size-15{
font-size: 1.5rem!important;
}
.font-size-155{
font-size: 1.55rem!important;
}
.font-size-175{
font-size: 1.75rem!important;
}
.font-size-2{
font-size: 2rem!important;
}
.font-size-25{
font-size: 2.5rem!important;
}
.font-size-3{
font-size: 3rem!important;
}
.font-size-35{
font-size: 3.5rem!important;
}
.font-size-4{
font-size: 4rem!important;
}
.font-size-45{
font-size: 4.5rem!important;
}
.font-size-5{
font-size: 5rem!important;
}

.line-height-075{
    line-height: .75rem;
}
.line-height-08{
    line-height: .8rem;
}
.line-height-085{
    line-height: .85rem;
}
.line-height-09{
    line-height: .9rem;
}
.line-height-095{
    line-height: .95rem;
}
.line-height-1{
    line-height: 1rem;
}
.line-height-11{
    line-height: 1.1rem;
}
.line-height-115{
    line-height: 1.15rem;
}
.line-height-12{
    line-height: 1.2rem;
}
.line-height-125{
    line-height: 1.25rem;
}


.dot-glow {
    display: inline-block;
    width: 10px;
    height: 10px;
    min-width: 10px;
    border-radius: 50rem;
    transition: .3s;
}
.dot-glow-sm {
    width: 8px;
    height: 8px;
    min-width: 8px;
}

.dot-glow-success {
    box-shadow: 0 0 0 0.25rem #19d89533;
    background-color: #19d895;
}
.dot-glow-danger {
    box-shadow: 0 0 0 .25rem #ff625833;
    background-color: #ff6258;
}
.dot-glow-secondary {
    box-shadow: 0 0 0 .25rem #c0c2c333;
    background-color: #c0c2c3;
}
.dot-glow-primary {
    box-shadow: 0 0 0 .25rem #2196f333;
    background-color: #2196f3;
}
.dot-glow-warning {
    box-shadow: 0 0 0 .25rem #ffaf0033;
    background-color: #ffaf00;
}

.dot-glow-sm.dot-glow-success {
    box-shadow: 0 0 0 .2rem #19d89533;
    background-color: #19d895;
}
.dot-glow-sm.dot-glow-danger {
    box-shadow: 0 0 0 .2rem #ff625833;
    background-color: #ff6258;
}
.dot-glow-sm.dot-glow-secondary {
    box-shadow: 0 0 0 .2rem #c0c2c333;
    background-color: #c0c2c3;
}
.dot-glow-sm.dot-glow-primary {
    box-shadow: 0 0 0 .2rem #2196f333;
    background-color: #2196f3;
}
.dot-glow-sm.dot-glow-warning {
    box-shadow: 0 0 0 .2rem #ffaf0033;
    background-color: #ffaf00;
}


.corner-loader{
    position: fixed;
    right: 1.5rem;
    bottom: 1.5rem;
    z-index: 9999;
    font-size: 1.25rem;
}
.corner-loader-content{
    display: flex;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 2555, .2);
    border-radius: 0.25rem !important;
    backdrop-filter: blur(.25rem);
}
.corner-loader i{
    font-size: 1.5rem;
}
.loader {
    display: inline-block;
    position: relative;
    width: 40px;
    height: 40px;
}
.loader:before,
.loader:after{
    content: '';
    border-radius: 50%;
    position: absolute;
    inset: 0;
    box-shadow: 0 0 10px 2px rgba(0, 0, 0, 0.3) inset;
}
.loader:after {
    box-shadow: 0 2px 0 #2196f3 inset;
    animation: rotate 1s linear infinite;
}

:root {
    --step-node-size: 40px;
    --step-divider-size: 3px;
    --step-color: #01689b;

    --sub-step-node-size: 30px;

}

.steps-node{
    width: var(--step-node-size);
    height: var(--step-node-size);
    min-width: var(--step-node-size);
    min-height: var(--step-node-size);
    border-radius: 100%;
    border: 2px solid;
    font-size: 1.4rem;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}
[data-steps-container="CURRENT"] .steps-node,
[data-steps-container="CURRENT_SUB"] .steps-node{
    border-color: var(--step-color);
    color: var(--step-color);
}
[data-steps-container="OPEN"] .steps-node{
    border-color: #d0d0d0;
    color: white;
}
[data-steps-container="SKIPPED"] .steps-node{
    border-color: #d0d0d0;
    color: #d0d0d0;
}
[data-steps-container="COMPLETED"] .steps-node{
    border-color: var(--step-color);
    background-color: var(--step-color);
    color: white;
    line-height: 1rem;
}
[data-steps-container="COMPLETED"] .steps-month,
[data-steps-container="COMPLETED"] .steps-day{
    font-size: .9rem!important;
}

.steps-label{
    font-size: 1rem;
    margin-left: .25rem;
    font-weight: 600;
    white-space: nowrap;
    line-height: 1.2rem;
}
.steps-description{
    color: #6c757d;
    margin-left: .25rem;
    transform: translateY(-.5rem);
    line-height: 1rem;
}
.steps-divider, .sub-steps-parent-divider{
    min-height: 20px;
    width: var(--step-divider-size);
    margin: 0 calc( (var(--step-node-size) - var(--step-divider-size)) / 2 );
    background-color: var(--step-color);
}
.sub-steps-label{
    font-size: .75rem;
    margin-left: .2rem;
    line-height: 1rem;
}
.sub-steps-description{
    color: #6c757d;
    margin-left: calc( var(--sub-step-node-size) + .2rem );
    margin-top: .25rem;
    line-height: 1rem;
}


[data-steps-container="OPEN"] .steps-divider,
[data-steps-container="SKIPPED"] .steps-divider,
[data-steps-container="CURRENT"] .steps-divider{
    background-color: #d0d0d0;
}

[data-steps-container="OPEN"] .sub-steps-parent-divider,
[data-steps-container="SKIPPED"] .sub-steps-parent-divider{
    background-color: #d0d0d0;
}

.steps-indicator, .sub-steps-indicator{
    height: 3px;
    border-radius: 50rem;
    width: 0%;
    background-color: var(--step-color);
    transition: .2s;
}
[data-steps-container]:hover .steps-indicator,
[data-sub-steps-container]:hover .sub-steps-indicator,
.steps-indicator[data-active],
.sub-steps-indicator[data-active]{
    width: 100%;
}
[data-sub-steps-container]{
    margin-left: calc( (var(--step-node-size) + (var(--step-divider-size)) / 2) / -2 );;
}

.sub-steps-node{
    width: var(--sub-step-node-size);
    height: var(--sub-step-node-size);
    min-width: var(--sub-step-node-size);
    min-height: var(--sub-step-node-size);
    font-size: 1rem;
}
[data-sub-steps-container="COMPLETED"] .sub-steps-node{
    border-color: var(--step-color);
    background-color: var(--step-color);
    color: white;
    line-height: 0.75rem;
    font-size: 0.75rem;
}

.sub-step-divider{
    width: 20px;
    height: calc( var(--sub-step-node-size) / 2 + 1px);
    border-left: 2px solid var(--step-color);
    border-bottom: 2px solid var(--step-color);
    border-bottom-left-radius: .75rem;
}
[data-steps-container="OPEN"] .sub-step-divider,
[data-steps-container="SKIPPED"] .sub-step-divider{
    border-color: #d0d0d0;
}
[data-sub-steps-container="COMPLETED"] .steps-month,
[data-sub-steps-container="COMPLETED"] .steps-day{
    font-size: .75rem!important;
}

.nobr{
    white-space: nowrap;
}
.divider{
    width: 95%;
    margin: .5rem 2.5%;
    border-bottom: 1px solid #dee2e6;
}
.full-divider{
    width: 100%;
    margin: .5rem 0;
    border-bottom: 1px solid #dee2e6;
}
.codeblock{
    padding: .5rem;
    border-radius: .5rem;
    background: #C0C2C333!important;
    border: 1px solid #dee2e6 !important;
}

.vertical-middle{
    vertical-align: middle!important;
}

/*Logs*/
.bg-logs-debug{
    background-color: #7f8c8d;
}
.bg-logs-info{
    background-color: #3498db;
}
.bg-logs-notice{
    background-color: #2ecc71;
}
.bg-logs-warning{
    background-color: #f39c12;
}
.bg-logs-alert{
    background-color: #e74c3c;
}

/*Charts*/
.chart-container{
    height: 250px;
    position: relative;
}
.chart-container[data-chart-type=doughnut]{
    height: 350px;
}
.chart-loader{
    position: absolute;
    left: 50%;
    top: 50%;

    transform: translateY(-50%) translateX(-50%);
}

/*Tippy*/
.tippy-box{
    max-width: unset!important;
}

/*Livewire*/
.wire-loading{
    pointer-events: none;
    opacity: 0.66;
}

@keyframes rotate {
    0% {  transform: rotate(0)}
    100% { transform: rotate(360deg)}
}

