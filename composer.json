{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.29", "ext-curl": "*", "ext-json": "*", "barryvdh/laravel-dompdf": "^2.2", "box/spout": "^3.3", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "^7.0.1", "jenssegers/agent": "^2.6", "laravel/framework": "^8.75", "laravel/sanctum": "^2.11", "laravel/socialite": "^5.16", "laravel/tinker": "^2.5", "laravel/ui": "^3.4", "livewire/livewire": "^2.12", "maatwebsite/excel": "^3.1", "phpoffice/phpspreadsheet": "^1.29", "socialiteproviders/microsoft": "^4.6"}, "require-dev": {"facade/ignition": "^2.5", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^5.10", "phpunit/phpunit": "^9.5.10"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Http/Helpers.php", "app/Http/Enums.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}