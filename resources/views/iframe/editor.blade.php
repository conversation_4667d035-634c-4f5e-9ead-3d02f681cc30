<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <link href="{{ asset('dashboard/css/vendor/bootstrap.css') }}{{cacheClear()}}" rel="stylesheet">
    <link href="{{ asset('dashboard/css/utilities.css') }}{{cacheClear()}}" rel="stylesheet">

    <script src="{{ asset('dashboard/js/vendor/jquery.js') }}"></script>
</head>
<body >

<div data-toolbar class="position-fixed top-0 w-100" ></div>
<div data-editor class="h-100" style="min-height: calc(100vh - 39.66px); margin-top: 39.66px" ></div>

<script src="{{ asset('dashboard/js/vendor/bootstrap.js') }}"></script>
<script src="{{ asset('dashboard/js/vendor/decouplededitor.js') }}"></script>
<script src="{{ asset('dashboard/js/editor.js') }}"></script>
<script src="{{ asset('dashboard/js/utilities.js') }}"></script>

<script>

    var editor
    const keywords = {
        options: @json(EmailsKeywords::LEGENDA),
        active: false,
        search: '',
        selection_index: null,
        html_options: () => { return $('[data-keyword]') }
    }
    $(document).ready(initEditor)

    //Editor
    async function initEditor(){
        editor = await DecoupledEditor.create( $('[data-editor]').get(0))

        editor.plugins.get( 'FileRepository' ).createUploadAdapter = ( loader ) => { return new ckEditorUploadAdapter( loader ); };
        editor.editing.view.document.on('keydown', keydown);

        $('[data-toolbar]').html(editor.ui.view.toolbar.element);
    }
    function keydown(event, data){
        const { html_options } = keywords;
        const { keyCode, domEvent } = data;
        const { key } = domEvent;

        if(keywords.active){


            if(key == 'Enter'){



                domEvent.preventDefault();
                keywordOptionSelect(html_options().eq(keywords.selection_index).attr('data-keyword'));
            }
            else if(key == 'ArrowUp' || key == 'ArrowDown'){
                domEvent.preventDefault();
                keywordPreviewSelection(key);
            }
            else if(keyType(keyCode) == 'letter'){
                keywordsSearch(key);
                keywordPreviewSelection();
            }
            return;
        }

        if(key == '#'){
            keywordSelect();
            keywordPreviewSelection();
        }
        else if(keyType(keyCode) != 'letter'){
            keywordsDeselect();
        }
    }

    //Keywords
    async function keywordSelect(retry = false){
        const domSelection = window.getSelection();
        const domRange = domSelection.getRangeAt(0);
        const rects = domRange.getClientRects();

        if(!rects.length && !retry){
            await delay(50);
            return keywordSelect(true);
        }

        if(!rects.length){ return }

        const { x, y } = rects[0];
        const scroll = document.documentElement.scrollTop;

        $('body').append(`
            <div class="position-absolute border shadow bg-white rounded p-1 font-size-085" data-keywords-container style="top: ${y + scroll + 20}px; left: ${x}px" >
                ${ keywords.options.map(option => `<div class="rounded-pill hover-mark cursor-pointer px-2" data-keyword="${option.key}" onclick="keywordOptionSelect('${option.key}')" >${option.name}</div>`).join('') }
            </div>
        `)

        const element = $('[data-keywords-container]');
        const editor_height = $('.ck-content').innerHeight();
        const editor_width = $('.ck-content').innerWidth();

        if( element.position().top + element.outerHeight() > editor_height ){
            element.css({top: `${y + scroll - element.outerHeight()}px`})
        }
        if( element.position().left + element.outerWidth() > editor_width ){
            element.css({left: `${editor_width - 20 - element.outerWidth()}px`})
        }

        keywords.active = true;

    }
    function keywordsDeselect(){
        $('[data-keywords-container]').remove();

        keywords.search = '';
        keywords.active = false;
    }
    function keywordsSearch(key){
        keywords.search += key;

        $('[data-keyword]').each(function () {
            const key = $(this).attr('data-keyword');
            const name = $(this).text().toLowerCase();

            if(!key.includes(keywords.search) && !name.includes(keywords.search)){
                $(this).remove();
            }
        });

        const active_keywords = $('[data-keyword]').length;
        if(!active_keywords){
            keywordsDeselect();
        }
    }
    function keywordOptionSelect(keyword){
        const { search } = keywords;
        const new_position = getPosition(null, keyword.length - search.length + 1);

        replaceTextInRange(
            getPosition(null, -search.length),
            getPosition(),
            `${keyword}#`
        );

        editor.focus();
        editor.model.change(writer => {
            writer.setSelection(new_position);
        });

        keywordsDeselect();
    }
    function keywordPreviewSelection(key = null){
        const { html_options } = keywords;

        if(key == 'ArrowUp'){
            keywords.selection_index -= 1;
        }
        else if(key == 'ArrowDown'){
            keywords.selection_index += 1;
        }
        else{
            keywords.selection_index = 0;
        }

        if(keywords.selection_index < 0){
            keywords.selection_index = html_options().length - 1;
        }
        else if(keywords.selection_index == html_options().length){
            keywords.selection_index = 0
        }

        const preview_classes = ['bg-inverse-primary', 'text-primary'];

        $('[data-keyword]').removeClass(preview_classes).eq(keywords.selection_index).addClass(preview_classes);
    }

    //Utility
    function getPosition(path = null, offset_x = 0, offset_y = 0){
        const position =  editor.model.document.selection.getFirstRange().start;
        if(path){
            position.path = path;
        }
        if(offset_x){
            position.path[1] += offset_x;
        }
        if(offset_y){
            position.path[0] += offset_y;
        }

        return position;
    }
    function replaceTextInRange(start, end, text) {
        editor.model.change(writer => {
            writer.remove(writer.createRange(start, end));
            writer.insertText( text, start );
        });
    }

</script>

</body>
</html>
