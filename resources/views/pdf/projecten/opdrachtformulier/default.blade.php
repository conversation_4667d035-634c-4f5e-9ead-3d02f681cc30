@extends('pdf.layout')

@section('title', "Opdrachtformulier {$project->projectnummer}")
@section('style')
  <style>

    .opdrachtformulier-header{
        font-weight: bold;
        font-size: 1.35rem;
    }

    .gegevens-table td:first-child{
        width: 0!important;
        white-space: nowrap;
    }

    .bronnen-table{
        table-layout: fixed;
        vertical-align: center;
        overflow-wrap: anywhere;
    }
    .bronnen-table tr:first-child{
        font-size: .65rem;
    }
    .bronnen-table .x-mark{
        text-align: center;
        font-size: 1.25rem;
    }

  </style>
@endsection


@section('header')

    <table>
      <tbody>
        <tr>
          <td class="opdrachtformulier-header " >Opdrachtformulier</td>
          <td class="text-right" >
            @if(_domain()->logo_guid)
              <img src="{{_file(_domain()->logo_guid)}}" height="75" class="domain-logo" >
            @endif
          </td>
        </tr>
      </tbody>
    </table>

@endsection

@section('content')

  <section>

    <div>Omvang onderzoek: <u>Basis betreft altijd de volledige binnenschil van de woning met opstallen visueel, met gericht destructief onderzoek zoals in onderstaande tabel aangegeven.</u></div>

    <div class="my-4" >
      <b>Gegevens</b>
      <table class="gegevens-table table-bordered my-2" >
        <tr>
          <td>Adres</td>
          <td>{{$adres->addressLine()}}</td>
        </tr>
        <tr>
          <td>Complexnummer</td>
          <td>{{$adres->complexnummer}}</td>
        </tr>
        <tr>
          <td>Postcode en plaats</td>
          <td>{{$adres->postcode}}, {{$adres->plaats}}</td>
        </tr>
        <tr>
          <td>Woning in gebruik?</td>
          <td>{{ucfirst($formulier->woning_in_gebruik)}}</td>
        </tr>
        <tr>
          <td>Sleutels</td>
          <td>{{ucfirst($formulier->sleutel)}}</td>
        </tr>
        <tr>
          <td>Opleverdatum + tijd</td>
          <td>{{displayDate($formulier->opleverdatum)}} {{Carbon($formulier->oplevertijd)->format('H:i')}}</td>
        </tr>
        <tr>
          <td>Reden van Inventarisatie</td>
          <td>{{ucfirst($formulier->reden_van_inventarisatie)}}</td>
        </tr>
        <tr>
          <td>Kruipruimte aanwezig</td>
          <td>{{ucfirst($formulier->kruipruimte_aanwezig)}}</td>
        </tr>
        <tr>
          <td>Mangatwacht aanwezig</td>
          <td>{{ucfirst($formulier->mangatwacht_aanwezig)}}</td>
        </tr>
      </table>
    </div>

    <div class="my-4">
      <b>Destructief onderzoeken</b>
      <table class="bronnen-table table-bordered my-2">
        <tbody>

          <tr>
            <td colspan="2" ></td>
            @foreach($bronnen as $bron)
              <td>{{$bron->omschrijving}}</td>
            @endforeach
          </tr>

          @foreach($formulier->locaties_koppeling as $locatie_koppeling)
            <tr>
              <td colspan="2" >{{$locatie_koppeling->bron_locatie->locatie}}</td>
              @foreach($bronnen as $bron)
                <td> <div class="x-mark" >{{$locatie_koppeling->hasBron($bron->id) ? 'X' : ''}}</div> </td>
              @endforeach
            </tr>
          @endforeach
        </tbody>
      </table>
    </div>

    <div class="my-4">
      <b>Vermoedens asbest / specifieke aandachtspunten</b>
      <div class="my-1" >Vermoedens op basis van eigen visuele opnamen en/of vermoeden op basis van vergelijkbare wonging of zaken die specifiek van toepassing zijn voor dit bouwwerk:</div>

      <ul>
        @foreach($formulier->locaties_koppeling as $locatie_koppeling)
          @if(!$locatie_koppeling->opmerking) @continue @endif

          <li class="my-3" >
            <b>{{$locatie_koppeling->bron_locatie->locatie}}</b>
            <div>{{$locatie_koppeling->opmerking}}</div>
          </li>

        @endforeach
      </ul>

    </div>

  </section>


@endsection
