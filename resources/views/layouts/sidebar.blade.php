@auth
    <nav class="sidebar" @if(_cookie('sidebar_hidden') == 'true' || isMobile()) data-hidden @endif >

        <div class="nav-logo">
            <img data-logo="main" src="{{ url('dashboard/img/Logo.png') }}">
            <img data-logo="compact" src="{{ url('dashboard/img/LogoCompact.png') }}">
        </div>
        <div class="nav-user">
            <div class="nav-user-name">
                <div>{{Auth::user()->name}} {{Auth::user()->lastname}}</div>
                <div>{{Auth::user()->email}}</div>
            </div>
        </div>

        {{--Domeinen--}}
        <div class="nav-item-container">
            <a class="nav-item">
                <span class="nav-item-icon"><i class="fa-solid fa-people-roof"></i></span>
                <span class="nav-item-label" >{{_domain()->domein}}</span>
                <span class="nav-direction-icon">@icon_right</span>
            </a>
            <div class="nav-item-dropdown">
                @foreach(_user()->domeinen as $domein)
                    <a class="nav-item" href="{{url("/dashboard/domeinen/switch/{$domein->id}")}}" >{{$domein->domein}}</a>
                @endforeach
            </div>
        </div>

        {{--Projecten--}}
        @if(hasPermission('projecten_create') || hasPermission('projecten_index'))
            <div class="nav-item-container" >
                <a class="nav-item">
                    <span class="nav-item-icon"><i class="fa-regular fa-folder-open"></i></span>
                    <span class="nav-item-label" >Projecten</span>
                    <span class="nav-direction-icon">@icon_right</span>
                </a>
                <div class="nav-item-dropdown">
                    @if(hasPermission('projecten_create'))
                        @foreach(getActiveProcessen() as $proces)
                            @if($proces->getSetting('sidebar_verbergen')) @continue @endif
                            @if(!$proces->isFristStapInvulbaar()) @continue @endif

                            <a class="nav-item" href="{{url("/dashboard/projecten/new/$proces->id")}}">Nieuw {{$proces->naam}}</a>
                            @if($proces->getSetting('is_planmatig'))
                                <a class="nav-item" href="{{url("/dashboard/projecten/new/$proces->id?planmatig=true")}}">Nieuw Planmatig {{$proces->naam}}</a>
                            @endif
                        @endforeach
                    @endif

                    @if(hasPermission('projecten_create') && hasPermission('projecten_index'))
                        <div class="nav-item-divider"></div>
                    @endif

                    @if(hasPermission('projecten_index'))
                        <a class="nav-item" href="{{url('/dashboard/projecten?status=ACTIELIJST')}}">Actielijst</a>
                        <a class="nav-item" href="{{url('/dashboard/projecten?status=LOPEND')}}">Lopende Projecten</a>
                        <a class="nav-item" href="{{url('/dashboard/projecten?status=GEPARKEERD')}}">Geparkeerde Projecten</a>
                        <a class="nav-item" href="{{url('/dashboard/projecten?status=GEANNULEERD')}}">Geannuleerde Projecten</a>
                        <a class="nav-item" href="{{url('/dashboard/projecten?status=AFGEROND')}}">Afgeronde Projecten</a>
                    @endif
                </div>
            </div>
        @endif

        {{--Adressen--}}
        @if(hasPermission('adressen_index'))
            <div class="nav-item-container" >
                <a class="nav-item">
                    <span class="nav-item-icon"><i class="fa-solid fa-house-chimney"></i></span>
                    <span class="nav-item-label" >{{label('Adressen')}}</span>
                    <span class="nav-direction-icon">@icon_right</span>
                </a>
                <div class="nav-item-dropdown">
                    <a class="nav-item" href="{{url('/dashboard/adressen?active=1')}}">Actieve {{label('Adressen')}}</a>
                    <a class="nav-item" href="{{url('/dashboard/adressen?active=0')}}">Verwijderde {{label('Adressen')}}</a>
                </div>
            </div>
        @endif

        {{--Complexen--}}
        @if(hasPermission('complexen_index'))
            <div class="nav-item-container" >
                <a class="nav-item">
                    <span class="nav-item-icon"><i class="fa-solid fa-building"></i></span>
                    <span class="nav-item-label" >{{label('Complexen')}}</span>
                    <span class="nav-direction-icon">@icon_right</span>
                </a>

                <div class="nav-item-dropdown">
                    <a class="nav-item" href="{{url('/dashboard/complexen')}}" >Alle {{label('Complexen')}}</a>
                </div>
            </div>
        @endif


        {{--Nieuws--}}
        <div class="nav-item-container" >
            <a class="nav-item" href="{{url('/dashboard/nieuws')}}" >
                <span class="nav-item-icon"><i class="fa-solid fa-newspaper"></i></span>
                <span class="nav-item-label" >Nieuws</span>
            </a>
        </div>

        <div class="nav-item-divider"></div>

        @foreach(getActiveProcessenTypes() as $type)
            @include("layouts.sidebar_components.{$type}")
        @endforeach

        <div class="nav-item-divider"></div>

        {{--Users--}}
        @if(hasPermission('gebruikers_beheren'))
            <div class="nav-item-container" >
                <a class="nav-item">
                    <span class="nav-item-icon"><i class="fa-solid fa-users"></i></span>
                    <span class="nav-item-label" >Gebruikers</span>
                    <span class="nav-direction-icon">@icon_right</span>
                </a>
                <div class="nav-item-dropdown">
                    <a class="nav-item" href="{{url('/dashboard/gebruikers/create')}}">Gebruiker toevoegen</a>
                    <a class="nav-item" href="{{url('/dashboard/gebruikers')}}">Alle gebruikers</a>
                </div>
            </div>
        @endif

        {{--Contacten--}}
        @if(hasPermission('contacten_beheren'))
            <div class="nav-item-container" >
                <a class="nav-item">
                    <span class="nav-item-icon"><i class="fa-solid fa-address-card"></i></span>
                    <span class="nav-item-label" >Contacten</span>
                    <span class="nav-direction-icon">@icon_right</span>
                </a>
                <div class="nav-item-dropdown">
                    <a class="nav-item" href="{{url('/dashboard/contacten/create')}}">Contact toevoegen</a>
                    <a class="nav-item" href="{{url('/dashboard/contacten')}}">Alle Contacten</a>
                </div>
            </div>
        @endif

        {{--Rollen--}}
        @if(hasPermission('rollen_beheren'))
            <div class="nav-item-container">
                <a class="nav-item">
                    <span class="nav-item-icon"><i class="fa-solid fa-user-gear"></i></span>
                    <span class="nav-item-label" >Rollen</span>
                    <span class="nav-direction-icon">@icon_right</span>
                </a>
                <div class="nav-item-dropdown">
                    <a class="nav-item" href="{{url('/dashboard/rollen')}}">Alle rollen</a>
                </div>
            </div>
        @endif

        {{--Emails--}}
        @if(hasPermission(['emails_templates_beheren', 'emails_verzonden_inzien']))
            <div class="nav-item-container">
                <a class="nav-item">
                    <span class="nav-item-icon"><i class="fa-solid fa-envelope"></i></span>
                    <span class="nav-item-label" >Emails</span>
                    <span class="nav-direction-icon">@icon_right</span>
                </a>
                <div class="nav-item-dropdown">
                    @if(hasPermission('emails_verzonden_inzien')) <a class="nav-item" href="{{url('/dashboard/mails/sent')}}">Verzonden Emails</a> @endif
                        @if(hasPermission('emails_templates_beheren'))  <a class="nav-item" href="{{url('/dashboard/mails/templates')}}">Templates</a> @endif
                </div>
            </div>
        @endif

        {{--Instellingen--}}
        @if(hasPermission('instellingen_beheren'))
            <div class="nav-item-container" >
                <a class="nav-item">
                    <span class="nav-item-icon"><i class="fa-solid fa-gear"></i></span>
                    <span class="nav-item-label" >Instellingen</span>
                    <span class="nav-direction-icon">@icon_right</span>
                </a>
                <div class="nav-item-dropdown">
                    <a class="nav-item" href="{{url('/dashboard/instellingen/processen')}}">Processen</a>
                    <div class="nav-item-divider" ></div>
                    <a class="nav-item" href="{{url('/dashboard/instellingen/projecten')}}">Projecten</a>
                    <a class="nav-item" href="{{url('/dashboard/instellingen/adressen')}}">Adressen</a>
                    <div class="nav-item-divider" ></div>
                    <a class="nav-item" href="{{url('/dashboard/instellingen/lijsten')}}">Lijsten</a>
                    <a class="nav-item" href="{{url('/dashboard/instellingen/redenen')}}">Redenen</a>
                </div>
            </div>
        @endif

        {{--Koppelingen--}}
        @if(hasPermission('koppelingen_requests_inzien'))
            <div class="nav-item-container" >
                <a class="nav-item">
                    <span class="nav-item-icon"><i class="fa-solid fa-plug"></i></span>
                    <span class="nav-item-label" >Koppelingen</span>
                    <span class="nav-direction-icon">@icon_right</span>
                </a>
                <div class="nav-item-dropdown">
                    @foreach(getKoppelingen() as $koppeling)
                        <a class="nav-item" href="{{url('/dashboard/koppelingen?koppeling='.$koppeling)}}">{{$koppeling}}</a>
                    @endforeach
                </div>
            </div>
        @endif

        {{--Beheer--}}
        @if(isAdmin())
            <div class="nav-item-container" >
                <a class="nav-item">
                    <span class="nav-item-icon"><i class="fa-solid fa-sliders"></i></span>
                    <span class="nav-item-label" >Dashboard</span>
                    <span class="nav-direction-icon">@icon_right</span>
                </a>
                <div class="nav-item-dropdown">
                    <a class="nav-item" href="{{url('/dashboard/corporaties/create')}}">Corporatie toevoegen</a>
                    <a class="nav-item" href="{{url('/dashboard/corporaties')}}">Alle corporaties</a>
                    <div class="nav-item-divider" ></div>
                    <a class="nav-item" href="{{url('/dashboard/bedrijven/create')}}">Bedrijf toevoegen</a>
                    <a class="nav-item" href="{{url('/dashboard/bedrijven')}}">Alle bedrijven</a>
                </div>
            </div>
        @endif

        {{--Logs--}}
        @if(hasPermission('logs_inzien'))
            <div class="nav-item-container" >
                <a class="nav-item" href="{{url('/dashboard/logs')}}" >
                    <span class="nav-item-icon"><i class="fa-solid fa-clock-rotate-left"></i></span>
                    <span class="nav-item-label" >Logs</span>
                </a>
            </div>
        @endif

        {{--Logout--}}
        <form class="nav-item-container" method="POST" action="{{ route('logout') }}" >
            <button type="submit" class="nav-item">
                <span class="nav-item-icon"><i class="fa-solid fa-arrow-right-from-bracket"></i></span>
                <span class="nav-item-label" >Uitloggen</span>
            </button>
            @csrf
        </form>

        <div class="nav-text">
            <div>Hulp nodig met Asbestregisseur? Neem contact op met InforDB.</div>
            <div>Tel: 085 0642 300</div>
            <div>Email: <EMAIL></div>
        </div>

    </nav>

    <div class="nav-toggle-container" >
        <a class="btn nav-toggle-btn">@icon_bars</a>
    </div>

@endauth
