<div class="corner-loader"></div>
<div class="corner-notifications"></div>

<!-- Vendor Libraries -->
<script src="{{ asset('dashboard/js/vendor/popper.js') }}"></script>
<script src="{{ asset('dashboard/js/vendor/tippy.js') }}"></script>
<script src="{{ asset('dashboard/js/vendor/jsonview.js') }}"></script>
<script src="{{ asset('dashboard/js/vendor/chart.js') }}"></script>

<!-- Infor Libraries -->
<script src="{{ asset('dashboard/js/inforcdn/base.js') }}{{cacheClear()}}"></script>
<script src="{{ asset('dashboard/js/inforcdn/bar-selector.js') }}{{cacheClear()}}"></script>
<script src="{{ asset('dashboard/js/inforcdn/selectables.js') }}{{cacheClear()}}"></script>
<script src="{{ asset('dashboard/js/inforcdn/search.js') }}{{cacheClear()}}"></script>
<script src="{{ asset('dashboard/js/inforcdn/table.js') }}{{cacheClear()}}"></script>

<!-- Libraries -->
<script src="{{ asset('dashboard/js/utilities.js') }}{{cacheClear()}}"></script>
<script src="{{ asset('dashboard/js/dom.js') }}{{cacheClear()}}"></script>
<script src="{{ asset('dashboard/js/search.js') }}{{cacheClear()}}"></script>
<script src="{{ asset('dashboard/js/editor.js') }}{{cacheClear()}}"></script>
<script src="{{ asset('dashboard/js/handleFlow.js') }}{{cacheClear()}}"></script>
<script src="{{ asset('dashboard/js/sidebar.js') }}{{cacheClear()}}"></script>
<script src="{{ asset('dashboard/js/ar-chart.js') }}{{cacheClear()}}"></script>


@yield('script', '')

<script>
  $(document).ready(() => {
    initAlert();
  })

  function initAlert(){
    const alert = $('[data-session-alert]');
    if(!alert.children().length){ return; }

    const toggleAlert = () => {
      alert.toggle(200);
    }

    setTimeout(toggleAlert, 200);
    setTimeout(toggleAlert, 5000);
  }

</script>
