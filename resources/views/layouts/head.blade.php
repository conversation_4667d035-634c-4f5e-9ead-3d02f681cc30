<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Asbestregisseur') }}</title>

    <link rel="ICON" href="{{ url('dashboard/avatar.ico') }}">

    @livewireStyles
    @livewireScripts

    <!-- Scripts -->
    <script src="{{ asset('dashboard/js/app.js') }}"></script>
    <script src="{{ asset('dashboard/js/head.js') }}"></script>

    <!-- Vendor Scripts -->
    <script src="{{ asset('dashboard/js/vendor/jquery.js') }}"></script>
    <script src="{{ asset('dashboard/js/vendor/ckeditor.js') }}"></script>

    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.gstatic.com">
    <link href="https://fonts.googleapis.com/css?family=Nunito" rel="stylesheet">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@48,400,0,0"/>

    <!-- Vendor Styles -->
    <link href="{{ asset('dashboard/css/vendor/font-awesome.css') }}{{cacheClear()}}" rel="stylesheet">
    <link href="{{ asset('dashboard/css/vendor/bootstrap.css') }}{{cacheClear()}}" rel="stylesheet">
    <link href="{{ asset('dashboard/css/vendor/jsonview.css') }}{{cacheClear()}}" rel="stylesheet">

    <!-- Vendor Styles -->
    <link href="{{ asset('dashboard/css/app.css') }}{{cacheClear()}}" rel="stylesheet">
    <link href="{{ asset('dashboard/css/utilities.css') }}{{cacheClear()}}" rel="stylesheet">
    <link href="{{ asset('dashboard/css/buttons.css') }}{{cacheClear()}}" rel="stylesheet">
    <link href="{{ asset('dashboard/css/sidebar.css') }}{{cacheClear()}}" rel="stylesheet">

    <!-- Infor Libraries -->
    <link href="{{ asset('dashboard/css/inforcdn/base.css') }}{{cacheClear()}}" rel="stylesheet">
    <link href="{{ asset('dashboard/css/inforcdn/bar-selector.css') }}{{cacheClear()}}" rel="stylesheet">
    <link href="{{ asset('dashboard/css/inforcdn/selectables.css') }}{{cacheClear()}}" rel="stylesheet">
    <link href="{{ asset('dashboard/css/inforcdn/search.css') }}{{cacheClear()}}" rel="stylesheet">
    <link href="{{ asset('dashboard/css/inforcdn/table.css') }}{{cacheClear()}}" rel="stylesheet">

    <script>
      var _global = {
        csrf: @json(csrf_token()),
        url: @json(url('/')),
        user: @json(_user()->censored()),
        regex_ignore: /[ `!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/g,
      }
    </script>

    <style>

        @if(isRoute('/login'))
            main{
                width: 100%!important;
            }
        @endif

    </style>

</head>
