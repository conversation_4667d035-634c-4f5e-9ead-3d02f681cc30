<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

@include('layouts.head')

<body @if(Request::is('dashboard/login')) class="blauweachtergrond" @endif>
    <div id="app">
        <div class="main-container d-flex">
            @include('layouts.sidebar')
            <main @if(_cookie('sidebar_hidden') == 'true' || isMobile()) data-sidebar-hidden @endif >

                @page_loader

                <div data-main-content class="container-fluid opacity-0">
                    <div class='w-100' data-session-alert style="display: none" >
                        @if (session('success'))
                           <div class="bg-success p-2 rounded-3 text-white">{!! session('success') !!}</div>
                        @elseif (session('danger'))
                           <div class="bg-danger p-2 rounded-3 text-white">{!! session('danger') !!}</div>
                        @elseif (session('primary'))
                           <div class="bg-primary p-2 rounded-3 text-white">{!! session('primary') !!}</div>
                        @endif
                    </div>

                    @yield('content')
                </div>

            </main>



        </div>
    </div>
    @include('layouts.modals')
    @include('layouts.footer')

</body>
</html>
