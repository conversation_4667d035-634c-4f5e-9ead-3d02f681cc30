@extends('layouts.app')

@section('content')
    <section class="append-loader">

        <livewire:corporaties.corporatie :corporatie="$corporatie" :database="$database" />

        @if($corporatie->id)
            <div class="card">
                <table class="table table-borderless">
                    <thead>
                        <tr style="border-bottom: 1px solid rgb(222,226,230)">
                            <th scope="col">Naam</th>
                            <th scope="col">Username</th>
                            <th scope="col">Email</th>
                            <th scope="col">Role</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($corporatie->users as $user)
                            <tr scope="row">
                                <td>{{ $user->name }}</td>
                                <td>{{ $user->username }}</td>
                                <td>{{ $user->email }}</td>
                                <td>{{ $user->roleByDomain($corporatie->id)->name }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @endif
    </section>
@endsection
