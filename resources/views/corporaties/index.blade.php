@extends('layouts.app')

@section('content')
    <section class="append-loader">

        <infor-table id="corporaties_table">
            <infor-table-pagination>
                <infor-table-order-option data-column="domein">Domein</infor-table-order-option>
                <infor-table-order-option data-column="startdatum">Startdatum</infor-table-order-option>
                <infor-table-order-option data-column="gebruikers">Aantal gebruikers</infor-table-order-option>
            </infor-table-pagination>

            <infor-table-count data-single="corporatie" data-multiple="corporaties"></infor-table-count>

            <infor-table-header>Logo</infor-table-header>
            <infor-table-header>Domein</infor-table-header>
            <infor-table-header>Startdatum</infor-table-header>
            <infor-table-header>Aantal gebruikers</infor-table-header>
            <infor-table-header></infor-table-header>

        </infor-table>
    </section>
@endsection

@section('script')
    <script>

        pageInteractive(() => {
            initInforTables({
                id: 'corporaties_table',
                api: `/dashboard/api/corporaties/get`,
                response_records_name: 'corporaties',
                search: {
                    api: 'dashboard/api/search/corporatie',
                    content: 'domein',
                    sub_content: `user`,
                    placeholder: `Zoeken...`,
                },
                errorHandler: handleAjaxErrors,
                fillRecord: fillRecord,
                execute: [tippyInit],
            });
        });

        function fillRecord(record) {
            const { id, logo_guid, domein, startdatum, users } = record;

            return `
                <tr>
                    <td class="w-0" >${logo_guid ? `<img height="35" src="${_file(logo_guid)}" >` : ''}</td>
                    <td>${domein || ''}</td>
                    <td>${parseDate(startdatum).date.eu}</td>
                    <td>${users.length}</td>
                    <td class="text-right">
                        <a class="btn btn-light" href="${_url(`/dashboard/corporaties/edit/${id}`)}">Wijzigen @icon_edit</a>
                    </td>
                </tr>
            `;
        }

    </script>
@endsection
