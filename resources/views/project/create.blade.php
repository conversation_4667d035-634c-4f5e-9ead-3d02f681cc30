@extends('layouts.app')

@section('content')
    <div class="row justify-content-center">
        <div class="col-lg-12">
            @if (session('status'))
                <div class="alert alert-success" role="alert">
                    {{ session('status') }}
                </div>
            @endif

            <form method="post" data-main-form>

                <h4>Project</h4>
                <div class="card">
                    <div class="row">
                        @foreach($stap->inputsByContext('project') as $input)
                            <div class="col-md-6 col-12 my-2">
                                {!! $input->HTMLLabel() !!}
                                {!! $input->HTMLInput() !!}
                            </div>
                        @endforeach
                    </div>
                </div>

                <h4>Adres</h4>
                <div class="card">
                    <div class="row">

                        <div class="col-12 my-2">
                            <label>Adres selecteren</label>
                            <div class="form-control" data-adres-search-container >
                                <input class="form-control-plaintext p-0 h-100" placeholder="Zoeken" name="search_adres" >
                                <input type="hidden" name="adres_id" >
                            </div>
                            <div class="position-relative">
                                <div class="position-absolute p-2 max-h-400 overflow-auto bg-white border rounded z-index-9 shadow w-100 d-none" data-adressen-search-results ></div>
                            </div>
                        </div>
                        <div class="col-12 my-2 text-center">
                            <div class="flex-between">
                                <div class="border-bottom w-100"></div>
                                <div class="mx-3" >OF</div>
                                <div class="border-bottom w-100"></div>
                            </div>
                        </div>
                        @foreach($stap->inputsByContext('adres') as $input)
                            <div class="col-md-6 col-12 my-2">
                                {!! $input->HTMLLabel() !!}
                                {!! $input->HTMLInput(['data' => 'data-adres-input']) !!}
                            </div>
                        @endforeach
                        <div class="col-12" data-adres-check ></div>
                    </div>
                </div>

                <div class="my-4 text-center">
                    <input class="btn btn-success" type="submit" value="Opslaan" >
                    @csrf
                </div>

            </form>


        </div>
    </div>

@endsection

@section('script')
<script>

    const _search = {
        results: [],
        container: $('[data-adressen-search-results]'),
        timeout: null,
        request: null,
        string: () => { return $('[name=search_adres]').val(); },
        is_loading: () => { return !!($('[data-adressen-search-results]').find('[data-spinner]').length) }
    }

    $(document).click(() => {

        resetAdressen();

    });

    $('[data-adres-input]').on('input', function(){
        $('[name=search_adres], [name=adres_id]').val(``).trigger('change');

        if(_search.request){
            _search.request.abort();
            _search.request = null;
        }

        const search_string = $('[data-adres-input]').map(function(){ return ` ${$(this).val() || ''}` }).get().join('');
        if(!search_string.replaceAll(' ', '')){
            $(`[data-adres-check]`).empty();
            return;
        }


        _search.request = searchAdres(search_string);
        _search.request.then(response => {
            if (!response.length){
                $(`[data-adres-check]`).empty();
                return;
            }

            $(`[data-adres-check]`).html(`<div class="text-danger" >Er zijn ${response.length}${response.length === 100 ? '+' : ''} resultaten gevonden. Weet je zeker dat je een nieuw adres wilt toevoegen?</div>`)
        })
    })
    $('[name=postcode]').on('input', function(){
        let postcode = this.value.replaceAll(' ', '');
        let huis_nr = `${$('[name=huisnummer]').val() || ''}${$('[name=toevoeging]').val() || ''}`

        if(!postcode || postcode.length < 6){ return; }

        getLocation(postcode, huis_nr)
            .then(response => {
                if(!response.status){ return; }

                const { plaats, straat } = response.data;

                $('[name=plaats]').val(plaats || '')
                $('[name=straat]').val(straat || '')
            })
            .catch(err => {

            });

    })
    $('[name=search_adres]').on('input', async function(){
        let { container, timeout, is_loading } = _search;

        if (timeout){
            clearTimeout(timeout);
            timeout = null;
        }

        if(!is_loading()){
            container.removeClass('d-none').html(`<div class="text-center py-2" data-spinner >@spinner</div>`);
        }

        if(!this.value){
            resetAdressen();
            this.value = '';
            return;
        }


        _search.timeout = setTimeout(findAdressen, 100);
    })
    $('[name=adres_id]').change(function(){
        $('[data-adres-search-container]').removeClass('border-success');

        if(this.value){
            $('[data-adres-search-container]').addClass('border-success');
        }
    });

    async function findAdressen(){
        try{
            _search.adressen = await searchAdres( _search.string() );

            const { adressen, container } = _search;
            if(!adressen){ throw { message: 'Er is iets foutgegaan' } }

            container.empty();
            if(!adressen?.length){
                container.html('<div class="py-1 px-2 text-center" >Geen resultaten gevonden</div>')
                return;
            }

            for(const adres of adressen){
                const { id, straat, huisnummer, toevoeging, plaats, postcode, vhenummer, complexnummer } = adres;
                container.append(`
                    <div class="d-flex align-items-center pt-2 pb-1 mx--1 border-bottom hover-bg-inverse-secondary cursor-pointer" onclick="selectAdres(${id})" >
                        ${ complexnummer ? `<span class="badge badge-primary tippy mx-1" data-tippy-content="Complexnummer" >${complexnummer}</span>` : '' }
                        ${ vhenummer ? `<span class="badge badge-primary tippy mx-1" data-tippy-content="VHE nummer" >${vhenummer}</span>` : '' }
                        <span>${straat || ''} ${huisnummer || ''}${toevoeging || ''} ${postcode || ''} ${plaats || ''}</span>
                    </div>`)
            }

            tippyInit();

        }
        catch (err){
            container.empty();
            if(err?.message){ notification(err.message || ''); }
        }
    }
    function resetAdressen(){
        const { container } = _search;
        container.addClass('d-none').empty();
    }
    function selectAdres(id){
        const adres = _search.adressen.find(row => row.id == id)

        if(!adres){
            notification('Er is iets foutgegaan!');
            return;
        }

        const { straat, huisnummer, toevoeging, plaats, postcode } = adres;
        $('[name=search_adres]').val(`${straat || ''} ${huisnummer || ''}${toevoeging || ''}, ${postcode || ''} ${plaats || ''}`)
        $('[name=adres_id]').val(id).trigger('change');
        $('[data-adres-input]').val('');
        $('[data-adres-check]').empty();
    }

</script>
@endsection
