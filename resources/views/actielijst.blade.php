@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">{{ __('Dashboard') }}</div>

                <div class="card-body">
                    @if (session('status'))
                        <div class="alert alert-success" role="alert">
                            {{ session('status') }}
                        </div>
                    @endif

                    ACTIELIJST<br>

                    @foreach($projecten AS $project)
                        {{ $project->projectnummer }} <br>
                    @endforeach
                    afdsd<br>
                    asfdasffsdfsdff<br>
                    zdfszdff<br>
                    asfsadfsf<br>
                    afdsd<br>
                    asfdasffsdfsdff<br>
                    zdfszdff<br>
                    asfsadfsf<br>
                    afdsd<br>
                    asfdasffsdfsdff<br>
                    zdfszdff<br>
                    asfsadfsf<br>
                    afdsd<br>
                    asfdasffsdfsdff<br>
                    zdfszdff<br>
                    asfsadfsf<br>
                    afdsd<br>
                    asfdasffsdfsdff<br>
                    zdfszdff<br>
                    asfsadfsf<br>
                    afdsd<br>
                    asfdasffsdfsdff<br>
                    zdfszdff<br>
                    asfsadfsf<br>
                    afdsd<br>
                    asfdasffsdfsdff<br>
                    zdfszdff<br>
                    asfsadfsf<br>
                    afdsd<br>
                    asfdasffsdfsdff<br>
                    zdfszdff<br>
                    asfsadfsf<br>
                    afdsd<br>
                    asfdasffsdfsdff<br>
                    zdfszdff<br>
                    asfsadfsf<br>
                    afdsd<br>
                    asfdasffsdfsdff<br>
                    zdfszdff<br>
                    asfsadfsf<br>
                    afdsd<br>
                    asfdasffsdfsdff<br>
                    zdfszdff<br>
                    asfsadfsf<br>
                    afdsd<br>
                    asfdasffsdfsdff<br>
                    zdfszdff<br>
                    asfsadfsf<br>
                    afdsd<br>
                    asfdasffsdfsdff<br>
                    zdfszdff<br>
                    asfsadfsf<br>
                    afdsd<br>
                    asfdasffsdfsdff<br>
                    zdfszdff<br>
                    asfsadfsf<br>
                    afdsd<br>
                    asfdasffsdfsdff<br>
                    zdfszdff<br>
                    asfsadfsf<br>
                    afdsd<br>
                    asfdasffsdfsdff<br>
                    zdfszdff<br>
                    asfsadfsf<br>
                    afdsd<br>
                    asfdasffsdfsdff<br>
                    zdfszdff<br>
                    asfsadfsf<br>
                    afdsd<br>
                    asfdasffsdfsdff<br>
                    zdfszdff<br>
                    asfsadfsf<br>
                    afdsd<br>
                    asfdasffsdfsdff<br>
                    zdfszdff<br>
                    asfsadfsf<br>
                    afdsd<br>
                    asfdasffsdfsdff<br>
                    zdfszdff<br>
                    asfsadfsf<br>
                    afdsd<br>
                    asfdasffsdfsdff<br>
                    zdfszdff<br>
                    asfsadfsf<br>
                    afdsd<br>
                    asfdasffsdfsdff<br>
                    zdfszdff<br>
                    asfsadfsf<br>
                    afdsd<br>
                    asfdasffsdfsdff<br>
                    zdfszdff<br>
                    asfsadfsf<br>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
