{{-- Stap 4: Aanmaken projecten --}}
{{-- De data is volledig opgeschoond en kloppend volgens de database richtlijnen (ID's komen overeen)--}}
{{-- Nu kan voor ieder adres in de datasets een project aangemaakt worden in 'projecten', waarbij vervolgens de bronnen worden toegevoegd in 'asbest_bronnen' --}}
@extends('layouts.app')

@section('content')
<div class="container-fluid">

    {{-- Definiëren titel en subtitel --}}
    <h1>Aanmaken projecten</h1>
    <h5> Zijn alle voorgaande stappen volledig doorlopen? </h5>

    {{-- Aanmaken grote buttons --}}
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-sm-6">
                    {{-- Knop 1: Aanmaken projecten (Wanneer alle stappen doorlopen zijn) --}}
                    {{-- <PERSON><PERSON><PERSON><PERSON> wordt de functie 'insertProjecten()' uitgevoerd --}}
                    <a class="btn btn-success btn-lg w-100" onclick="insertProjecten()">
                        <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="currentColor" class="bi bi-box-arrow-right" viewBox="0 0 16 16">
                            <path fill-rule="evenodd" d="M10 12.5a.5.5 0 0 1-.5.5h-8a.5.5 0 0 1-.5-.5v-9a.5.5 0 0 1 .5-.5h8a.5.5 0 0 1 .5.5v2a.5.5 0 0 0 1 0v-2A1.5 1.5 0 0 0 9.5 2h-8A1.5 1.5 0 0 0 0 3.5v9A1.5 1.5 0 0 0 1.5 14h8a1.5 1.5 0 0 0 1.5-1.5v-2a.5.5 0 0 0-1 0v2z"/>
                            <path fill-rule="evenodd" d="M15.854 8.354a.5.5 0 0 0 0-.708l-3-3a.5.5 0 0 0-.708.708L14.293 7.5H5.5a.5.5 0 0 0 0 1h8.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3z"/>
                        </svg>
                        <br>
                        Ja, maak projecten aan
                    </a>
                </div>
                <div class="col-sm-6">
                    {{-- Knop 2: Breng me terug (Wanneer toch niet alle stappen doorlopen zijn) --}}
                    {{-- Deze knop brengt je terug naar de hoofdpagina --}}
                    <a class="btn btn-danger btn-lg w-100" href="{{ url('dashboard/tempimport') }}">
                        <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="currentColor" class="bi bi-box-arrow-left" viewBox="0 0 16 16">
                            <path fill-rule="evenodd" d="M6 12.5a.5.5 0 0 0 .5.5h8a.5.5 0 0 0 .5-.5v-9a.5.5 0 0 0-.5-.5h-8a.5.5 0 0 0-.5.5v2a.5.5 0 0 1-1 0v-2A1.5 1.5 0 0 1 6.5 2h8A1.5 1.5 0 0 1 16 3.5v9a1.5 1.5 0 0 1-1.5 1.5h-8A1.5 1.5 0 0 1 5 12.5v-2a.5.5 0 0 1 1 0v2z"/>
                            <path fill-rule="evenodd" d="M.146 8.354a.5.5 0 0 1 0-.708l3-3a.5.5 0 1 1 .708.708L1.707 7.5H10.5a.5.5 0 0 1 0 1H1.707l2.147 2.146a.5.5 0 0 1-.708.708l-3-3z"/>
                        </svg>
                        <br>
                        Nee, breng me terug
                    </a>
                </div>
            </div>
            {{-- Percentage balk --}}
            {{-- Bij het aanmaken van de projecten geeft deze de voortgang weer (Aangezien dit proces even kan duren) --}}
            <div class="overflow-hidden rounded mt-2 d-none" style="border: 1px solid black;">
                <div id="totPercBar" class="py-1 bg-primary transition-05" style="width: 0%; height: 20px;"></div>
            </div>
        </div>
    </div>
@endsection

{{-- Script --}}
{{-- Hierin worden de uit te voeren functies gedefiniëerd --}}
@section('script')
<script>

    // Aanmaken variabelen
    var histbronnencount = @json($histbronnencount);
    var perc = 0; //Percentage als standaard op 0 zetten

    // Invoeren projecten (wanneer op 'Ja, maak projecten aan' wordt geklikt)
    function insertProjecten(i) {
        $("#totPercBar").parent().removeClass('d-none'); // Maak de percentage balk zichtbaar
        if(!i){var i = 0;}
        $.ajax({
            method: "POST",
            url: "https://www.asbestregisseur.be/api/insertproject",
            data: {
                'i': i,
            }
        })
        .done(function (response){
            if(!response.success){
                alert("DOE KINS ECHT NIEKS");
                return;
            }
            // Berekenen percentage (Voortgang percentage balk bepalen)
            perc = i/histbronnencount*100;
            $("#totPercBar").attr("style", "width: "+perc+"%; height: 20px;");
            if(i <= histbronnencount){
                i = i + 100;
                insertProjecten(i);
            }
            // Wanneer de balk 100% heeft bereikt (en dus alle projecten zijn aangemaakt)
            // Ga terug naar hoofdpagina
            if (perc >= 100)
                {
                    window.location.href = "https://asbestregisseur.be/dashboard/tempimport";
                }
        })
        // In geval van een error, geef pop-up met foutmelding weer
        .fail(function (jqXHR, textStatus, errorThrown){
            alert("Er is een fout opgetreden: "+errorThrown);
        });
    }
</script>
@endsection
