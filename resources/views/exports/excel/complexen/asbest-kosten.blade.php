<table>

    @if(hasPermission('asbest_prices'))
        <tbody>
            <tr>
                <td colspan="2" ><b>Uitvoerkosten</b></td>
            </tr>
            @foreach($uitvoerkosten as $year => $cost)
                <tr>
                    <td style="text-align: left" ><b>{{$year}}</b></td>
                    <td style="text-align: left" >€ {{ $cost->get(PriceType::EXCL) }}</td>
                </tr>
            @endforeach

            <tr></tr>
            <tr></tr>

        </tbody>
    @endif

    @foreach($complexen as $complex)

        <tbody>
            <tr> <td colspan="50" style="border: 1px solid white; border-top: unset;" ></td> </tr>
            <tr> <td colspan="50" style="border: 1px solid white; border-bottom: unset;" ></td> </tr>
        </tbody>

        <tbody>
            <tr>
                <td><b>Complexnummer:</b></td>
                <td style="text-align: left;" >{{$complex->complexnummer}}</td>
            </tr>
            <tr>
                <td><b><PERSON><PERSON> van uitvoer:</b></td>
                <td style="text-align: left;" >{{$complex->uitvoerjaar ?? 'Onbekend'}}</td>
            </tr>
            <tr>
                <td><b>Aantal Adressen:</b></td>
                <td style="text-align: left;" >{{$complex->adressen()->count()}}</td>
            </tr>

            <tr></tr>

            <tr>
                <td><b>Bron</b></td>
                @if(hasPermission('asbest_prices')) <td><b>Prijs</b></td> @endif
                <td><b>Aantal geinventariseerd</b></td>
                @if($settings['cost_incl_indicatie']) <td><b>Aantal Indicatie ( obv {{$settings['indicatie_percentage']}}% )</b></td> @endif
                @if($settings['cost_incl_referentie']) <td><b>Aantal Referentie</b></td> @endif
                @if(hasPermission('asbest_prices')) <td><b>Totaal</b></td> @endif
            </tr>

        </tbody>
        <tbody>
            @php
                $geinventariseerd_total = 0;
                $indicatie_total = 0;
                $referentie_total = 0;
            @endphp

            @foreach($complex->uniekeAsbestBronnen() as $bron)
                @php
                    $geinventariseerd = $complex->asbestBronnen($bron->omschrijving_id, $bron->locatie_id)->where('gesaneerd', 0)->where('asbesthoudend', 1)->count();

                    $indicatie = 0;
                    if($settings['cost_incl_indicatie']){
                        $indicatie = $complex->aantalIndicatieBronnen($bron->omschrijving_id, $bron->locatie_id, $settings['indicatie_percentage']);
                    }

                    $referentie = 0;
                    if($settings['cost_incl_referentie']){
                        $referentie = $complex->aantalReferentieBronnen($bron->omschrijving_id, $bron->locatie_id, AsbestBronStatus::REFERENTIE);
                    }

                    if(hasPermission('asbest_prices')){
                        $total = $bron->cost()->multiply($geinventariseerd + $indicatie + $referentie);

                        $geinventariseerd_total += $geinventariseerd;
                        $indicatie_total += $indicatie;
                        $referentie_total += $referentie;
                    }
                @endphp

                <tr>
                    <td>{{$bron->locatieOmschrijving()}}</td>
                    @if(hasPermission('asbest_prices')) <td>€ {{$bron->cost()->get(PriceType::EXCL)}}</td> @endif
                    <td>{{ $geinventariseerd }}</td>
                    @if($settings['cost_incl_indicatie']) <td>{{$indicatie}}</td> @endif
                    @if($settings['cost_incl_referentie']) <td>{{$referentie}}</td> @endif
                    @if(hasPermission('asbest_prices')) <td>€ {{$total->get(PriceType::EXCL)}}</td> @endif
                </tr>
            @endforeach

            @if(hasPermission('asbest_prices'))
                <tr>
                    <td><b>Totaal:</b></td>
                    <td></td>
                    <td><b>{{$geinventariseerd_total}}</b></td>
                    @if($settings['cost_incl_indicatie']) <td><b>{{$indicatie_total}}</b></td> @endif
                    @if($settings['cost_incl_referentie']) <td><b>{{$referentie_total}}</b></td> @endif
                    <td><b>€ {{$complex->asbestBronnenTotalCost($settings)->get(PriceType::EXCL)}}</b></td>
                </tr>
            @endif

        </tbody>

    @endforeach

</table>
