<table>

    <thead>

        <tr>
            <th><b>Complexnummer:</b></th>
            <th style="text-align: left;" >{{$complex->complexnummer}}</th>
        </tr>
        <tr>
            <th><b><PERSON><PERSON> <PERSON> u<PERSON>er:</b></th>
            <th style="text-align: left;" >{{$complex->uitvoerjaar}}</th>
        </tr>
        <tr>
            <th><b>Aantal Adressen:</b></th>
            <th style="text-align: left;" >{{$complex->adressen()->count()}}</th>
        </tr>

        <tr>
            <th></th>
        </tr>

        <tr>
            <th></th>
            @foreach($complex->uniekeAsbestBronnen() as $bron)
                <th>{{$bron->locatieOmschrijving()}} @if(hasPermission('asbest_prices')) ( € {{$bron->cost()->get(PriceType::EXCL)}} ) @endif</th>
            @endforeach
            @if(hasPermission('asbest_prices')) <th>Totaal</th> @endif
        </tr>
    </thead>
    <tbody>

        @foreach($complex->adressen() as $adres)

            {{--display an address mutliple times to fit each bron into 1 cell vertically--}}
            @php
                $adres_rows = 1;
                foreach($complex->uniekeAsbestBronnen() as $bron){
                    $adres_bronnen_count = $complex->adresAsbestBronnen($adres->id, $bron->omschrijving_id, $bron->locatie_id)->count();
                    $adres_referentie_bronnen_count = $complex->adresAsbestReferentieBronnen($adres->id, $bron->omschrijving_id, $bron->locatie_id)->count();

                    if($adres_bronnen_count > $adres_rows){ $adres_rows = $adres_bronnen_count; }
                    if($adres_referentie_bronnen_count > $adres_rows){ $adres_rows = $adres_referentie_bronnen_count; }
                }
            @endphp

            @for($adres_row = 0; $adres_row < $adres_rows; $adres_row++)
                <tr>
                    <td style="
                     border: 1px solid white;
                     @if(!$adres_row && $adres_rows > 1) border-bottom: 1px solid {{ AdresStatus::get($adres->inventarisatieStatus(), 'color') }}; @endif
                     @if($adres_row) border-top: 1px solid {{ AdresStatus::get($adres->inventarisatieStatus(), 'color') }}; @endif
                     background-color: {{ AdresStatus::get($adres->inventarisatieStatus(), 'color') }}"
                    >
                        @if(!$adres_row) {{$adres->addressLine()}} @endif
                    </td>

                    {{--Bronnen--}}
                    @foreach($complex->uniekeAsbestBronnen() as $bron)
                        @php
                            $adres_bronnen = $complex->adresAsbestBronnen($adres->id, $bron->omschrijving_id, $bron->locatie_id);
                            $adres_referentie_bronnen = $complex->adresAsbestReferentieBronnen($adres->id, $bron->omschrijving_id, $bron->locatie_id);

                            $status = 'NIET_AANGETROFFEN';
                            $text = '';
                            $row_bron = null;

                            if(isset($adres_bronnen[$adres_row])){
                                $row_bron = $adres_bronnen[$adres_row];
                                $text = "{$row_bron->aantal} {$row_bron->eenheid}";
                                $status = $row_bron->status();
                            }
                            elseif(isset($adres_referentie_bronnen[$adres_row])){
                                $row_bron = $adres_referentie_bronnen[$adres_row];
                                $text = "{$row_bron->aantal} {$row_bron->eenheid} ";
                                $status = $row_bron->referentieStatus();
                            }
                            elseif(!$adres_row && $settings['indicatie_show'] && $complex->isIndicatieBron($bron->omschrijving_id, $bron->locatie_id, $settings['indicatie_percentage'])){
                                $status = 'INDICATIE';
                                $text = 'Indicatie';
                            }

                        @endphp

                        <td class="border-2 p-0" style="border: 1px solid white; background-color: {{AsbestBronStatus::color($status)}}" > {{$text}}</td>
                    @endforeach


                    {{--Total--}}
                    @if(hasPermission('asbest_prices'))
                        <td style="
                         border: 1px solid white;
                         @if(!$adres_row && $adres_rows > 1) border-bottom: 1px solid #212529; @endif
                         @if($adres_row) border-top: #212529; @endif
                         background-color: #212529;
                         color: white"
                        >
                            @if(!$adres_row) € {{$adres->asbestBronnenTotalCost($settings)->get(PriceType::EXCL)}} @endif
                        </td>
                    @endif

                </tr>
            @endfor

        @endforeach

        @if(hasPermission('asbest_prices'))
            <tr>
                <td style="background-color: #212529; color: white; text-align: right" colspan="{{$complex->uniekeAsbestBronnen()->count() + 1}}" ><b>Totaal:</b></td>
                <td style="background-color: #212529; color: white" ><b>€ {{ $complex->asbestBronnenTotalCost($settings)->get(PriceType::EXCL)  }}</b></td>
            </tr>
        @endif

    </tbody>

</table>
