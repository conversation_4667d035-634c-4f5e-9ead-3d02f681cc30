@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">

            <h1>Nieuws</h1>

            @foreach($nieuws AS $bericht)
                <div class="card" style="margin-bottom: 10px">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-2">
                                <img src="{{ $bericht->afbeelding }}" style="width: 100%">
                            </div>
                            <div class="col-sm-10">
                                <h3>{{ $bericht->titel }}</h3>
                                <p><i>{!! nl2br($bericht->subtitel) !!} ....</i><br><br><a href="{{ url('dashboard/nieuws/'.$bericht->id) }}"><button class="btn btn-secondary"><PERSON><PERSON> meer</button></a></p>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer"><i>Publicatiedatum: {{ \Carbon\Carbon::parse($bericht->datum)->format('d-m-Y') }}</i></div>
                </div>
            @endforeach

            @if(count($nieuws) == 0)
                <p>Er zijn nog geen nieuwsberichten gepubliceerd.</p>
            @endif
        </div>
    </div>
</div>
@endsection
