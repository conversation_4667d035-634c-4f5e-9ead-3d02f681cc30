{{-- Stap 1: <PERSON><PERSON> (<PERSON><PERSON><PERSON>) --}}
{{-- <PERSON><PERSON> er meer adressen tussen de opmerkingen staan, moeten deze worden toegevoegd als nieuwe rij --}}
@extends('layouts.app')

@section('content')

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-12">
            @if (session('status'))
                <div class="alert alert-success" role="alert">
                    {{ session('status') }}
                </div>
            @endif
            {{-- Definiëren titel met subtitel --}}
            <h1>Adress<PERSON></h1>
            <p>Adressen kopiëren indien deze in de opmerking vermeld zijn.</p>

            {{-- Definiëren tabel titels --}}
            <hr>
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Adresid</th>
                        <th>Complex</th>
                        <th>VHE nummer</th>
                        <th>Adres</th>
                        <th>#</th>
                        <th>Aantal/Eenheid</th>
                        <th>Locatie</th>
                        <th>Omschrijving</th>
                        <th>Opmerking</th>
                        <th>Opmerking2</th>
                        <th>Opmerking3</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody id="tbody">

                </tbody>
            </table>

            {{-- Button aan einde pagina --}}
            {{-- Wanneer deze wordt ingedrukt moet de functie 'clearAndGetBronnen()' worden uitgevoerd --}}
            {{-- Deze functie geeft de huidige adressen 'adreskopieklaar = 1' en geeft de volgende 100 weer (met status 'adreskopieklaar = 0' --}}
            <button class="btn btn-primary" id="volgendebutton" onclick="clearAndGetBronnen()" style="display: none">Akkoord, Volgende 100</button>
            {{-- Wanneer er geen adressen meer weer te geven zijn (allemaal doorlopen), moet een button komen die je terugleidt naar de hoofdpagina --}}
            <a href="{{ url('dashboard/tempimport') }}" id="terugbutton"><button class="btn btn-primary" style="display: none">Terug</button></a>
        </div>
    </div>
</div>

{{-- Pop-up menu, deze wordt weergeven wanneer op 'kopieer' wordt geklikt --}}
{{-- In dit menu worden alle adressen van het desbetreffende complex weergeven, en kunnen simpelweg toegevoegd worden door er op te klikken. --}}
<script src="https://code.jquery.com/ui/1.13.0/jquery-ui.min.js"></script>

<div class="modal fade" id="complexModal" tabindex="-1" role="dialog" aria-labelledby="complexModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable" role="document">
        <div class="modal-content">
        <div class="modal-header">
            <h5 class="modal-title" id="complexModalLabel"></h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body" id="complexModalBody">
            Adressen...
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-primary" onclick="save()">Voeg adressen toe</button>
        </div>
        </div>
    </div>
</div>

@endsection

{{-- Script --}}
{{-- Hierin worden de uit te voeren functies gedefiniëerd --}}
@section('script')
<script>

    // Weergeven bronnen bij openen pagina
    $( document ).ready(function() {
        getBronnen();
    });

    // Legen van de tabel
    function clear() {
        $('#tbody').empty();
    }

    var i = 0;
    var adressen = [];
    var regelid = 0;

    // Tabel leeghalen en volgende 100 weergeven bij doorvoeren pagina
    function clearAndGetBronnen(){
        i++;
        clear();
        getBronnen();
    }

    // Ophalen 100 volgende bronnen
    function getBronnen(){
        $.ajax({
            method: "POST",
            url: "https://www.asbestregisseur.be/api/get100tempbronnensaneerder",
            data: {
                i: i
            },
        })
        // Wanneer er bronnen worden weergeven > Geef 'Akkoord, volgende 100' button weer
        // Wanneer er geen bronnen meer zijn > Geef 'Terug' button weer
        .done(function (response){
            if(response.bronnen.length == 0){
                $('#volgendebutton').css("display", "none");
                $('#terugbutton').css("display", "block");
            } else {
                $('#volgendebutton').css("display", "block");
                $('#terugbutton').css("display", "none");
                $.each(response.bronnen, function (index, value){
                    $('#tbody').append('<tr><td>'+value.adres_id+'</td><td>'+value.complexnummer+'</td><td>'+value.vhenummer+'</td><td>'+value.straat+' '+value.huisnummer+''+value.toevoeging+' '+value.postcode+' '+value.plaats+' </td><td>'+value.bronnummer+'</td><td>'+value.aantal+' '+value.eenheid+'</td><td>'+value.locatie+'</td><td>'+value.omschrijving+'</td><td>'+value.opmerking+'</td><td>'+value.opmerking2+'</td><td>'+value.opmerking3+'</td><td><button class="btn btn-primary" id="knop'+value.regelid+'" data-toggle="modal" data-target="#complexModal" onclick="kopieer(\''+value.complexnummer+'\', \''+value.regelid+'\')">Kopieer</button></td></tr>');
                });
                window.scrollTo(0,0);

                // Maak het modal draggable
                $('#complexModal').draggable({
                    handle: ".modal-header"
                });
            }
        })
        // Foutmelding
        .fail(function (jqXHR, textStatus){
            alert("error: "+textStatus);
        });
    }

    // Achterliggende functie wanneer op 'Kopieer' wordt geklikt -> Openen pop-up menu
    function kopieer(complexnummer, id){
        regelid = id;
        $('#complexModal').modal('toggle'); // Geef het pop-up menu weer
        $('#complexModalLabel').empty(); // Leeg pop-up menu
        $('#complexModalLabel').append('Complex: '+complexnummer); // Geef bijbehorende adressen
        $.ajax({
            method: "GET",
            url: "https://www.asbestregisseur.be/api/getcomplexadressen/"+complexnummer,
        })
        .done(function (response){
            adressen = response.adressen;
            $('#complexModalBody').empty(); //Empty modal box
            $.each(adressen, function (index, value){
                $('#complexModalBody').append('<table class="table"><tr><td style="width: 90%">'+value.straat+' '+value.huisnummer+' '+value.toevoeging+'</td><td class="text-right" style="width: 10%"><input type="checkbox" id="'+value.id+'"></td></tr></table>');
            });
        })
        .fail(function (jqXHR, textStatus){
            alert("error: "+textStatus);
        });
    }

    // Sluiten pop-up menu
    $(function () {
        $("#complexModal button.close").on('click', function() {
            $('#complexModal').modal('hide');
        });
    });

    // Wanneer een adres is aangevinkt, sla gegevens hoofdrij op voor extra adres
    function save(){
        var data = {};
        data['regelid'] = regelid;
        data['adressen'] = [];

        $.each(adressen, function (index, value){
            //TODO: regels inschieten
            // Als checkbox is aangevinkt:
            if($('#'+value.id).prop('checked')){
                data['adressen'].push(value.id);
            }
        });

        // Toevoegen regel aan database
        $.ajax({
            method: "POST",
            url: "https://www.asbestregisseur.be/api/addtempbronnensaneerder",
            data: data
        })
        .done(function (response){
            $('#knop'+regelid).addClass('btn-success');
            $("#complexModal").modal('hide');
        })
        .fail(function (jqXHR, textStatus, errorThrown){
            alert("Er is een fout opgetreden: "+textStatus);
        });
    }

</script>
@endsection
