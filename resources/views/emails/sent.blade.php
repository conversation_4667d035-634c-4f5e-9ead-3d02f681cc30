@extends('layouts.app')

@section('content')
    <section>

        <infor-table id="mails_table" >

            <infor-table-filters >

                <infor-table-filter>
                    <label>Project</label>
                    <infor-search id="project" class="form-control" name="project" placeholder="Selecteer project" data-content="projectnummer" data-sub-content="straat, huisnummer,toevoeging" data-api="dashboard/api/search/project"></infor-search>
                </infor-table-filter>
                <infor-table-filter>
                    <label>Datum</label>
                    <input type="date" class="form-control" name="date" >
                </infor-table-filter>
                <infor-table-filter>
                    <label>Status</label>
                    <infor-select-search name="status" class="form-control" placeholder="Selecteer status" >
                        @foreach(EmailStatus::LEGENDA as $status)
                            <infor-select-option data-value="{{$status['key']}}" >{{ucfirst(strtolower($status['key']))}}</infor-select-option>
                        @endforeach
                    </infor-select-search>
                </infor-table-filter>

            </infor-table-filters>


            <infor-table-count data-single="Mail" data-multiple="Mails" ></infor-table-count>

            <infor-table-pagination>
                <infor-table-order-option data-column="created_at" >Datum</infor-table-order-option>
                <infor-table-order-option data-column="subject" >Onderwerp</infor-table-order-option>
            </infor-table-pagination>


            <infor-table-header>Datum</infor-table-header>
            <infor-table-header>Tijd</infor-table-header>
            <infor-table-header>Project</infor-table-header>
            <infor-table-header>Onderwerp</infor-table-header>
            <infor-table-header>Status</infor-table-header>
            <infor-table-header>Foutmelding</infor-table-header>
            <infor-table-header>Verzonden door</infor-table-header>



        </infor-table>

    </section>
@endsection

@section('script')
    <script>

        $(document).ready(() => {
            initTable()
        })

        //Table
        function initTable(){
            initInforTables({
                id: 'mails_table',
                api: `/dashboard/api/mails/get`,
                response_records_name: 'mails',
                search: {
                    api: 'dashboard/api/search/mail',
                    content: 'subject',
                    sub_content: `projectnummer`,
                    placeholder: `Zoeken...`,
                },
                errorHandler: handleAjaxErrors,
                fillRecord: fillRecord,
                execute: [tippyInit]
            })
        }
        function fillRecord(record){
            const { id, created_at, project, user, subject, status, status_data, error_message } = record;
            return `
                <tr onclick="openMail(${id})" class="hover-mark cursor-pointer" >
                    <td>${parseDate(created_at).date.eu}</td>
                    <td>${parseDate(created_at).time}</td>
                    <td>${project?.projectnummer || ''}</td>
                    <td>${subject || ''}</td>
                    <td> <span class="badge" style="background-color: ${status_data.color}" >${status}</span> </td>
                    <td>${(error_message || '').slice(0, 50)}${(error_message || '').length > 50 ? '...' : ''}</td>
                    <td>${user?.name || ''}</td>
                </tr>
            `
        }

        function openMail(id){
            const mail = _inforTables.get('mails_table').records.find(mail => mail.id == id);
            const { subject, content, error_message, recipients, created_at} = mail;

            const date = parseDate(created_at);
            const html = `
                <section class="" >

                  <div class="form-control flex-align rounded-pill my-1" >
                    <b class=w-px-75 >Naar:</b>
                    <div class="form-control-divider mx-2" ></div>
                    ${ recipients.map(recipient => `<span class="badge badge-secondary mx-2" >${recipient.email}</span>`).join('') }
                  </div>
                  <div class="form-control flex-align rounded-pill my-1" >
                    <b class=w-px-75 >Onderwerp:</b>
                    <div class="form-control-divider mx-2" ></div>
                    <span>${subject}</span>
                  </div>


                  ${error_message ? `<div class="form-control h-auto mt-3 text-danger" >${error_message}</div>` : ''}
                  <div class="form-control h-auto mt-3" >${content}</div>
                  <div class="text-end text-muted" > ${date.day} ${getMaanden(date.month)} ${date.time} </div>

                </section>

            `

            confirmModal({
                text: html,
                hideFooter: true,
                large: true,
            })

        }


    </script>
@endsection
