@extends('layouts.app')

@section('content')

  {{--Adres--}}
  <div class="flex-between">
    <div>
      <h1 class="m-0" >{{ $adres->straat }} {{ $adres->huisnummer }}{{ $adres->toevoeging }}</h1>
    </div>

    <div class="flex-end mx--1">
      <a class="btn hover-mark" target="_blank" href="{{ url("dashboard/adressen/qrkaart/{$adres->id}") }}">QR Kaart</a>
      <a class="btn hover-mark" target="_blank" href="{{ url("dashboard/adressen/meterkastkaart/{$adres->id}") }}">Meterkastkaart</a>

      <div class="form-control-divider mx-1" ></div>
      @foreach(getActiveProcessen() as $proces)
        <a class="btn btn-sm hover-mark" target="_blank" href="{{ url("dashboard/projecten/new/{$proces->id}?adres={$adres->id}") }}">{{$proces->naam}} @icon_plus</a>
      @endforeach

    </div>
  </div>
  <livewire:adressen.info :adres="$adres" ></livewire:adressen.info>

  {{--Documenten--}}
  @foreach(getActiveProcessen()->unique('type') as $proces)
    @if(!$adres->getInstellingenFiles($proces->type)->count()) @continue @endif

    <section class="card" data-toggle-container >
      <div class="flex-between hover-mark rounded-3 px-2 py-1 cursor-pointer" data-toggle-btn >
        <div class="card-title flex-align">
          <span>{{label('Adres')}} Documenten</span>
          <span class="badge mx-2" style="background-color: {{$proces->statusInfo('color')}}" >{{$proces->statusInfo('title')}}</span>
        </div>
        <a class="btn btn-sm" >@icon_down</a>
      </div>
      <div  data-toggle-content class="w-100 px-2" style="display: none" >
        <livewire:adressen.documenten :adres="$adres" :proces_type="$proces->type" wire:key="adres-proces-{{$proces->type}}" ></livewire:adressen.documenten>
      </div>
    </section>
  @endforeach


  {{--Complex documenten--}}
  @if($adres->complex)
    @foreach(getActiveProcessen()->unique('type') as $proces)
      @if(!$adres->complex->getInstellingenFiles($proces->type)->count()) @continue @endif

      <section class="card" data-toggle-container >
        <div class="flex-between hover-mark rounded-3 px-2 py-1 cursor-pointer" data-toggle-btn >
          <div class="card-title flex-align">
            <span>{{label('Complex')}} Documenten</span>
            <span class="badge mx-2" style="background-color: {{$proces->statusInfo('color')}}" >{{$proces->statusInfo('title')}}</span>
          </div>
          <a class="btn btn-sm" >@icon_down</a>
        </div>
        <div  data-toggle-content class="w-100 px-2" style="display: none" >
          <livewire:complexen.documenten :complex="$adres->complex" :proces_type="$proces->type" wire:key="complex-proces-{{$proces->type}}" ></livewire:complexen.documenten>
        </div>
      </section>
    @endforeach
  @endif

  {{--Projecten--}}
  <section class="card" data-toggle-container >
    <div class="flex-between hover-mark rounded-3 px-2 py-1 cursor-pointer" data-toggle-btn >
      <div class="card-title">Projecten</div>
      <a class="btn btn-sm" >@icon_down</a>
    </div>
    <div  data-toggle-content class="w-100" style="display: none" >
      <livewire:projecten.projecten-table :projecten="$projecten" ></livewire:projecten.projecten-table>
    </div>
  </section>

  {{--Bronnen--}}
  <section class="card" data-toggle-container >
    <div class="flex-between hover-mark rounded-3 px-2 py-1 cursor-pointer" data-toggle-btn >
      <div class="card-title">Bronnen</div>
      <a class="btn btn-sm" >@icon_down</a>
    </div>
    <div  data-toggle-content class="w-100" style="display: none" >
      <livewire:asbest.bronnen-table :bronnen="$bronnen" ></livewire:asbest.bronnen-table>
    </div>
  </section>

  {{--Referentiebronnen--}}
  <section class="card" data-toggle-container >
    <div class="flex-between hover-mark rounded-3 px-2 py-1 cursor-pointer" data-toggle-btn >
      <div class="card-title">Referentiebronnen</div>
      <a class="btn btn-sm" >@icon_down</a>
    </div>
    <div  data-toggle-content class="w-100" style="display: none" >
      <livewire:asbest.bronnen-table :bronnen="$adres->referentieBronnen()" :referentie="true" ></livewire:asbest.bronnen-table>
    </div>
  </section>

@endsection

@section('script')
  <script>


  </script>
@endsection
