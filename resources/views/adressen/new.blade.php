@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-12">
            @if (session('status'))
                <div class="alert alert-success" role="alert">
                    {{ session('status') }}
                </div>
            @endif
            <h1>Nieuw adres invoeren</h1>
            <p>Vul hieronder alle gegevens in</p>
            <form method="post">
                @csrf

                <div class="form-group mt-2">
                    <label for="straat">Straat</label>
                    <input name="straat" type="text" class="form-control" id="straat" placeholder="Straat" required>
                </div>
                <div class="form-group mt-2">
                    <label for="huisnummer">Huisnummer</label>
                    <input name="huisnummer" type="text" class="form-control" id="huisnummer" placeholder="Huisnummer" required>
                </div>
                <div class="form-group mt-2">
                    <label for="toevoeging">Toevoeging</label>
                    <input name="toevoeging" type="text" class="form-control" id="huisnummer" placeholder="Toevoeging">
                </div> 
                <div class="form-group mt-2">
                    <label for="complexnummer">Complexnummer</label>
                    <input name="complexnummer" type="text" class="form-control" placeholder="Complexnummer">     
                </div>
                <div class="form-group mt-2">     
                    <label for="subcomplexnummer">Subcomplexnummer</label>
                    <input name="subcomplexnummer" type="text" class="form-control" placeholder="Subcomplexnummer">
                </div>
                <div class="form-group mt-2">
                    <label for="vhenummer">VHE Nummer</label>
                    <input name="vhenummer" type="text" class="form-control" placeholder="VHE nummer">
                </div>
                <div class="form-group mt-2">
                    <label for="postcode">Postcode</label>
                    <input name="postcode" type="text" class="form-control" id="postcode" placeholder="Postcode" required>
                </div>  
                <div class="form-group mt-2">
                    <label for="plaats">Plaats</label>
                    <input name="plaats" type="text" class="form-control" id="plaats" placeholder="Plaats" required>
                </div> 
                <div class="form-group mt-2">
                    <label for="bouwjaar">Bouwjaar</label>
                    <input name="bouwjaar" type="text" class="form-control" id="bouwjaar" placeholder="Bouwjaar">
                </div> 
                <div class="form-group mt-2">
                    <label for="wijk">Wijk</label>
                    <input name="wijk" type="text" class="form-control" id="wijk" placeholder="Wijk">
                </div> 
                <div class="form-group mt-2">
                    <label for="buurt">Buurt</label>
                    <input name="buurt" type="text" class="form-control" id="buurt" placeholder="Buurt">
                </div> 
                <div class="form-group mt-2">
                    <label for="wongintype">Woningtype</label>
                    <input type="text" class="form-control" name="woningtype" id="wongintype" placeholder="Woningtype">
                </div> 
                <div class="form-group mt-2">
                    <label for="opmerkingen">Opmerkingen</label>
                    <textarea name="opmerking" class="form-control" ></textarea>
                </div> 
                <div class="form-group mt-2">
                    <button style="margin-bottom: 40px;" type="submit" class="btn btn-primary">Adres aanmaken</button>
                </div>
                </div>
            </form>

            
        </div>
    </div>
</div>

@endsection

@section('script')
<script>
    
</script>
@endsection