@extends('layouts.app')

@section('content')
    <section>
        <infor-table id="adressen_table">


            <infor-table-filters >

                <infor-table-filter>
                    <label>Status</label>
                    <infor-select-search id="active" name="active" class="form-control" placeholder="Selecteer status">
                        <infor-select-option data-value="1" data-selected >Actief</infor-select-option>
                        <infor-select-option data-value="0" >Inactief</infor-select-option>
                    </infor-select-search>
                </infor-table-filter>

                <infor-table-filter></infor-table-filter>
                <infor-table-filter></infor-table-filter>

            </infor-table-filters>

            <infor-table-count data-single="{{label('Adres')}}" data-multiple="{{label('Adressen')}}"></infor-table-count>

            <infor-table-pagination>
                <infor-table-order-option data-column="CONCAT_WS('', straat, huisnummer, toevoeging)">Adres</infor-table-order-option>
                <infor-table-order-option data-column="postcode">Postcode</infor-table-order-option>
                <infor-table-order-option data-column="plaats">Plaats</infor-table-order-option>
                <infor-table-order-option data-column="complexnummer">Complexnummer</infor-table-order-option>
                <infor-table-order-option data-column="vhenummer">VHE Nummer</infor-table-order-option>
                <infor-table-order-option data-column="bouwjaar">Bouwjaar</infor-table-order-option>
                <infor-table-order-option data-column="wijk">wijk</infor-table-order-option>
                <infor-table-order-option data-column="buurt">buurt</infor-table-order-option>
                <infor-table-order-option data-column="woningtype">woningtype</infor-table-order-option>
            </infor-table-pagination>

            <infor-table-header>Adres</infor-table-header>
            <infor-table-header>Postcode</infor-table-header>
            <infor-table-header>Plaats</infor-table-header>
            <infor-table-header>VHE Nummer</infor-table-header>
            <infor-table-header>Complexnummer</infor-table-header>
            <infor-table-header>Bouwjaar</infor-table-header>
            <infor-table-header>Wijk</infor-table-header>
            <infor-table-header>Buurt</infor-table-header>
            <infor-table-header>Woningtype</infor-table-header>


        </infor-table>
    </section>


@if(false)

@endif

@endsection

@section('script')
<script>

  pageInteractive(() => {
    initInforTables({
      id: 'adressen_table',
      api: `/dashboard/api/adressen/get`,
      response_records_name: 'adressen',
      search: {
        api: 'dashboard/api/search/adres',
        content: 'straat, huisnummer,toevoeging',
        sub_content: 'postcode, plaats',
        placeholder: `Zoeken...`,
      },
      errorHandler: handleAjaxErrors,
      fillRecord: fillRecord,
      execute: [ tippyInit ],
    })
  })

  $(document).on('click', '[data-adres]', function () {
    const id = $(this).attr('data-adres');
    window.location.href = _url(`/dashboard/adressen/adres/${id}`);
  })

  function fillRecord(record) {
    const {id, vhenummer, complexnummer, straat, huisnummer, toevoeging, postcode, plaats, bouwjaar, wijk, buurt, woningtype} = record;
    return `
                <tr class="cursor-pointer" data-adres="${id}" >
                    <td>${straat || ''} ${huisnummer || ''}${toevoeging || ''}</td>
                    <td>${postcode || ''}</td>
                    <td>${plaats || ''}</td>
                    <td>${complexnummer || ''}</td>
                    <td>${vhenummer || ''}</td>
                    <td>${bouwjaar || ''}</td>
                    <td>${wijk || ''}</td>
                    <td>${buurt || ''}</td>
                    <td>${woningtype || ''}</td>
                </tr>
            `
  }
</script>
@endsection
