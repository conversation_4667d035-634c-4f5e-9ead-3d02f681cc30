<div>

    <div wire:ignore >

        <infor-bar-selector id="template" data-trigger="true" >
            @foreach(EmailsTemplates::LEGENDA as $template)
                <infor-bar-selector-option data-value="{{$template['key']}}" >{{$template['name']}}</infor-bar-selector-option>
            @endforeach
        </infor-bar-selector>

    </div>

    <div class="card p-2">


        <div class="border rounded-5 bg-inverse-secondary">
            @if($email_template)
                @if($email_template->content)
                    <div class="p-2 hover-shadow-inset cursor-pointer" onclick="editTemplate()" >{!! $email_template->content !!}</div>
                @else
                    <div class="text-center py-5 text-muted font-size-12 hover-shadow-inset cursor-pointer" onclick="editTemplate()" >Geen inhoud @icon_edit</div>
                @endif
            @else
                <div class="text-center py-5 text-muted font-size-12" >Selecteer Template</div>
            @endif
        </div>

    </div>


</div>
<script>

    $(document).ready(() => {

        _inforBarSelector.get('template').onchange = selectTemplate

    })

    async function selectTemplate(template_key){
        const content = await @this.selectMail(template_key);
    }
    async function editTemplate(){
        const content = await @this.getContent();
        const { status, text } = await getEditorText({
            text: content
        });

        if(!status){ return; }
        await @this.setContent(text);
    }

</script>
