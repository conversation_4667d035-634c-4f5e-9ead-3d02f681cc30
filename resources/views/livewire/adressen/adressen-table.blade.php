<section class="overflow-auto" >

    <table class="table">
        <thead>
        <tr>
            <th>Adres</th>
            <th>Postcode</th>
            <th>Plaats</th>
            @foreach($files_keys as $file_key)
                <th>{{snakeToSpaceCase($file_key)}}</th>
            @endforeach
        </tr>
        </thead>
        <tbody>
        @forelse($adressen AS $adres)
            <tr onclick="redirect('/dashboard/adressen/adres/{{ $adres->id }}', 0, true)" class="hover-mark cursor-pointer" >
                <td>{{ $adres->addressLine() }}</td>
                <td>{{ $adres->postcode }}</td>
                <td>{{ $adres->plaats }}</td>
                @foreach($files_keys as $file_key)
                    <td>

                        <div class="flex-align">
                            @foreach($adres->getProjectFiles($file_key) as $file)
                                {!! $file->html() !!}
                            @endforeach
                        </div>

                    </td>
                @endforeach
            </tr>
        @empty
            <tr>
                <td colspan="100" class="text-center text-muted">Er staan voor dit adres geen project in Asbestregisseur</td>
            </tr>
        @endforelse
        </tbody>
    </table>

</section>
