<section>

    <div class="card">
        <div class="flex-between">
            <div class="card-title">{{label('Adres')}}</div>
            @if(hasPermission('adressen_edit'))
                <div class="flex-align" >
                    @if($adres->actief)
                        <a class="btn text-danger" onclick="confirmActive(false)" >Verwijderen</a>
                    @else
                        <a class="btn text-success" onclick="confirmActive(true)" >Activeren</a>
                    @endif

                    @if(!$edit)
                        <a class="btn text-primary" wire:click="edit" >Wijzigen</a>
                    @endif
                </div>
            @endif
        </div>
        <div class="row my--1">

            <div class="col-md-6 col-12 my-1">
                <label class="label">Adres</label>
                <div class="label-value">{{$adres->addressLine()}}</div>
            </div>
            <div class="col-md-6 col-12 my-1">
                <label class="label">Plaats</label>
                <div class="label-value">{{$adres->plaats}}</div>
            </div>
            <div class="col-md-6 col-12 my-1">
                <label class="label">Postcode</label>
                <div class="label-value">{{$adres->postcode}}</div>
            </div>
            @if($adres->vhenummer)
                <div class="col-md-6 col-12 my-1">
                    <label class="label">VHE nummer</label>
                    <div class="label-value">{{$adres->vhenummer}}</div>
                </div>
            @endif
            <div class="col-md-6 col-12 my-1">
                <label class="label">{{label('Complex')}}nummer</label>
                <div class="label-value">
                    @if($adres->complexnummer)
                        <a href="{{url('dashboard/complexen/'.$adres->complexnummer)}}" target="_blank" >{{$adres->complexnummer}}</a>
                    @endif
                </div>
            </div>
            @if($adres->bouwjaar)
                <div class="col-md-6 col-12 my-1">
                    <label class="label">Bouwjaar</label>
                    <div class="label-value">{{$adres->bouwjaar}}</div>
                </div>
            @endif
            @if($adres->wijk)
                <div class="col-md-6 col-12 my-1">
                    <label class="label">Wijk</label>
                    <div class="label-value">{{$adres->wijk}}</div>
                </div>
            @endif
            @if($adres->buurt)
                <div class="col-md-6 col-12 my-1">
                    <label class="label">Buurt</label>
                    <div class="label-value">{{$adres->buurt}}</div>
                </div>
            @endif
            @if($adres->woningtype)
                <div class="col-md-6 col-12 my-1">
                    <label class="label">Woningtype</label>
                    <div class="label-value">{{$adres->woningtype}}</div>
                </div>
            @endif

            <div class="col-md-6 col-12 my-1">
                <label class="label">Destructief onderzoek uitvoerende</label>
                @if($edit)
                    <input type="text" class="form-control" placeholder="Destructief onderzoek uitvoerende" wire:model="adres.destructief_onderzoek_uitvoerende" >
                @else
                    <div class="label-value">{{$adres->destructief_onderzoek_uitvoerende}}</div>
                @endif
            </div>
            <div class="col-12 my-1">
                <label class="label">Reikwijdte</label>
                @if($edit)
                    <textarea type="text" class="form-control" placeholder="reikwijdte" wire:model="adres.reikwijdte" rows="4" ></textarea>
                @else
                    <div class="label-value">{{$adres->reikwijdte}}</div>
                @endif
            </div>
            <div class="col-12 my-1">
                <label class="label">Memo geschiktheid - reikwijdte</label>
                @if($edit)
                    <textarea type="text" class="form-control" placeholder="Memo geschiktheid - reikwijdte" wire:model="adres.memo_geschiktheid" rows="4" ></textarea>
                @else
                    <div class="label-value">{!! $adres->memo_geschiktheid !!}</div>
                @endif
            </div>
            <div class="col-12 my-1">
                <label class="label">Uitsluitingen</label>
                @if($edit)
                    <textarea type="text" class="form-control" placeholder="Uitsluitingen" wire:model="adres.uitsluitingen" rows="4" ></textarea>
                @else
                    <div class="label-value">{!! $adres->uitsluitingen !!}</div>
                @endif
            </div>




        </div>
    </div>

</section>
<script>

    async function confirmActive(state){

        const { status } = await confirmModal({
            text: `Weet je zeker dat je dit adres wil <b>${state ? 'Activeren' : 'Verwijderen'}</b>?`,
            btnColor: state ? 'btn-success' : 'btn-danger'
        });
        if(!status){ return }


        @this.setActive(Number(state))
    }

</script>
