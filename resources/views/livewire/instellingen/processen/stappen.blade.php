<section>

    <div class="font-size-125" >{{ $proces->naam  }}</div>
    <div class="row">
        @foreach($stappen as $index => $stap)
            <div class="col-md-6 col-12 my-1">
                <div class="card rounded-5 overflow-auto" data-stap-container data-toggle-container >

                    <div class="flex-start">
                        <a class="btn btn-light rounded-circle m-2 m-0" wire:click="toggleStap({{$stap->id}})" > @icon_off</a>

                        @if($stap->isEnabled())
                            @if($stap->hasSubStappen() && !$stap->activeSubStappen()->count())
                                <div class="dot-glow dot-glow-warning mr-1" data-active-glow ></div>
                                <div class="font-weight-semibold text-warning m-1" data-active-text >Niet geconfigureerd</div>
                            @else
                                <div class="dot-glow dot-glow-success mr-1" data-active-glow ></div>
                                <div class="font-weight-semibold text-success m-1" data-active-text >Actief</div>
                            @endif
                        @else
                            <div class="dot-glow dot-glow-danger mr-1" data-active-glow ></div>
                            <div class="font-weight-semibold text-danger m-1" data-active-text >Inactief</div>
                        @endif

                        <div class="font-size-125 font-weight-semibold my-2 mx-4">{{$index + 1}}. {{$stap->stapnaam}}</div>

                        <div class="ml-auto nobr">
                            @if($stap->hasSubStappen())
                                <div data-toggle-btn >
                                    <a class="btn btn-light m-2"  >Substappen @icon_down</a>
                                </div>
                            @elseif($stap->isEnabled() && !$stap->subStappen()->count())
                                <a class="btn btn-light m-2" href="{{ url('dashboard/instellingen/stappen/'.$stap->id) }}"> Instellingen @icon_wrench </a>
                            @endif
                        </div>
                    </div>
                    @if($stap->subStappen()->count() && $stap->isEnabled())
                        <div data-toggle-content style="display: none" class="w-100" wire:ignore.self >
                            <div class="py-2 px-1 border-top">
                                @foreach($stap->subStappen() as $sub_index => $sub_stap)
                                    <div class="flex-start font-size-075">
                                        @if($stap->isEnabled())
                                            <a class="btn btn-sm btn-light rounded-circle m-2 m-0" wire:click="toggleStap({{$sub_stap->id}})" > @icon_off</a>
                                            <div class="dot-glow dot-glow-{{$sub_stap->isEnabled() ? 'success' : 'danger'}} m-1" data-active-glow ></div>
                                            <div class="font-weight-semibold text-{{$sub_stap->isEnabled() ? 'success' : 'danger'}} m-1" data-active-text >{{$sub_stap->isEnabled() ? 'Actief' : 'Inactief'}}</div>
                                        @endif

                                        <div class="font-size-09 font-weight-semibold my-2 mx-4">{{$index + 1}}.{{$sub_index + 1}} {{$sub_stap->stapnaam}}</div>

                                        <div class="ml-auto nobr">
                                            @if($sub_stap->isEnabled())
                                                <a class="btn btn-sm btn-light m-2" href="{{ url('dashboard/instellingen/stappen/'.$sub_stap->id) }}"> Instellingen @icon_wrench </a>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif

                </div>
            </div>

        @endforeach
    </div>


</section>
