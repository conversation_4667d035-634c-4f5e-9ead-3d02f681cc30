<div>

    <div class="font-size-125" >Processen</div>
    <div class="row">
        @foreach($processen as $proces)
            <div class="col-md-6 col-12 my-1">
                <div class="card rounded-5 overflow-auto">
                    <div class="flex-start">
                        <a class="btn btn-light rounded-circle m-2 m-0" wire:click="toggleProces({{$proces->id}})" > @icon_off</a>

                        <div class="dot-glow dot-glow-{{$proces->isEnabled() ? 'success' : 'danger'}} m-1" data-active-glow ></div>
                        <div class="font-weight-semibold text-{{$proces->isEnabled() ? 'success' : 'danger'}} m-1" data-active-text >{{$proces->isEnabled() ? 'Actief' : 'Inactief'}}</div>

                        <div class="font-size-125 flex-between font-weight-semibold my-2 mx-4 nobr">
                            <span>{{$proces->naam}}</span>
                            <span class="badge mx-2" style="background-color: {{$proces->statusInfo('color')}}" >{{$proces->statusInfo('title')}}</span>
                        </div>

                        <div class="ml-auto nobr">
                            @if($proces->isEnabled())
                                <a class="btn btn-light font-size-075 mx-1" href="{{ url('dashboard/instellingen/processen/settings/'.$proces->id) }}"> Instellingen @icon_gear </a>
                                <a class="btn btn-light font-size-075 mx-1" href="{{ url('dashboard/instellingen/processen/stappen/'.$proces->id) }}"> Stappen @icon_list </a>
                                <a class="btn btn-light font-size-075 mx-1" href="{{ url('dashboard/instellingen/processen/bestanden/'.$proces->id) }}"> Documenten @icon_file </a>
                            @endif
                        </div>
                    </div>
                </div>

            </div>
        @endforeach

    </div>

</div>
