<div>

    <div class="font-size-125" >Bestanden</div>

    <div class="row">

        {{--Adres documenten--}}
        <div class="col-md-6 col-12">
            <div class="card">
                <div class="card-title">Adres documenten</div>
                <div>
                    @foreach($adres_bestanden as $index => $file)
                        <div class="flex-align my-2" >
                            <div class="btn text-danger" wire:click="deleteFile({{$file->id}})" >@icon_close</div>
                            <input type="text" class="form-control mr-2" wire:model="adres_bestanden.{{$index}}.name" placeholder="Documentnaam" >
                            <input type="text" class="form-control" wire:model="adres_bestanden.{{$index}}.keyword" placeholder="Keyword" >
                        </div>
                    @endforeach
                </div>
                <div class="text-center" >
                    <a class="btn btn-inverse-primary" wire:click="addFile('adres')" >Toevoegen @icon_plus</a>
                </div>

            </div>
        </div>

        {{--Complex documenten--}}
        <div class="col-md-6 col-12">
            <div class="card">
                <div class="card-title">Complex documenten</div>
                <div>
                    @foreach($complex_bestanden as $index => $file)
                        <div class="flex-align my-2" >
                            <div class="btn text-danger" wire:click="deleteFile({{$file->id}})" >@icon_close</div>
                            <input type="text" class="form-control mr-2" wire:model="complex_bestanden.{{$index}}.name" placeholder="Documentnaam" >
                            <input type="text" class="form-control" wire:model="complex_bestanden.{{$index}}.keyword" placeholder="Keyword" >
                        </div>
                    @endforeach
                </div>
                <div class="text-center" >
                    <a class="btn btn-inverse-primary" wire:click="addFile('complex')" >Toevoegen @icon_plus</a>
                </div>

            </div>
        </div>

    </div>

</div>
