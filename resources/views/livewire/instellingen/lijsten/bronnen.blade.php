<section>

  <div class="card">

    <div class="flex-between my-1">

      <div class="card-title">Bron omschrijvingen</div>
      <a class="btn btn-inverse-primary" wire:click="addBronOmschrijving()" >Omschrijving @icon_plus</a>

    </div>

    <div class="overflow-auto my-2">
      <table class="table" >
        <thead>
        <tr>
          <th class="w-0" ></th>
          <th>Omschrijving</th>
          <th>Kosten Binnen Excl.</th>
          <th>Kosten Buiten Excl.</th>
          <th>Kosten Binnen Incl.</th>
          <th>Kosten Buiten Incl.</th>
          <th class="w-0" >Bronnen</th>
          <th class="w-0" >Opdrachtformulier</th>
        </tr>
        </thead>
        <tbody>
        @foreach($bronnen as $index => $bron)

          <tr>
            <td>
              <a class="btn btn-td-danger" wire:click="deleteBronOmschrijving({{$bron->id}})" >@icon_close</a>
            </td>
            <td>
              <input type="text" class="form-control" wire:model="bronnen.{{$index}}.omschrijving" placeholder="Omschrijving" >
            </td>
            <td>
              <input type="number" step="0.01" class="form-control" wire:model="bronnen.{{$index}}.kosten_binnen_excl" placeholder="0.00" >
            </td>
            <td>
              <input type="number" step="0.01" class="form-control" wire:model="bronnen.{{$index}}.kosten_buiten_excl" placeholder="0.00" >
            </td>
            <td>
              <input type="number" step="0.01" class="form-control" wire:model="bronnen.{{$index}}.kosten_binnen_incl" placeholder="0.00" >
            </td>
            <td>
              <input type="number" step="0.01" class="form-control" wire:model="bronnen.{{$index}}.kosten_buiten_incl" placeholder="0.00" >
            </td>


            <td>
              <label class="form-switch-label" >
                <input class="form-switch-custom" type="checkbox" wire:model="bronnen.{{$index}}.bronnen" >
              </label>
            </td>
            <td>
              <label class="form-switch-label" >
                <input class="form-switch-custom" type="checkbox" wire:model="bronnen.{{$index}}.opdrachtformulier" >
              </label>
            </td>
          </tr>

        @endforeach
        </tbody>
      </table>
    </div>
  </div>

</section>
