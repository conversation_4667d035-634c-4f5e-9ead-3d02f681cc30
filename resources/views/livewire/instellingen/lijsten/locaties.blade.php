<section>

  <div class="card">

    <div class="flex-between my-1">

      <div class="card-title">Locaties</div>
      <a class="btn btn-inverse-primary" wire:click="addLocatie()" >Locatie @icon_plus</a>

    </div>

    <div class="overflow-auto my-2">
      <table class="table" >
        <thead>
          <tr>
            <th class="w-0" ></th>
            <th>Locatie</th>
            <th class="w-0" >Binnen</th>
            <th class="w-0" >Bronnen</th>
            <th class="w-0" >Opdrachtformulier</th>
          </tr>
        </thead>
        <tbody>
        @foreach($locaties as $index => $locatie)

          <tr>
            <td>
              <a class="btn btn-td-danger" wire:click="deleteLocatie({{$locatie->id}})" >@icon_close</a>
            </td>
            <td>
              <input type="text" class="form-control" wire:model="locaties.{{$index}}.locatie" placeholder="Naam" >
            </td>
            <td>
              <label class="form-switch-label" >
                <input class="form-switch-custom" type="checkbox" wire:model="locaties.{{$index}}.binnen" >
              </label>
            </td>
            <td>
              <label class="form-switch-label" >
                <input class="form-switch-custom" type="checkbox" wire:model="locaties.{{$index}}.bronnen" >
              </label>
            </td>
            <td>
              <label class="form-switch-label" >
                <input class="form-switch-custom" type="checkbox" wire:model="locaties.{{$index}}.opdrachtformulier" >
              </label>
            </td>
          </tr>

        @endforeach
        </tbody>
      </table>
    </div>
  </div>

</section>
