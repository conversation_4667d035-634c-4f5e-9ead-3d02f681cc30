<section>

  <div class="card">

    <div class="flex-between my-1">

      <div class="card-title">Risicoklassen</div>
      <a class="btn btn-inverse-primary" wire:click="addRisicoklasse()" >Risicoklasse @icon_plus</a>

    </div>

    <div class="overflow-auto my-2">
      <table class="table" >
        <thead>
        <tr>
          <th class="w-0" ></th>
          <th>Klasse</th>
          <th class="w-0" >Bronnen</th>
        </tr>
        </thead>
        <tbody>
        @foreach($risicoklassen as $index => $klasse)

          <tr>
            <td>
              <a class="btn btn-td-danger" wire:click="deleteRisicoklasse({{$klasse->id}})" >@icon_close</a>
            </td>
            <td>
              <input type="text" class="form-control" wire:model="risicoklassen.{{$index}}.klasse" placeholder="Klasse" >
            </td>
            <td>
              <label class="form-switch-label" >
                <input class="form-switch-custom" type="checkbox" wire:model="risicoklassen.{{$index}}.bronnen" >
              </label>
            </td>
          </tr>

        @endforeach
        </tbody>
      </table>
    </div>
  </div>

</section>
