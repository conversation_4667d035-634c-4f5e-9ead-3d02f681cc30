<div class="col" >
    @foreach($inputs->groupBy('context') as $context => $context_inputs)

        <div class="card">
          <div class="card-title" >{{ ProjectStapContext::get($context, 'label') }}</div>
            <div class="overflow-auto">
                <table class="table table-fixed">
                    <thead>
                    <tr>
                        <th class="w-px-100" >Aan/uit</th>
                        <th class="w-px-100" >Verplicht</th>
                        <th>Naam</th>
                        <th>Input Type</th>
                        <th>Type Project</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($context_inputs as $input)
                        <tr>
                            <td>
                                <input class="form-switch-custom" type="checkbox" wire:click="toggleInput({{$input->id}})"  @if($this->isChecked($input->id)) checked @endif >
                            </td>
                            <td>
                                <input class="form-switch-custom" type="checkbox" wire:click="toggleRequired({{$input->id}})" @if($this->isRequired($input->id)) checked @endif @if(!$this->isChecked($input->id)) disabled @endif >
                            </td>
                            <td>{{ $input->titel }}</td>
                            <td>{{ $input->type }}</td>
                            <td>
                                @if($input->is_planmatig === null)
                                    <span>Planmatig / Niet Planmatig</span>
                                @elseif($input->is_planmatig)
                                    <span>Planmatig</span>
                                @else
                                    <span>Niet Planmatig</span>
                                @endif
                            </td>
                        </tr>

                        @if(isset($input['sub_inputs']) && $this->isChecked($input->id))
                          @foreach($input['sub_inputs'] as $sub_input)
                            <tr>
                              <td>
                                <input class="form-switch-custom" type="checkbox"  wire:click="toggleInput({{$sub_input->id}})"  @if($this->isChecked($sub_input->id)) checked @endif >
                              </td>
                              <td>
                                <input class="form-switch-custom" type="checkbox"  wire:click="toggleRequired({{$sub_input->id}})" @if($this->isRequired($sub_input->id)) checked @endif @if(!$this->isChecked($sub_input->id)) disabled @endif >
                              </td>
                              <td>{{ $sub_input->titel }}</td>
                              <td>{{ $sub_input->type }}</td>
                              <td></td>
                            </tr>
                          @endforeach
                        @endif
                    @endforeach

                    </tbody>
                </table>
            </div>
        </div>
    @endforeach

</div>
