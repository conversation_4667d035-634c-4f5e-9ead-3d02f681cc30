<section class="col-12" >

    <div data-mails-container class="my--2" >
      @foreach($mails as $i => $mail)

        <div class="card" >

          <div class="card-title">Mail</div>

          <div class=" position-absolute end-0 top-0 m-1" >
            @if(!$mail->isValid()) <span class="tippy badge badge-warning mx-1" data-tippy-content="Vul alle verplichte velden in!" >Concept</span> @endif
            <a class="btn text-danger" onclick="deleteEmail({{$mail->id}})" >@icon_trash</a>
          </div>


          <div class="row mt--2">

            <div class="col-md-4 col-12 my-2">
              <label>Onderwerp*</label>
              <input class="form-control" data-mail-input name="onderwerp" placeholder="Onderwerp" wire:model="mails.{{$i}}.onderwerp">
            </div>
            <div class="col-md-4 col-12 my-2" wire:ignore >
              <label>Mailontvangers*</label>
              <infor-select-multiple data-livewire="setOntvangers" data-livewire-params=":id={{$mail->id}}" name="ontvangers" class="form-control" placeholder="Selecteer ontvangers">
                @foreach(getRoles() as $role )
                  <infor-select-option data-name="{{$role->name}}" data-value="{{$role->id}}" data-mail-input @if($mail->isRoleSelected($role->id)) data-selected @endif >
                    @if($role->login)
                      <span class="font-weight-bold text-primary mr-1" >Gebruiker</span>
                    @else
                      <span class="font-weight-bold text-warning mr-1" >Contact</span>
                    @endif
                    <span>{{$role->name}}</span>
                  </infor-select-option>
                @endforeach
              </infor-select-multiple>
            </div>
            <div class="col-md-4 col-12 my-2" >
              <label>Trigger</label>
              <select class="form-select" wire:model="mails.{{$i}}.trigger" >
                <option value="STEP_CONFIRM" >Stap bevestiging</option>
                <option value="STEP_REJECT" >Stap afwijziging</option>
              </select>
            </div>

            <div class="col-12 my-2">
              <div class="border rounded bg-inverse-secondary cursor-pointer p-2 hover-shadow-inset" onclick="editMail({{$mail->id}})" > {!! $mail->mail ?? '<div class="p-1 font-size-11 text-center opacity-50" >Geen inhoud* @icon_edit </div>' !!} </div>
              <textarea data-mail-input name="mail" class="d-none" wire:model="mails.{{$i}}.mail" ></textarea>
            </div>
          </div>
        </div>

      @endforeach
    </div>
    <div class="mt-4 text-center">
      <a class="btn btn-inverse-primary text-white rounded-pill" wire:click="addMail" >Mail toevoegen @icon_plus</a>
    </div>

</section>
<script>

  $(document).ready(() => {
    Livewire.on('addMail', initInforSelectMultiple);
  })

  function deleteEmail(id){
    confirmModal({
      text: 'Weet u zeker dat u deze e-mail wilt verwijderen?',
      btnColor: 'btn-danger',
    })
      .then(response => {
        if(!response.status){ return; }
        @this.deleteMail(id);
      })
  }
  async function editMail(id){
    const content = await @this.getMailContent(id);

    getEditorText({text: content})
      .then(response => {
        if (!response.status) { return; }
        @this.setMailContent(id, response.text || null);
      })
  }

</script>
