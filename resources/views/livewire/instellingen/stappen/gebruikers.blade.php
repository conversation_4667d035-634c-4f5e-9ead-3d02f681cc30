<section class="col-12" >
    <div class="row">

        <div class="col-md-4 col-12 my-2">
            <div class="card">
                <div class="card-title">Gebruikers</div>
                <div class="mb-2"  wire:ignore >
                    <label>Stap invulbaar door</label>
                    <infor-select-multiple name="gebruikers" class="form-control" placeholder="Selecteer status" data-livewire="setInvullen">
                        @foreach($available_gebruikers as $role)
                            <infor-select-option @if($role->_invullen) data-selected @endif data-name="{{$role->name}}" data-value="{{$role->id}}" >{{$role->name}}</infor-select-option>
                        @endforeach
                    </infor-select-multiple>
                </div>
                <div class="my-2"  wire:ignore >
                    <label>Stap inzichtbaar door</label>
                    <infor-select-multiple class="form-control" placeholder="Selecteer status" data-livewire="setInzien" >
                        @foreach($gebruikers as $role)
                            <infor-select-option @if($role->_inzien) data-selected @endif data-name="{{$role->name}}" data-value="{{$role->id}}" >{{$role->name}}</infor-select-option>
                        @endforeach
                    </infor-select-multiple>
                </div>
                <div class="my-2"  wire:ignore >
                    <label>Gebruikers toevoegen</label>
                    <infor-select-multiple class="form-control" placeholder="Selecteer status" data-livewire="setToevoegen">
                        @foreach($gebruikers as $role)
                            <infor-select-option @if($role->_toevoegen) data-selected @endif data-name="{{$role->name}}" data-value="{{$role->id}}" >{{$role->name}}</infor-select-option>
                        @endforeach
                    </infor-select-multiple>
                </div>
                <div class="my-2"  wire:ignore >
                    <label>Contacten toevoegen</label>
                    <infor-select-multiple class="form-control" placeholder="Selecteer status" data-livewire="setContact">
                        @foreach($contacten as $role)
                            <infor-select-option @if($role->_contact) data-selected @endif data-name="{{$role->name}}" data-value="{{$role->id}}" >{{$role->name}}</infor-select-option>
                        @endforeach
                    </infor-select-multiple>
                </div>
            </div>

            @if($this->gebruikers->where('_toevoegen', true)->count())
                <div class="card">
                    <div class="card-title">Gebruikers toevoegen modifiers</div>
                    @foreach($gebruikers->where('_toevoegen', true) as $gebruikers_role)
                        <div data-toggle-container class="form-switch-label py-0 px-1" >
                            <div class="flex-between" data-toggle-btn >
                                <span>{{$gebruikers_role->name}}</span>
                                <a class="btn" >@icon_down</a>
                            </div>
                            <div data-toggle-content style="display: none" class="w-100" wire:ignore.self >
                                @foreach(getRoles(1) as $role)
                                    <label class="form-switch-label px-1" >
                                        <input type="checkbox" class="form-switch mr-1"
                                               wire:change="toggleModifier({{$gebruikers_role->id}}, 'INCLUDE_PROJECT_USER_BY_ROLE', {{$role->id}})"
                                               @if($this->hasModifier($gebruikers_role->id, 'INCLUDE_PROJECT_USER_BY_ROLE', $role->id)) checked @endif
                                        >
                                        <span>Project {{$role->name}} toevoegen</span>
                                    </label>
                                @endforeach
                            </div>
                        </div>
                    @endforeach
                </div>
            @endif

        </div>

        <div class="col-md-8 col-12 my-2">
            <div class="card font-size-075">
              <div class="card-title">Gebruikers overzicht</div>

                    @foreach($tables as $table)


                        <div data-toggle-container class="rounded-5 border overflow-hidden my-2" >
                          <div class="flex-between bg-light p-2 cursor-pointer" data-toggle-btn >
                            <span class="font-size-11" >{{$table['name']}}</span>
                            <a class="btn" > @if($loop->last) @icon_up @else @icon_down @endif </a>
                          </div>
                          <div data-toggle-content style="display: {{$loop->last ? 'block' : 'none'}}" class="w-100" >

                            <div class="overflow-auto p-2">
                              <table class="table table-fixed table-borderless" >
                                <tbody>
                                <tr class="border-bottom" >
                                  <th>Invullen</th>
                                  <th>Inzien</th>
                                  <th>Toevoegen</th>
                                  <th>Contacten</th>
                                </tr>
                                <tr>
                                  @foreach($table['tasks'] as $roles)
                                    <td>
                                      @foreach($roles as $role)
                                        <div class="my-1">{{$role->name}}</div>
                                      @endforeach
                                    </td>
                                  @endforeach
                                </tr>
                                </tbody>
                              </table>
                            </div>

                          </div>
                        </div>
                    @endforeach

            </div>
        </div>

    </div>

    <script>
      $(document).on('click', '[data-role-stap]', function(){
        const proces_id = @js($proces->id);
        const stap_id = $(this).attr('data-role-stap');

        loader();
        redirect(`/dashboard/instellingen/stap/${proces_id}/${stap_id}`);
      });
    </script>

</section>
