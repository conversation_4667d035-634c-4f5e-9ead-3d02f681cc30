<div>

    <div wire:ignore >

        <infor-bar-selector id="blade" data-trigger="true" >
            @foreach($blades as $name => $path)
                <infor-bar-selector-option data-value="{{$path}}" >{{$name}}</infor-bar-selector-option>
            @endforeach
        </infor-bar-selector>

    </div>

    <section class="my-2 w-100">

        <div data-blades >
            @if($blade)
                @include("instellingen.blades.$blade", array_merge($blades_data, ['livewire' => $this]))
            @endif
        </div>

    </section>

<script>

    $(document).ready(()=>{
        _inforBarSelector.get('blade').onchange = blade => { console.log('trigger'); Livewire.emit('setBlade', blade); }
    })

</script>

</div>
