<div>

    <div class="card">
        <div class="flex-between my-1">
            <div class="card-title">Redenen</div>
            <a class="btn btn-inverse-primary" wire:click="addReden()" >Reden @icon_plus</a>
        </div>

        <div class="overflow-auto">
            <table class="table" >
                <thead>
                    <tr>
                        <th></th>
                        <th>Onderdeel</th>
                        <th>Reden</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($redenen as $i => $reden)

                        @if($loop->first || $reden->onderdeel != $this->redenen[$i - 1]->onderdeel)
                            <tr class="bg-inverse-secondary" >
                                <td colspan="3" class="py-1" >
                                    <div class="flex-between">
                                        <b>{{$reden->onderdeel}}</b>
                                        <a class="btn btn-td-primary" wire:click="addReden('{{$reden->onderdeel}}')">@icon_plus</a>
                                    </div>
                                </td>
                            </tr>
                        @endif

                        <tr>
                            <td class="w-0" >
                                <a class="btn btn-td-danger" wire:click="deleteReden({{$reden->id}})">@icon_close</a>
                            </td>
                            <td>
{{--                                <input type="text" class="form-control" placeholder="Onderdeel" wire:model="redenen.{{$i}}.onderdeel" >--}}
                                <infor-select-edit class="form-control" placeholder="Onderdeel" data-livewire="setOnderdeel" data-livewire-params=":id={{$reden->id}}" data-value="{{$reden->onderdeel}}" >
                                    @foreach($this->getOnderdelen() as $onderdeel)
                                        <infor-select-option data-value="{{$onderdeel}}" >{{$onderdeel}}</infor-select-option>
                                    @endforeach
                                </infor-select-edit>
                            </td>
                            <td>
                                <input type="text" class="form-control" placeholder="Reden" wire:model="redenen.{{$i}}.reden" >
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>


    </div>

</div>
