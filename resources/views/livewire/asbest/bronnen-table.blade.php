<div class="overflow-auto" >

    <table class="table">
        <thead>
        <tr>
            <th>Status</th>
            <th>Projectnummer</th>
            <th>Bronnummer</th>
            <th>Locatie</th>
            <th>Omschrijving</th>
            <th>Risicoklasse</th>
            <th>Z.A.V.</th>
            <th>Aantal</th>
            <th>Eenheid</th>
            <th>Asbesthoudend</th>
            <th>Saneren (advies)</th>
            <th>Gesaneerd</th>
            <th>Aangemaakt op</th>
            <th>Inventarisatierapporten</th>
        </tr>
        </thead>
        <tbody>
        @forelse($bronnen->groupBy('adres_id') AS $adres_bronnen)

            @if(!$single_adres)
                <tr>
                    <th colspan="100" class="bg-inverse-secondary" >{{$adres_bronnen->first()->adres->addressLine()}}</th>
                </tr>
            @endif

            @foreach($adres_bronnen as $bron)
                <tr>
                    <td>
                        @if($referentie)
                            <span class="badge w-100" style="background-color: {{$bron->referentieStatusInfo('color')}}" data-tippy-content="{{$bron->referentieStatusInfo('description')}}" >{{$bron->referentieStatusInfo('title')}}</span>
                        @else
                            <span class="badge w-100" style="background-color: {{$bron->statusInfo('color')}}" data-tippy-content="{{$bron->statusInfo('description')}}" >{{$bron->statusInfo('title')}}</span>
                        @endif
                    </td>
                    <td>
                        <a target="_blank" href="{{url('/dashboard/projecten/project/'.$bron->project_id)}}" >{{$bron->project->projectnummer ?? ''}}</a>
                    </td>
                    <td>{{ $bron->bronnummer }}</td>
                    <td>{{ $bron->bronlocatie->locatie ?? '' }}</td>
                    <td>{{ $bron->bronomschrijving->omschrijving ?? '' }}</td>
                    <td>{{ $bron->risicoklasse->klasse ?? '' }}</td>
                    <td>{{ $bron->zav }}</td>
                    <td>{{ $bron->aantal }}</td>
                    <td>{{ $bron->eenheid }}</td>
                    <td>{{ $bron->asbesthoudend ? 'Ja' : 'Nee' }}</td>
                    <td>{{ $bron->saneeradvies }}</td>
                    <td>{{ $bron->gesaneerd ? 'Ja' : 'Nee' }}</td>
                    <td>{{ displayDate($bron->created_at) }}</td>
                    <td class="text-end" >
                        @forelse($bron->inventarisatieRapporten() as $file)
                            <a href="{{_file($file->guid)}}" target="_blank" data-tippy-content="Geldig tot {{displayDate($file->rapport_valid_until)}}" class="btn btn-sm {{$file->rapport_valid ? 'btn-inverse-success' : 'btn-inverse-danger'}}" >{{$file->name}}</a>
                        @empty
                            <span class="text-muted" >Geen inventarisatierapporten gevonden</span>
                        @endforelse
                    </td>
                </tr>
            @endforeach

        @empty
            <tr>
                <td colspan="100" class="text-center text-muted">Er staan voor dit adres geen {{$referentie ? 'referentie' : ''}}bronnen in Asbestregisseur</td>
            </tr>
        @endforelse
        </tbody>
    </table>

</div>
