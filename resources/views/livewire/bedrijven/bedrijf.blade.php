


<div class="row">

    @if($logo || $bedrijf->logo_guid)
        <div class="col-12 mt-2 text-center">
            <img height="100" src="{{$logo ? $logo->temporaryUrl() : _file($bedrijf->logo_guid)}}" alt="Logo">
        </div>
    @endif

    <div class="col-md-6 col-12 my-2">
        <label>Naam*</label>
        <input type="text" class="form-control" placeholder="Naam" wire:model="bedrijf.naam" required >
    </div>
    <div class="col-md-6 col-12 my-2">
        <label>Email</label>
        <input type="text" class="form-control" placeholder="Email" wire:model="bedrijf.email">
    </div>
    <div class="col-md-6 col-12 my-2">
        <label>Telefoonnummer</label>
        <input type="text" class="form-control" placeholder="Telefoonnummer" wire:model="bedrijf.telefoon">
    </div>
    <div class="col-md-6 col-12 my-2">
        <label>Logo</label>
        <input type="file" class="form-control" wire:model.lazy="logo" wire:loading.attr="disabled" accept="image/*" >
    </div>

    <div class="my-2 text-center" >
        <a class="btn btn-success" wire:click="store" wire:target="store" wire:loading.remove  >Opslaan</a>
        <div wire:loading wire:target="store" > @spinner_success </div>
        @error('*') <div class="text-danger my-1"> {{ $message }} </div> @enderror
    </div>

</div>

<script>
    $(document).ready(() => {
      Livewire.on('stored', () => {
        notification('Bedrijf succesvol opgeslagen!', 'success');
        redirect('/dashboard/bedrijven', 350);
      })
    })
</script>
