<div class="row" >

    <div class="col-12 col-md-6 my-2">
        <label>Naam*</label>
        <input type="text" class="form-control" placeholder="Naam" wire:model="contact.name" >
    </div>
    <div class="col-12 col-md-6 my-2" wire:ignore>
        <label>Rol*</label>
        <infor-select-search id="role" class="form-control" placeholder="Selecteer rol" data-livewire="setRole" >
            @foreach(getRoles(0) as $role)
                <infor-select-option @if($role->id == ($contact->role->id ?? null)) data-selected @endif data-name="{{$role->name}}" data-value="{{$role->id}}">{{$role->name}}</infor-select-option>
            @endforeach
        </infor-select-search>
    </div>
    <div class="col-12 col-md-6 my-2">
        <label>Email*</label>
        <input type="text" class="form-control" placeholder="Email" wire:model="contact.email" >
    </div>
    <div class="col-12 col-md-6 my-2">
        <label>Telefoon</label>
        <input type="text" class="form-control" placeholder="Naam" wire:model="contact.telefoon" >
    </div>
    <div class="col-12 col-md-6 my-2">
        <label>Partij</label>
        <input type="text" class="form-control" placeholder="Partij" wire:model="contact.partijnaam" >
    </div>

    <div class="text-center my-2">
        <a class="btn btn-success" wire:click="store" wire:loading.remove wire:target="store">Opslaan</a>
        <div wire:loading wire:target="store"> @spinner_success</div>
        @error('*')<div class="text-danger my-1"> {{ $message }} </div> @enderror
    </div>

</div>

<script>

    $(document).ready(() => {

      Livewire.on('stored', () => {
        notification('Contact succesvol opgeslagen!', 'success');
        redirect('/dashboard/contacten', 250);
      })

    })

</script>
