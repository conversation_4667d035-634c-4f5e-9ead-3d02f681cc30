<div>


    {{--Global overview--}}
    <div>

        <div class="card-title w-100 text-center text-black">Globaal overzicht</div>

        <div class="flex-between align-items-start">

            {{--Total--}}
            @if(hasPermission('asbest_prices'))
                <div class="d-flex mx--2" >

                    <div class="card rounded-5 mx-2" wire:ignore >
                        <div class="card-title">Complexen Totaal Excl.</div>
                        <div class="font-size-125" >€ <span data-complexen-total >0,00</span></div>
                    </div>

                </div>
            @else
                <div></div>
            @endif

            {{--Actions--}}
            <div class="d-flex align-items-end mx--2" >

                {{--Export--}}
                <div class="card rounded-5 mx-2" wire:loading.class="wire-loading" >
                    <div class="card-title font-size-1" >Export</div>
                    <div>
                        <div class="form-switch-label border rounded-5 px-3" wire:click="exportExcel()" >Excel @icon_download</div>
                    </div>
                </div>

                {{--Indicatie settings--}}
                <div class="card rounded-5 mx-2" wire:loading.class="wire-loading" >
                    <div class="card-title font-size-1">Indicatiebronnen</div>
                    <div>
                        <label class="form-switch-label" >
                            <input @if(!$loaded) disabled @endif wire:model="global_settings.indicatie_show" type="checkbox" class="form-switch-custom mr-2" >
                            <span>Indicatiebronnen weergeven</span>
                        </label>
                    </div>
                    @if($global_settings['indicatie_show'])
                        <div>
                            <label>Percentage geïnventariseerde woningen</label>
                            <div class="flex-between">
                                <input type="range" step="10" wire:model.debounce.250ms="global_settings.indicatie_percentage" class="form-range" min="0" max="100" list="test" data-range-indicators >
                                <input type="number" step="5" wire:model.debounce.250ms="global_settings.indicatie_percentage" class="form-control-plaintext w-px-50 text-end" >
                                <span>%</span>
                            </div>
                        </div>
                    @endif
                </div>

                {{--Cost settings--}}
                @if(hasPermission('asbest_prices'))
                    <div class="card rounded-5 mx-2" wire:loading.class="wire-loading" >
                        <div class="card-title font-size-1">Prijzen</div>
                        <div>
                            <label class="form-switch-label" >
                                <input wire:model="global_settings.cost_incl_referentie" type="checkbox" class="form-switch-custom mr-2" >
                                <span>Prijzen inclusief referentiebronnen</span>
                            </label>
                        </div>
                        @if($global_settings['indicatie_show'])
                            <div>
                                <label class="form-switch-label" >
                                    <input wire:model="global_settings.cost_incl_indicatie" type="checkbox" class="form-switch-custom mr-2" >
                                    <span>Prijzen inclusief indicatiebronnen</span>
                                </label>
                            </div>
                        @endif
                    </div>
                @endif

            </div>

        </div>

        @if(hasPermission('asbest_prices'))
            <div class="my-2 card rounded-5" wire:ignore >
                <div class="card-title flex-between">
                    <span>Uitvoerkosten per jaar</span>
                    <span data-uitvoerkosten-loader ></span>
                </div>
                <div class="chart-container" >
                    <canvas class="uitvoer-chart" ></canvas>
                    @chart_loader
                </div>
            </div>
        @endif

        <hr class="opacity-1" >

    </div>

    <section wire:init="initLoaded" >
        @if($loaded)
            @foreach($complexen as $complex)
                <div class="my-2" >


                    <div class="flex-between">
                        <div>
                            <div class="card-title">{{$complex->complexnummer}}, {{$complex->plaats}}</div>
                            <div class="card-subtitle">Uitvoerjaar: {{$complex->uitvoerjaar ?? 'Onbekend'}}</div>
                        </div>
                        @if(!$loaded_matrixen[$complex->id])
                            <a class="btn btn-inverse-primary nobr" wire:click="loadMatrix({{$complex->id}})" wire:loading.class="wire-loading" >Matrix inladen</a>
                        @endif
                    </div>

                    <livewire:complexen.asbest-bronnen-matrix :complex="$complex" wire:key="complex-{{$complex->id}}" :show_matrix="false" :show_legenda="false" :show_total="true" :show_export="false" ></livewire:complexen.asbest-bronnen-matrix>

                    <hr>

                </div>
            @endforeach
        @else
            <div class="text-center my-2" >@dots_loader</div>
        @endif
    </section>

    <script>

        $(document).ready(() => {
            Livewire.on('load-matrix', loadMatrix);
            Livewire.on('set-matrix-settings', settings => {
                setMatrixSettings(settings);
                initCharts();
            });
            Livewire.on('complexen-loaded', () => {
                calculateTotal();
                initCharts();
            });
        })

        //Matrixen
        function loadMatrix(complex_id){
            internalMessageSend(`set-values-${complex_id}`, {
                show_matrix: true,
                show_total: true,
                show_settings: true,
            });

            $(this).remove();
        }
        async function setMatrixSettings(settings){
            livewireContentBlockerSet()

            const complexen_ids = @js($complexen->pluck('id'));
            const values = {}

            for(const i in settings){
                values[`settings.${i}`] = settings[i];
            }
            for(const i in complexen_ids){
                const id = complexen_ids[i];
                const percentage = (Number(i) / complexen_ids.length * 100).toFixed(1);

                livewireContentBlockerText(`Laden: ${percentage}%`);
                await internalMessageSend(`set-values-${id}`, values);
            }

            calculateTotal();
            livewireContentBlockerRemove()
        }

        //Total
        function calculateTotal(){
            let total = 0;

            $('[data-complex-total]').each(function(){ total += Number(this.value); })

            $('[data-complexen-total]').html(toPrice(total));
        }

        //Charts
        function initCharts(){
            uitvoerChart();
        }
        async function uitvoerChart(){
            const settings = await @this.global_settings;
            const { uitvoerkosten } = await ajax('/dashboard/api/complexen/asbest/get/uitvoerkosten', {settings: settings});

            const labels = [];
            const data = [];

            for(const year in uitvoerkosten){
                labels.push(year);
                data.push(uitvoerkosten[year].excl);
            }

            const datasets = [
                {
                    label: 'Uitvoerkosten',
                    data: data,
                    borderColor: '#F4AB33',
                    backgroundColor: '#F4AB33BF',
                }
            ];

            initBarChart({
                id: '.uitvoer-chart',
                labels: labels,
                datasets: datasets,
            });
        }

    </script>

</div>
