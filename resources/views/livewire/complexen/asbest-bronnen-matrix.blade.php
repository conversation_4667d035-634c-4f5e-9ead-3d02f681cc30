<div class="my-2" >

    {{--Legenda & Options--}}
    <div class="flex-between align-items-end flex-wrap" >

        {{--Legenda's--}}
        @if($show_legenda)
            <div >
                {{--Adressen legenda--}}
                <div class="my-2" >
                    <b>{{label('Adressen')}} Legenda</b>
                    @foreach(AdresStatus::LEGENDA as $legenda)
                        <div class="flex-start mb-1" >
                            <div style="background-color: {{$legenda['color']}};" class="min-h-px-25 min-w-50 rounded" ></div>
                            <div class="px-2" >{{$legenda['description']}}</div>
                        </div>
                    @endforeach
                </div>
                {{--Bronnen legenda--}}
                <div class="my-2" >
                    <b>Asbest Legenda</b>
                    @foreach(AsbestBronStatus::LEGENDA as $legenda)
                        <div class="flex-start mb-1" >
                            <div style="background-color: {{$legenda['color']}};" class="min-h-px-25 min-w-50 rounded" ></div>
                            <div class="px-2" >{{$legenda['description']}}</div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        {{--Total--}}
        @if(hasPermission('asbest_prices') && $show_total)
            <div class="card rounded-5">
                <div class="card-title" >Totaal Excl.</div>
                <div class="font-size-125" >€ {{ $total }}</div>
            </div>
        @endif


        {{--Actions--}}
        @if($show_settings || $show_export)
            <div class="d-flex align-items-end mx--2" >

                @if($show_export)
                    {{--Export--}}
                    <div class="card rounded-5 mx-2" wire:loading.class="wire-loading" >
                        <div class="card-title font-size-1" >Export</div>
                        <div>
                            <div class="form-switch-label border rounded-5 px-3" wire:click="exportExcel()" >Excel @icon_download</div>
                        </div>
                    </div>
                @endif

                @if($show_settings)
                    {{--Indicatie settings--}}
                    <div class="card rounded-5 mx-2" wire:loading.class="wire-loading" >
                        <div class="card-title font-size-1">Indicatiebronnen</div>
                        <div>
                            <label class="form-switch-label" >
                                <input wire:model="settings.indicatie_show" type="checkbox" class="form-switch-custom mr-2" >
                                <span>Indicatiebronnen weergeven</span>
                            </label>
                        </div>
                        @if($settings['indicatie_show'])
                            <div>
                                <label>Percentage geïnventariseerde woningen</label>
                                <div class="flex-between">
                                    <input type="range" step="10" class="form-range" wire:model="settings.indicatie_percentage" min="0" max="100" list="test" data-range-indicators >
                                    <input type="number" step="5" wire:model.debounce.10ms="settings.indicatie_percentage" class="form-control-plaintext w-px-50 text-end" >
                                    <span>%</span>
                                </div>
                            </div>
                        @endif
                    </div>

                    {{--Cost settings--}}
                    @if(hasPermission('asbest_prices'))
                        <div class="card rounded-5 mx-2" wire:loading.class="wire-loading" >
                            <div class="card-title font-size-1">Prijzen</div>
                            <div>
                                <label class="form-switch-label" >
                                    <input wire:model="settings.cost_incl_referentie" type="checkbox" class="form-switch-custom mr-2" >
                                    <span>Prijzen inclusief referentiebronnen</span>
                                </label>
                            </div>
                            @if($settings['indicatie_show'])
                                <div>
                                    <label class="form-switch-label" >
                                        <input wire:model="settings.cost_incl_indicatie" type="checkbox" class="form-switch-custom mr-2" >
                                        <span>Prijzen inclusief indicatiebronnen</span>
                                    </label>
                                </div>
                            @endif
                        </div>
                    @endif
                @endif

            </div>
        @endif

    </div>

    @if($show_matrix)
        {{--Matrix--}}
        <section class="card" >
            <div class="card-title" >Asbest Bronnen Matrix</div>
            <div class="overflow-auto" >
                <table class="table" >

                    <tr class="font-size-075" >
                        <th></th>
                        @foreach($bronnen as $bron)
                            <th>
                                <div class="flex-between" >
                                    <span class="mr-2" >{{$bron->locatieOmschrijving()}}</span>
                                    @if(hasPermission('asbest_prices')) <span class="text-muted" >€ {{$bron->cost()->get(PriceType::EXCL)}}</span> @endif
                                </div>
                            </th>
                        @endforeach
                        @if(hasPermission('asbest_prices'))
                            <th class="w-0" >Totaal</th>
                        @endif
                    </tr>
                    @foreach($complex->adressen() as $adres)
                        <tr class="text-white" >

                            {{--Address--}}
                            <td class="nobr w-0 p-0 border-2 border-white p-0">
                                <div class="rounded h-100 p-1 hover-shadow-inset cursor-pointer" onclick="redirect('/dashboard/adressen/adres/{{$adres->id}}', 0, true)" data-tippy-content="{{AdresStatus::description($adres->inventarisatieStatus())}}" style="background-color: {{ AdresStatus::color( $adres->inventarisatieStatus())  }}" >
                                    <span>{{$adres->addressLine()}}</span>
                                </div>
                            </td>

                            {{--Bronnen--}}
                            @foreach($bronnen as $bron)
                                @php $adres_bronnen = $complex->adresAsbestBronnen($adres->id, $bron->omschrijving_id, $bron->locatie_id); @endphp
                                @php $adres_referentie_bronnen = $complex->adresAsbestReferentieBronnen($adres->id, $bron->omschrijving_id, $bron->locatie_id) @endphp

                                <td class="border-2 p-0 border-white" >

                                    {{--Has bronnen--}}
                                    @if($adres_bronnen->count())
                                        @foreach($adres_bronnen as $adres_bron)
                                            <div class="rounded p-1 hover-shadow-inset cursor-pointer @if(!$loop->last) border-bottom border-white @endif" wire:click="displayBron({{$adres_bron->id}}, false)" data-tippy-content="{{AsbestBronStatus::description($adres_bron->status())}}" style="background-color: {{AsbestBronStatus::color($adres_bron->status())}}; height: {{ 100 / $adres_bronnen->count() }}%" >
                                                <span>{{$adres_bron->aantal}} {{$adres_bron->eenheid}}</span>
                                            </div>
                                        @endforeach

                                        {{--Has referentie bronnen--}}
                                    @elseif($adres_referentie_bronnen->count())
                                        @foreach($adres_referentie_bronnen as $referentie_bron)
                                            <div class="rounded p-1 hover-shadow-inset cursor-pointer @if(!$loop->last) border-bottom border-white @endif" wire:click="displayBron({{$referentie_bron->id}}, true)" data-tippy-content="{{AsbestBronStatus::description($referentie_bron->referentieStatus())}}" style="background-color: {{AsbestBronStatus::color($referentie_bron->referentieStatus())}}; height: {{ 100 / $adres_referentie_bronnen->count() }}%" >
                                                <span>{{$referentie_bron->aantal}} {{$referentie_bron->eenheid}}</span>
                                            </div>
                                        @endforeach

                                        {{--Indicatie bron--}}
                                    @elseif($settings['indicatie_show'] && $complex->isIndicatieBron($bron->omschrijving_id, $bron->locatie_id, $settings['indicatie_percentage']))
                                        <div class="rounded p-1 hover-shadow-inset h-100"  data-tippy-content="{{AsbestBronStatus::description('INDICATIE')}}" style="background-color: {{AsbestBronStatus::color('INDICATIE')}};" >
                                            <span>Indicatie</span>
                                        </div>

                                        {{--No bronnen--}}
                                    @else
                                        <div class="rounded h-100 p-1" data-tippy-content="{{AsbestBronStatus::description('NIET_AANGETROFFEN')}}" style="background-color: {{AsbestBronStatus::color('NIET_AANGETROFFEN')}}" >
                                        </div>
                                    @endif

                                </td>
                            @endforeach


                            {{--Total--}}
                            @if(hasPermission('asbest_prices'))
                                <td class="nobr w-0 p-0 border-2 border-white" >
                                    <div class="flex-between rounded bg-dark h-100 p-1" >
                                        <span class="mr-2" >€</span>
                                        <span>{{$adres->asbestBronnenTotalCost($settings)->get(PriceType::EXCL)}}</span>
                                    </div>
                                </td>
                            @endif

                        </tr>
                    @endforeach

                {{--Grand total--}}
                @if(hasPermission('asbest_prices'))
                    <tr class="text-white" >
                        <td class="nobr w-0 p-0 border-2 border-white" colspan="{{$bronnen->count() + 1}}" >
                            <div class="rounded bg-dark h-100 p-1 text-end" >
                                Totaal:
                            </div>
                        </td>
                        <td class="nobr w-0 p-0 border-2 border-white" >
                            <div class="flex-between rounded bg-dark h-100 p-1" >
                                <span class="mr-2" >€</span>
                                <span>{{$total}}</span>
                            </div>
                        </td>
                    </tr>
                @endif

                </table>
            </div>

            {{--Bron Preview--}}
            @if($bron_preview)
                <section class="my-2" >
                    <livewire:asbest.bronnen-table :bron="$bron_preview" :referentie="$bron_preview_reference" wire:key="{{randomString(20)}}" ></livewire:asbest.bronnen-table>
                </section>
            @endif

        </section>
    @endif

    {{--Hidden values--}}
    <div class="d-none" >
        <input type="text" data-complex-total="{{$complex->id}}" value="{{$total_plain}}" >
    </div>

    <script>

        $(document).ready(() => {

            internalMessageListen(`set-values-@js($complex->id)`, async function(data){
                await @this.setValues(data);
            });

        })

    </script>

</div>
