<div>
    @foreach($instellingen_files as $file)
        <div>
            <label class="label" >{{ $file->name }}</label>
            <div class="d-flex" >
                @foreach($complex->getFiles($proces_type, $file->keyword) as $complex_file)
                    {!! $complex_file->html() !!}
                @endforeach
                @if(hasPermission('complexen_edit'))
                    <label class="cursor-pointer" data-tippy-content="Bestand uploaden" >
                        <img class="hover-translatey-up" height="40" src="{{url('dashboard/img/files/plus.png')}}">
                        <input type="file" multiple class="d-none" wire:model="files.{{$file->keyword}}" >
                    </label>
                @endif
            </div>
        </div>
    @endforeach
    @error('files.*') <div class="text-danger">{{$message}}</div> @enderror
</div>
