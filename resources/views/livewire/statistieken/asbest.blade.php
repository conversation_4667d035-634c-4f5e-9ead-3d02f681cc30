<div>

    <div class="card rounded-pill">
        <div class="row">

            <div class="col">
                <div class="d-flex form-control rounded-pill">
                    <div>Van</div>
                    <div class="form-control-divider mx-2" ></div>
                    <input type="date" wire:model="filters.start" class="form-control-plaintext" >
                </div>
            </div>
            <div class="col">
                <div class="d-flex form-control rounded-pill">
                    <div>Tot</div>
                    <div class="form-control-divider mx-2" ></div>
                    <input type="date" wire:model="filters.end" class="form-control-plaintext" >
                </div>
            </div>

        </div>
    </div>

    <div class="my-3 flex-center">
        <div class="border-bottom w-100" ></div>
        <div class="card-title mx-2 nobr" >Asbest Statistieken</div>
        <div class="border-bottom w-100" ></div>
    </div>

    @foreach($chart_groups as $group)
        <div class="card">
            <div data-toggle-container wire:ignore >
                <div class="flex-between form-switch-label py-0" data-toggle-btn wire:click="openCharts('{{$group['key']}}')" >
                    <div>
                        <div class="card-title" >{{$group['name']}}</div>
                        <div class="card-subtitle" > {{$group['description']}} </div>
                    </div>
                    <a class="btn"  >{{count($group['charts'])}} @icon_down</a>
                </div>
                <div data-toggle-content style="display: none" class="w-100"  >
                    <div class="row">
                        @foreach($group['charts'] as $chart)
                            <div class="col-md-6 col-12 my-2">
                                <div class="chart-container" data-chart-type="{{$chart['type']}}" >
                                    <canvas class="{{$chart['key']}}" ></canvas>
                                    @chart_loader
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>

    @endforeach

    <script>
        $(document).ready(() => {
            Livewire.on('setChart', initDyamicChart);
        })
    </script>

</div>
