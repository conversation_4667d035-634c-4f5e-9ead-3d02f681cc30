
<div>
    @foreach($this->proces->activeStappen(['include_sub' => false]) as $proces_stap)
        @php $stap_status = $this->project->stapStatus($proces_stap->id); @endphp

        <div data-steps-container="{{$stap_status}}" @if($project->isInzichtbaar($proces_stap->id) && ($stap_status == ProjectStapType::CURRENT || $stap_status == ProjectStapType::COMPLETED) && !$proces_stap->hasSubStappen()) class="cursor-pointer" data-stap="{{$proces_stap->id}}" @endif >

            {{--Parent Stap Node--}}
            <div class="flex-start" >

                {{--Node--}}
                <div class="steps-node">
                    @if($stap_status == ProjectStapType::COMPLETED)
                        <div class="steps-month" >{{getShortMaanden($project->stapCompletedAt($proces_stap->id)->month)}}</div>
                        <div class="steps-day" >{{$project->stapCompletedAt($proces_stap->id)->format('d')}}</div>
                    @elseif($stap_status == ProjectStapType::SKIPPED)
                        @icon_close
                    @elseif($stap_status == ProjectStapType::CURRENT || $stap_status == ProjectStapType::CURRENT_SUB)
                        @icon_play
                    @endif
                </div>

                {{--Label--}}
                @if($project->isInzichtbaar($proces_stap->id) || $proces_stap->hasSubStappen())
                    <div class="steps-label" >
                        <div>
                            @if($project->stapApis($proces_stap->id, $this->planmatig, StappenApiType::OUTBOUND)->count()) <span class="text-success" > @icon_arrows_bottom_left_top_right </span> @endif
                            @if($project->stapApis(stap_id: $proces_stap->id, type: StappenApiType::INBOUND)->count()) <span class="text-danger" > @icon_arrows_bottom_left_top_right </span> @endif
                            <span>{{$proces_stap->stapnaam}}</span>
                        </div>
                        @if($project->isInzichtbaar($proces_stap->id) && $stap_status != ProjectStapType::OPEN && !$proces_stap->hasSubStappen())
                            <div class="steps-indicator" @if($proces_stap->id == $stap->id) data-active @endif ></div>
                        @endif
                    </div>
                @else
                    <div class="steps-label text-muted" >@icon_hide {{$proces_stap->stapnaam}}  </div>
                @endif

            </div>

            {{--Divider & Roles--}}
            @if(!$proces_stap->hasSubStappen())
                <div class="d-flex">
                    <div class="steps-divider @if($loop->last) opacity-0 @endif " ></div>
                    <div class="steps-description" >
                        @forelse($project->stapRolesInvullenVisual($proces_stap->id) as $role)
                            <div>@isset($role->user->bedrijf->naam) {{$role->user->bedrijf->naam}},@endisset {{$role->name}}</div>
                            @if(!$loop->last) <div class="nav-item-divider mx-0 my-1" ></div> @endif
                        @empty
                            <div class="text-danger" >Geen rollen geselecteerd!</div>
                        @endforelse
                    </div>
                </div>
            @else
                {{--Sub stappen divider--}}
                <div class="d-flex">
                    <div class="sub-steps-parent-divider"></div>
                    <div>
                        @foreach($proces_stap->activeSubStappen() as $sub_stap)
                            @php $stap_status = $this->project->stapStatus($sub_stap->id); @endphp
                            <div data-sub-steps-container="{{$stap_status}}" @if($project->isInzichtbaar($sub_stap->id) && ($stap_status == ProjectStapType::CURRENT_SUB || $stap_status == ProjectStapType::COMPLETED)) class="cursor-pointer" data-stap="{{$sub_stap->id}}" @endif >

                                <div class="d-flex my-2" >

                                    {{--Sub steps divider--}}
                                    <div class="sub-step-divider" ></div>

                                    <div>

                                        {{--Sub node && Roles--}}
                                        <div class="flex-start" >

                                            {{--Sub node--}}
                                            <div class="steps-node sub-steps-node">
                                                @if($stap_status == ProjectStapType::COMPLETED)
                                                    <div class="steps-month" >{{getShortMaanden($project->stapCompletedAt($sub_stap->id)->month)}}</div>
                                                    <div class="steps-day" >{{$project->stapCompletedAt($sub_stap->id)->format('d')}}</div>
                                                @elseif($stap_status == ProjectStapType::SKIPPED)
                                                    @icon_close
                                                @elseif($stap_status == ProjectStapType::CURRENT || $stap_status == ProjectStapType::CURRENT_SUB)
                                                    @icon_play
                                                @endif
                                            </div>

                                            {{--sub Label--}}
                                            @if($project->isInzichtbaar($sub_stap->id))
                                                <div class="steps-label sub-steps-label" >
                                                    <div>{{$sub_stap->stapnaam}}</div>
                                                    @if($project->isInzichtbaar($sub_stap->id) && $stap_status != ProjectStapType::OPEN)
                                                        <div class="sub-steps-indicator" @if($sub_stap->id == $stap->id) data-active @endif ></div>
                                                    @endif
                                                </div>
                                            @else
                                                <div class="steps-label sub-steps-label text-muted" >@icon_hide {{$sub_stap->stapnaam}}  </div>
                                            @endif

                                        </div>

                                        <div class="steps-description sub-steps-description" >
                                            @forelse($project->stapRolesInvullenVisual($sub_stap->id) as $role)
                                                <div>@isset($role->user->bedrijf->naam) {{$role->user->bedrijf->naam}},@endisset {{$role->name}}</div>
                                                @if(!$loop->last) <div class="nav-item-divider mx-0 my-1" ></div> @endif
                                            @empty
                                                <div class="text-danger" >Geen rollen geselecteerd!</div>
                                            @endforelse
                                        </div>

                                    </div>


                                </div>

                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

        </div>
    @endforeach
</div>
