<section>

    {{--<PERSON><PERSON><PERSON>--}}
    @if($invulbaar)

        {{--Outbound--}}
        @if($project->stapApis( planmatig: $this->planmatig, type: StappenApiType::OUTBOUND )->count())
            <div class="alert alert-success" >Na het invullen van deze stap wordt er data uitgewisseld met <b>{{$project->stapApis( planmatig: $this->planmatig, type: StappenApiType::OUTBOUND )->implode('koppeling', ', ')}}</b></div>
        @endif

        {{--Inbound--}}
        @if($project->stapApis( type: StappenApiType::INBOUND )->count())
            <div class="alert alert-danger" >Deze stap dient ingevuld te worden vanuit de <b>{{$project->stapApis( type: StappenApiType::INBOUND )->implode('koppeling', ', ')}}</b> koppeling.</div>
        @endif


    @endif




    {{--Gebruikers--}}
    @if($invulbaar)
      @if(!$gebruikers->isEmpty())
        <div class="card">
          <div class="card-title" >Gebruikers</div>
          <div class="row">
            @foreach($gebruikers as $role)
              <div class="col-md-6 col-12 my-2">
                <label>{{$role->name}}*</label>
                <infor-select-search class="form-control" placeholder="Selecteer {{$role->name}}" data-livewire="setGebruiker" data-livewire-params=":role_id={{$role->id}}" >
                  @foreach($this->getModifiedGebruikers($role->id) as $user)
                    <infor-select-option @if($this->hasGebruiker($user->id, $role->id)) data-selected @endif data-name="{{$user->name}}" data-value="{{$user->id}}" >
                      <div>{{$user->name}}</div>
                      <div class="text-muted font-size-08" >{{$user->email}}</div>
                    </infor-select-option>
                  @endforeach
                </infor-select-search>
              </div>
            @endforeach
          </div>
        </div>
      @endif
      @if(!$contacten->isEmpty())
        <div class="card">
          <div class="card-title" >Contacten</div>
          <div class="row">
            @foreach($contacten as $role)
              <div class="col-md-6 col-12 my-2" >
                <label>{{$role->name}}</label>
                <infor-select-multiple class="form-control" placeholder="Selecteer {{$role->name}}" data-livewire="setContacten" data-livewire-params=":role_id={{$role->id}}" >
                  @foreach($role->contacten as $contact)
                    <infor-select-option @if($this->hasContact($contact->id, $role->id)) data-selected @endif data-name="{{$contact->name}}" data-value="{{$contact->id}}" >
                      <div>
                        <div>{{$contact->name}}</div>
                        <div class="text-muted font-size-08" >{{$contact->email}}</div>
                      </div>
                    </infor-select-option>
                  @endforeach
                </infor-select-multiple>
              </div>
            @endforeach
          </div>
        </div>
      @endif
    @endif

    {{--Stap Inputs--}}
    @foreach($inputs->groupBy('db_input.context') as $context => $context_inputs)
        <div class="card @if(!$this->contextVisible($context)) d-none @endif">
            <div class="card-title">{{ ProjectStapContext::get($context, 'label') }}</div>
            <div class="row">
                @foreach($context_inputs as $input)
                    @php $veldnaam = $input->db_input->veldnaam; @endphp
                    <div class="col-12 my-2 @if(!$this->inputVisible($input)) d-none @endif">
                        {!! $input->HTMLLabel() !!}
                        {!! $input->HTMLInput(['livewire' => $this]) !!}
                    </div>
                @endforeach
            </div>
        </div>
    @endforeach

    {{--Planmatig projecten--}}
    @if($planmatig && $invulbaar)
        <div class="card" wire:ignore >
            <div class="card-title">Planmatige adressen selecteren</div>
            <infor-select-multiple data-livewire="setPlanmatigeProjecten" class="form-control" placeholder="Selecteer adressen" >
                @foreach($project->planmatigeProjecten() as $planmatig_project)
                    <infor-select-option @if($planmatig_project->id == $project->id) data-selected data-locked @endif data-value="{{$planmatig_project->id}}" >{{$planmatig_project->adres->addressLine()}}</infor-select-option>
                @endforeach
            </infor-select-multiple>
        </div>
    @endif

    {{--Opmerking--}}
    @if($this->getStapSettingValue('FIELD_OPMERKING'))
      <div class="card">
        <div class="card-title">Opmerkingen {{$stap->stapnaam}}</div>
        <textarea wire:model="project_stap.opmerking" placeholder="Opmerkingen" class="form-control" @if(!$invulbaar) disabled @endif ></textarea>
      </div>
    @endif

    {{--Not invulbaar notification--}}
    @if(!$invulbaar)
        <div class="text-danger" >
            <span>U kunt hier geen wijzigingen aanbrengen, omdat deze stap al is ingevuld en / of u niet de juiste rechten heeft.</span>
        </div>
    @endif

    {{--Store / Reject--}}
    @if($invulbaar && !$stored)
        <div class="my-3 text-center">
            @if($this->getStapSettingValue('STEP_REJECT', false))
              <a class="btn btn-danger" onclick="reject()" wire:target="store" wire:loading.remove >@if($planmatig) {{$planmatige_projecten ? $planmatige_projecten->count() : 0}} / {{$project->planmatigeProjecten()->count()}} Projecten @endif  Afwijzen</a>
            @endif

            @if($planmatig)
                <a class="btn
                    @if(!$planmatige_projecten->count()) btn-danger opacity-75 @elseif($planmatige_projecten->count() < $project->planmatigeProjecten()->count()) btn-warning @else btn-success @endif"
                    wire:click="store" wire:target="store" wire:loading.remove
                    wire:key="store-planmatig-btn"
                >
                    {{$planmatige_projecten ? $planmatige_projecten->count() : 0}} / {{$project->planmatigeProjecten()->count()}} Projecten
                    <span>Bevestigen</span>
                </a>
            @else
                <a class="btn btn-success" wire:click="store" wire:key="store-btn" wire:target="store" wire:loading.remove  >Bevestigen</a>
            @endif

            <div wire:loading wire:target="store" > @spinner_success </div>
            @error('*') <div class="text-danger my-1"> {!! $message !!} </div> @enderror
        </div>
    @elseif($stored)
        <div class="my-3 text-center">
          <div> @spinner_success </div>
        </div>
    @endif

</section>

<script>

    $(document).ready(() => {
        Livewire.on('stored', () => {
            notification('Stap succesvol ingevuld!', 'success');
            redirect(window.location.pathname, 350);
        })
        Livewire.on('rejected', () => {
            notification('Stap succesvol afgewezen!', 'warning');
            redirect(window.location.pathname, 350);
        })
        Livewire.on('refreshInput', (element) => {
            const input = $(element);
            input.val(input.attr('value'));
        })
        internalMessageListen('revert-stap', revert)
    })

    async function reject() {
        const { status, inputs } = await confirmModal({
            text: `
                <label>Weet u zeker dat u <b>${data.stap.stapnaam}</b> wilt afwijzen</label>
                <textarea name="reason" class="form-control" rows="4" placeholder="Reden*"></textarea>
            `,
            btnColor: 'btn-danger',
            btnText: 'Afwijzen',
        });

        if(!status){ return; }
        if(!inputs.reason){
            notification('Reden is een verplichte veld!');
            return;
        }

        @this.reject(inputs.reason);
    }
    async function revert(data){
        const { projecten, reason, stap } = data;
        await @this.revert(projecten, stap, reason)
    }


</script>
