<div>

  @if(count($gebruikers))
    <div class="bg-light border my-2 p-2 rounded-6 shadow-sm">
      <div class="row">
        @foreach($gebruikers as $gebruikers_setting)
          <div class="col-md-6 col-12 my-2">
            <label class="font-size-08 text-muted mb-0">{{getRole($gebruikers_setting->role_id)->name ?? ''}}</label>
            <div class="border-bottom min-h-px-25 font-size-105">{{$project->getGebruiker($gebruikers_setting->role_id)->name ?? ''}}</div>
          </div>
        @endforeach
      </div>
    </div>
  @endif

  @if(count($inputs))
    <div class="bg-light border my-2 p-2 rounded-6 shadow-sm">
      <div class="row">
        @foreach($inputs->groupBy('context') as $context => $context_inputs)
          @foreach($context_inputs as $input)
            @if(!isset($$context[$input->veldnaam]))
              @continue
            @endif
            <div class="col-md-6 col-12 my-2">
              <div class="label">{{$input->titel}}</div>
              <div class="label-value">{{$$context->getAttribute($input->veldnaam)}}</div>
            </div>
          @endforeach
        @endforeach
      </div>
    </div>
  @endif

  @if(count($projecten_files_inputs) || $proces->hasInput('opdrachtformulier'))
    <div class="bg-light border my-2 p-2 rounded-6 shadow-sm">
      @if($proces->hasInput('opdrachtformulier'))
        <label class="font-size-08 text-muted mb-0">Opdrachtformulier</label>
        <div class="border-bottom flex-start pb-1">
          {!! $opdrachtformulier->html() !!}
        </div>
      @endif
      @foreach($projecten_files_inputs as $input)
        <label class="font-size-08 text-muted mb-0">{{$input->titel}}</label>
        <div class="border-bottom flex-start pb-1">
          @forelse($project->getFiles($input->veldnaam) as $file)
            {!! $file->html() !!}
          @empty
            <span class="text-muted p-2 fst-italic">Geen bestanden gevonden</span>
          @endforelse
        </div>
      @endforeach
    </div>
  @endif

</div>
