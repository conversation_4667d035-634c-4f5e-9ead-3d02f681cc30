<section class="overflow-auto" >

    <table class="table">
        <thead>
        <tr>
            <th>Projectnummer</th>
            <th>Proces</th>
            <th>Status</th>
            <th>Eerste leegstanddag</th>
            <th class="w-0">Partij aan zet</th>
            <th>Waar is geinventariseerd</th>
            <th></th>
        </tr>
        </thead>
        <tbody>
        @forelse($projecten->groupBy('adres_id') AS $adres_projecten)

            @if(!$single_adres)
                <tr>
                    <th colspan="100" class="bg-inverse-secondary" >{{$adres_projecten->first()->adres->addressLine()}}</th>
                </tr>
            @endif

            @foreach($adres_projecten as $project)
                <tr onclick="redirect('/dashboard/projecten/project/{{ $project->id }}', 0, true)" class="hover-mark cursor-pointer" >
                    <td>{{ $project->projectnummer }}</td>
                    <td>{{ $project->proces->naam ?? '' }}</td>
                    <td>{{ $project->stap->stapnaam ?? '' }}</td>
                    <td>{{ displayDate($project->eersteleegstanddag) }}</td>
                    <td>
                        @foreach($project->stapUsersInvullen() as $user)
                            <div>
                                @isset($user->bedrijf->logo)
                                    <img height="35" class="tippy" src="{{_file($user->bedrijf->logo)}}" data-tippy-content="{{$user->bedrijf->naam}}">
                                @else
                                    {{$user->bedrijf->naam ?? ''}}
                                @endisset
                            </div>
                        @endforeach
                    </td>
                    <td></td>
                    <td class="text-end"><a class="btn btn-light">Documenten @icon_file </a></td>
                </tr>
            @endforeach
        @empty
            <tr>
                <td colspan="100" class="text-center text-muted">Er staan voor dit {{label('adres')}} geen project in Asbestregisseur</td>
            </tr>
        @endforelse
        </tbody>
    </table>

</section>
