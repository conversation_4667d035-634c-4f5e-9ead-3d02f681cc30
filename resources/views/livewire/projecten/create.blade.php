<section>

    @if(count($gebruikers_toevoegen))
        <div class="card">
            <div class="card-title">Gebruikers</div>
            <div class="row">
                @foreach($gebruikers_toevoegen as $role)
                    <div class="col-md-6 col-12 my-2" wire:ignore>
                        <label>{{$role->name}}*</label>
                        <infor-select-search id="set{{$role->key}}" class="form-control" placeholder="{{$role->name}}">
                            @foreach($role->users as $user)
                                <infor-select-option data-name="{{$user->name}}" data-value="{{$user->id}}">
                                    <div>{{$user->name}}</div>
                                    <div class="text-muted font-size-08">{{$user->email}}</div>
                                </infor-select-option>
                            @endforeach
                        </infor-select-search>
                    </div>
                @endforeach
            </div>
        </div>
    @endif
    @if(count($gebruikers_contact))
        <div class="card">
            <div class="card-title">Contacten</div>
            <div class="row">
                @foreach($gebruikers_contact as $role)
                    <div class="col-md-6 col-12 my-2" wire:ignore>
                        <label>{{$role->name}}*</label>
                        <infor-select-multiple id="set{{$role->key}}" data-livewire="set{{$role->name}}"
                                               class="form-control" placeholder="{{$role->name}}">
                            @foreach($role->contacten as $contact)
                                <infor-select-option data-name="{{$contact->name}}" data-value="{{$contact->id}}">
                                    <div>
                                        <div>{{$contact->name}}</div>
                                        <div class="text-muted font-size-08">{{$contact->email}}</div>
                                    </div>
                                </infor-select-option>
                            @endforeach
                        </infor-select-multiple>
                    </div>
                @endforeach
            </div>
        </div>
    @endif

    <div class="card">
        <div class="card-title">Project</div>
        <div class="row">
            @foreach($project_inputs as $input)
                <div class="col-md-6 col-12 my-2">
                    {!! $input->HTMLLabel() !!}
                    {!! $input->HTMLInput(['livewire' => $this]) !!}
                </div>
            @endforeach
        </div>
    </div>

    {{--Adressen--}}
    @if($planmatig)

        <div class="card">
            <div class="card-title">{{label('Adressen')}}</div>

            <div class="my-2" wire:ignore>
                <label>{{label('Adressen')}} selecteren</label>
                <infor-search data-livewire="setPlanmatigAdres" class="form-control" placeholder="Selecteer {{label('adres')}}"
                              data-content="straat, huisnummer,toevoeging" data-sub-content="plaats"
                              data-api="dashboard/api/search/adres" data-display-prefill="{{ optional($adres)->addressLine() }}"
                              data-value-prefill="{{ optional($adres)->id }}"></infor-search>
            </div>

            @if(optional($planmatig_adressen)->count())
                <div class="my-2">
                    <div class="card-subtitle" >Voorgestelde {{label('Adressen')}}</div>
                    <div class="border rounded shadow-inset-even-sm max-h-300 overflow-auto" >
                        @foreach($planmatig_adressen->groupBy('complexnummer') as $complex_adressen)
                            @php $complexnummer = $complex_adressen->first()->complexnummer; @endphp

                            <div class="flex-align text-muted px-2 mt-2">
                                <div class="full-divider"></div>
                                <div class="nobr mx-2">{{label('Complex')}}: {{$complexnummer}}</div>
                                <div class="full-divider"></div>
                            </div>

                            @foreach(getComplexAdressen($complexnummer) as $complex_adres)
                                @if($this->planmatig_adressen->contains($complex_adres->id)) @continue @endif

                                <div class="my-1" >
                                    <span class="btn btn-sm text-success" wire:click="setPlanmatigAdres({{$complex_adres->id}})" >@icon_plus</span>
                                    <span>{{$complex_adres->addressLine()}}</span>
                                </div>
                            @endforeach

                        @endforeach
                    </div>
                </div>
            @endif

            @if(optional($planmatig_adressen)->count())
                <div class="my-2">
                    <div class="card-subtitle" >Geselecteerde {{label('Adressen')}}</div>
                    <div class="border rounded shadow-inset-even-sm max-h-300 overflow-auto" >
                        @foreach($planmatig_adressen as $planmatig_adres)
                            <div class="my-2">
                                <div class="btn btn-sm text-danger" wire:click="removePlanmatigAdres({{$planmatig_adres->id}})" >@icon_close</div>
                                <span>{{$planmatig_adres->addressLine()}}</span>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif

        </div>

    @else

        @if(count($adres_projecten))
            <div class="alert alert-danger my-3" >
                Er zijn nog niet afgeronde projecten op dit adres:
                @foreach($adres_projecten as $project)
                    <a href="{{url("dashboard/projecten/project/{$project->id}")}}" target="_blank" >{{$project->projectnummer}}</a> @if(!$loop->last),@endif
                @endforeach
            </div>
        @endif

        <div class="card">
            <div class="card-title">{{label('Adres')}}</div>
            <div class="row">
                <div class="col-12 my-2" wire:ignore>
                    <label>{{label('Adres')}} selecteren</label>
                    <infor-search data-livewire="setAdres" class="form-control" placeholder="Selecteer {{label('adres')}}"
                                  data-content="straat, huisnummer,toevoeging" data-sub-content="plaats"
                                  data-api="dashboard/api/search/adres"
                                  data-display-prefill="{{ optional($adres)->addressLine() }}"
                                  data-value-prefill="{{ optional($adres)->id }}"></infor-search>
                </div>
                <div class="col-12 my-2 text-center">
                    <div class="flex-between">
                        <div class="border-bottom w-100"></div>
                        <div class="mx-3">OF</div>
                        <div class="border-bottom w-100"></div>
                    </div>
                </div>
                @foreach($adres_inputs as $input)
                    <div class="col-md-6 col-12 my-2">
                        {!! $input->HTMLLabel() !!}
                        {!! $input->HTMLInput(['livewire' => $this, 'data' => isset($adres->id) ? 'readonly' : '']) !!}
                    </div>
                @endforeach
                <div class="col-12" data-adres-check></div>
            </div>
        </div>
        @if(!isset($adres->id))
            <div>* Nieuw adres zal aangemaakt worden</div>
        @endif
    @endif

    <div class="my-3 text-center">
        @if($invulbaar)
            <a class="btn btn-success" wire:click="store" wire:target="store" wire:loading.remove>Opslaan</a>
            <div wire:loading wire:target="store"> @spinner_success</div>
        @else
            <div> @spinner_success</div>
        @endif

        @error('*')
        <div class="text-danger my-1"> {{ $message }} </div> @enderror
    </div>

</section>

<script>

    $(document).ready(() => {
        for (const role of @js($gebruikers_toevoegen)) {
            _inforSelectSearch.get(`set${role.key}`).onchange = user_id => {
                Livewire.emit('setGebruiker', user_id, role.id);
            }
        }
        for (const role of @js($gebruikers_contact)) {
            _inforSelectMultiple.get(`set${role.key}`).onchange = contact_ids => {
                Livewire.emit('setContact', contact_ids, role.id);
            }
        }

        Livewire.on('stored', () => {
            notification('Project succesvol aangemaakt!', 'success');
            redirect('/dashboard/projecten?status=ACTIELIJST', 350);
        })
    })

</script>
