<div class="card" >
    <div class="row">

        <div class="col-12 col-md-6 my-2">
            <label>Gebruikersnaam*</label>
            <input class="form-control" type="text" placeholder="Gebruikersnaam" wire:model="user.username" wire:change="checkUser" @if($edit) disabled @endif >
        </div>
        <div class="col-12 col-md-6 my-2" >
            <label>Rol* </label>
            <infor-select-search class="form-control" placeholder="Selecteer rol" data-livewire="setValue" data-livewire-params=":model=role_id" >
                @foreach(getRoles(1) as $role)
                    <infor-select-option @if($role->id == $role_id) data-selected @endif data-name="{{$role->name}}" data-value="{{$role->id}}">{{$role->name}}</infor-select-option>
                @endforeach
            </infor-select-search>
        </div>
        <div class="col-12 col-md-6 my-2">
            <label>Email*</label>
            <input class="form-control" type="text" placeholder="Email" wire:model.lazy="user.email" @if($user->id) disabled @endif >
        </div>
        <div class="col-12 col-md-6 my-2">
            <div class="flex-between">
                <label>Wachtwoord*</label>
                <div>
                    <input class="form-switch" type="checkbox" wire:model="user.is_sso" @if($user->id) disabled @endif > SSO
                </div>
            </div>
            <input class="form-control" type="password" placeholder="Wachtwoord" wire:model="user.pass" @if($user->id || $user->is_sso) disabled @endif >
        </div>
        <div class="col-12 col-md-6 my-2">
            <label>Naam*</label>
            <input class="form-control" type="text" placeholder="Naam" wire:model="user.name" @if($user->id) disabled @endif >
        </div>
        <div class="col-12 col-md-6 my-2">
            <label>Telefoonnummer*</label>
            <input class="form-control" type="text" placeholder="Telefoon" wire:model.lazy="user.telefoon" @if($user->id) disabled @endif >
        </div>
        <div class="col-12 col-md-6 my-2" >
            <label>Bedrijf</label>
            <infor-select-search @if($user->id) data-disabled @endif placeholder="Selecteer bedrijf" class="form-control"  data-livewire="setValue" data-livewire-params=":model=user.bedrijf_id" >
                @foreach(getBedrijven() as $bedrijf)
                    <infor-select-option @if($bedrijf->id == ($user->bedrijf_id ?? null)) data-selected @endif data-name="{{$bedrijf->naam}}" data-value="{{$bedrijf->id}}">{{$bedrijf->naam}}</infor-select-option>
                @endforeach
            </infor-select-search>
        </div>
        <div class="col-12 col-md-6 my-2" >
            <label class="opacity-0" >Spacer</label>
            <label class="form-switch-label" >
                <input class="form-switch" type="checkbox" wire:model="user.is_supervisor" @if($user->id) disabled @endif >
                <span>Supervisor</span>
            </label>
        </div>

        <div class="text-center my-2">
            <a class="btn btn-success" wire:click="store" wire:loading.remove wire:target="store">Opslaan</a>
            <div wire:loading wire:target="store"> @spinner_success</div>
            @error('*')
            <div class="text-danger my-1"> {{ $message }} </div> @enderror
        </div>

    </div>

</div>
