<div class="card" >
    <div class="card-title">Supervisie over</div>

    <div class="my-2">
        <infor-select-search class="form-control" placeholder="Gebruiker toevoegen" data-livewire="setUser" >
            @foreach(getUsers() as $user)
                @if($supervisor->supervised_users->contains('id', $user->id)) @continue @endif
                <infor-select-option data-name="{{$user->name}}" data-value="{{$user->id}}" >
                    <div>{{$user->name}}</div>
                    <div class="text-muted font-size-085" >{{$user->email}}</div>
                </infor-select-option>
            @endforeach
        </infor-select-search>
    </div>

    <div class="my-2">
        @foreach($supervisor->supervised_users as $supervised_user)
            <div class="form-switch-label flex-align hover-bg-inverse-danger px-0" wire:click="removeUser({{$supervised_user->id}})" >
                <a class="btn text-danger" >@icon_close</a>
                <div class="line-height-1" >
                    <div>{{$supervised_user->name}}</div>
                    <div class="text-muted" >{{$supervised_user->email}}</div>
                </div>
            </div>
        @endforeach
    </div>

</div>
