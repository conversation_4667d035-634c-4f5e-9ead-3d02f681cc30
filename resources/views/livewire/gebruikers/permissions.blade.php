<div>

    <div class="card">
        <div class="card-title">Permissions</div>

        @foreach($permissions->groupBy('category') as $category => $category_permissions)

            <div data-toggle-container class="rounded-5 border overflow-hidden my-2" >
                <div class="flex-between bg-light p-2 cursor-pointer" data-toggle-btn >
                    <div >
                        @if($this->hasCategoryPermission($category) == 'ALL')
                            <span class="text-success mx-2" >@icon_check</span>
                        @elseif($this->hasCategoryPermission($category) == 'SOME')
                            <span class="text-warning mx-2" >@icon_minus</span>
                        @else
                            <span class="text-danger mx-2" >@icon_close</span>
                        @endif
                        <span class="font-size-11" >{{$category}}</span>
                    </div>
                    <a class="btn" wire:ignore > @icon_down</a>
                </div>

                <div data-toggle-content style="display: none" class="w-100" wire:ignore.self >
                    <div class="p-2 my--2 " >
                        @foreach($category_permissions as $permission)
                            <div class="flex-align">
                                <label class="align-items-center cursor-pointer d-flex hover-mark my-2 px-1 w-100 rounded-5" wire:click="togglePermission('{{$permission->keyword}}')" >

                                    <div class="mx-2">
                                        @if(hasPermission($permission->keyword, $user->id))
                                            <div class="dot-glow dot-glow-success" ></div>
                                        @else
                                            <div class="dot-glow dot-glow-danger" ></div>
                                        @endif
                                    </div>

                                    <div class="mx-2" >
                                        <div class="font-size-11" >{{$permission->name}}</div>
                                        <div class="font-size-09 text-muted" >{{$permission->description}}</div>
                                    </div>
                                </label>
                                @if(userHasPermission($permission->keyword, $this->user->id) !== null)
                                    <span class="ml-2 btn btn-sm btn-inverse-danger nobr" wire:click="removePermission('{{$permission->keyword}}')" >@icon_close USER PERMISSION</span>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>


        @endforeach
    </div>

</div>
