<div>

  @if($logo || $corporatie->logo_guid)
    <div class="my-2 text-center">
      <img height="100" src="{{$logo ? $logo->temporaryUrl() : _file($corporatie->logo_guid)}}" alt="Logo">
    </div>
  @endif

  <div class="card">
    <div class="card-title">Domein</div>
    <div class="row">
      <div class="col-md-6 col-12 my-2">
        <label>Domein*</label>
        <input type="text" class="form-control" placeholder="Domein naam" wire:model="corporatie.domein">
      </div>
        <div class="col-md-6 col-12 my-2">
          <label>Logo</label>
          <input type="file" class="form-control" wire:model.lazy="logo" wire:loading.attr="disabled" accept="image/*" >
        </div>

    </div>
  </div>

  <div class="card">
    <div class="card-title">Database</div>
    <div class="row">
      <div class="col-md-6 col-12 my-2">
        <label>Gebruiker*</label>
        <input type="text" class="form-control" placeholder="Gebruikers naam" wire:model="database.username">
      </div>
      <div class="col-md-6 col-12 my-2">
        <label>Database naam*</label>
        <input type="text" class="form-control" placeholder="Database naam" wire:model="database.database_name">
      </div>
      <div class="col-md-6 col-12 my-2">
        <label>Wachtwoord{{$corporatie->id ? '' : '*'}}</label>
        <input type="password" class="form-control" placeholder="Wachtwoord" wire:model="password" >
      </div>
    </div>
  </div>

  <div class="mt-3 mb-5 text-center" >
    <a class="btn btn-success" wire:click="store" wire:target="store" wire:loading.remove  >Opslaan</a>
    <div wire:loading wire:target="store" > @spinner_success </div>
    @error('*') <div class="text-danger my-1"> {{ $message }} </div> @enderror
  </div>


</div>
  
  <script>
    $(document).ready(() => {
      Livewire.on('stored', () => {
        notification('Corporatie succesvol opgeslagen!', 'success');
        redirect('/dashboard/corporaties', 350);
      })
    })
    
  </script>
