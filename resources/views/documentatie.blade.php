@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">

            <h1>Documentatie</h1>

            <div class="card">
                <div class="card-body">

                     <div class="row">
                        <div class="col-sm-12">
                            Download hier je handleiding:<br>
                            <div class="row">
                                <div class="col-sm-6">
                                    <a href="{{ url('dashboard/instellingen/processen') }}">
                                        <div class="d-grid gap-2">
                                            <button type="button" class="btn btn-primary btn-lg">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="currentColor" class="bi bi-file-earmark-text-fill" viewBox="0 0 16 16">
                                                    <path d="M9.293 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V4.707A1 1 0 0 0 13.707 4L10 .293A1 1 0 0 0 9.293 0zM9.5 3.5v-2l3 3h-2a1 1 0 0 1-1-1zM4.5 9a.5.5 0 0 1 0-1h7a.5.5 0 0 1 0 1h-7zM4 10.5a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7a.5.5 0 0 1-.5-.5zm.5 2.5a.5.5 0 0 1 0-1h4a.5.5 0 0 1 0 1h-4z"/>
                                                </svg>
                                                <br>
                                                Handleiding
                                            </button>
                                        </div>
                                    </a>
                                </div>
                            </div>
                            <br>
                            @if(count($documentatie) != '0')  
                            Download hier overige documentatie:<br>
                           
                            @foreach($documentatie as $document)
                            <div class="row">
                                <div class="col-sm-6">
                                    <a href="{{ $document->bestand }}">
                                        <div class="d-grid gap-2">
                                            <button type="button" class="btn btn-primary btn-lg">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" fill="currentColor" class="bi bi-file-earmark-text" viewBox="0 0 16 16">
                                                    <path d="M5.5 7a.5.5 0 0 0 0 1h5a.5.5 0 0 0 0-1h-5zM5 9.5a.5.5 0 0 1 .5-.5h5a.5.5 0 0 1 0 1h-5a.5.5 0 0 1-.5-.5zm0 2a.5.5 0 0 1 .5-.5h2a.5.5 0 0 1 0 1h-2a.5.5 0 0 1-.5-.5z"/>
                                                    <path d="M9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V4.5L9.5 0zm0 1v2A1.5 1.5 0 0 0 11 4.5h2V14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h5.5z"/>
                                                </svg>
                                                <br>
                                                {{ $document->naam }}
                                            </button>
                                        </div>
                                    </a>
                                </div>
                            </div>
                            <br>
                            @endforeach
                            @endif
                        </div>
                    </div>
                    @if(\Request::get('gebruikersvorm') == 'regisseur')
                        <form method="post">
                        @csrf
                            <div class="row">
                                <label>Nieuw document toevoegen:</label>
                                <div class="col-sm-12">
                                    <div class="form-group mt-2">
                                        <label>Naam</label>
                                        <input name="naam" type="text" class="form-control" placeholder="Naam" required>
                                    </div>
                                    <div class="form-group mt-2">
                                        <label>Soort</label>
                                        <input name="soort" type="text" class="form-control" placeholder="Soort" required>
                                    </div>
                                    <div class="form-group mt-2">
                                        <label for="formFileLg" class="form-label">Selecteer hier het bestand</label>
                                        <input type="file" class="form-control" id="customFile" />
                                    </div>
                                </div>
                                <br>
                                <div class="col-sm-12 mt-2">
                                    <button style="margin-bottom: 40px;" type="submit" class="btn btn-success">Document uploaden</button>
                                </div>
                            </div>
                        </form>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
