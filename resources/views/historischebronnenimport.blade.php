@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-12">
            @if (session('status'))
                <div class="alert alert-success" role="alert">
                    {{ session('status') }}
                </div>
            @endif

            <h1>Complexen</h1>
            <p>U kunt alle informatie van de complexen bekijken door ze te selecteren</p>
            <form method="post">
                @csrf
                <div class="input-group mb-3">
                    <input type="text" class="form-control" name="zoekterm" placeholder="Zoekterm">
                    <div class="input-group-append">
                        <button type="submit" class="btn btn-primary">Zoeken!</button>
                    </div>
                </div>
            </form>
            <hr>
            <table class="table table-hover">
                <thead>
                    <tr>
                        
                        <th><a href="{{ url('dashboard/complexen/complexnummer') }}" style="color: #000; text-decoration: none">Complexnummer</a></th>
                        <th><a href="{{ url('dashboard/complexen/plaats') }}" style="color: #000; text-decoration: none">Plaats</a></th>
                       
                    </tr>
                </thead>
                <tbody>
                    @foreach($complexen AS $complex)
                    <tr onclick="openComplex({{ $complex->complexnummer }})">
                        <td>{{ $complex->complexnummer }}</td>
                        <td>{{ $complex->plaats }}</td>
                    </tr>
                    @endforeach

                    @if(count($complexen) == 0)
                    <tr>
                        <td colspan="11">Geen resultaten met deze zoekterm...</td>
                    </tr>
                    @endif
                </tbody>
            </table>
            {{ $complexen->links() }}
        </div>
    </div>
</div>
@endsection

@section('script')
<script>
    var SITEURL = '{{URL("/")}}';
    function openComplex(complexnummer){
        // alert('Op complex '+complexnummer+' geklikt');
        window.location.href = SITEURL+'/dashboard/complexen/'+complexnummer;

    }
</script>
@endsection 