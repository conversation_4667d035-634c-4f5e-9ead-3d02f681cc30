{{-- Stap 2.1: Vervangen risicoklassen --}}
{{-- <PERSON><PERSON>en risicoklassen uit verkregen datasets (inventariseerder en saneerder) met risicoklassen uit database--}}
@extends('layouts.app')

@section('content')

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-12">
            @if (session('status'))
                <div class="alert alert-success" role="alert">
                    {{ session('status') }}
                </div>
            @endif
            {{-- Definiëren titel --}}
            <h1>Vervang Risicoklassen</h1>
            <p>{{ $saneerder }}</p>

            {{-- Definiëren tabel titels --}}
            <hr>
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Risicoklasse in dataset</th>
                        <th>Gekozen Risicoklasse (in DB)</th>
                        <th>Toevoegen</th>
                    </tr>
                </thead>
                <tbody id="tbody">
                    {{-- <PERSON><PERSON> alle risicoklassen die voorkomen in de verkregen dataset op --}}
                    @foreach($risicoklassen AS $risicoklasse)
                    <tr>
                        {{-- Geef de zojuist opgehaalde waarden weer --}}
                        <td>{{ $risicoklasse->risicoklasse }}</td>
                        <td>
                            {{-- Wanneer een risicoklasse wordt aangeklikt uit het dropdown menu, voer functie 'updateRisicoklasse()' uit--}}
                            <select class="form-control" id="risicoklasse" onchange="updateRisicoklasse(this, '{{ $risicoklasse->risicoklasse }}')">
                                {{-- Weergeef 'Nog niet geselecteerd' als er nog geen optie is aangeklikt --}}
                                <option value="0" disabled selected>Nog niet geselecteerd</option>
                                {{-- Ophalen alle risicoklassen die in de database staan (Geef deze in dropdown menu weer) --}}
                                @foreach($allerisicoklassen AS $rk)
                                <option value="{{ $rk->id }}" @if($risicoklasse->risicoklasse_id == $rk->id) selected @endif>{{ $rk->klasse }}</option>
                                @endforeach
                            </select>
                        </td>
                        <td>
                            {{-- Wanneer op de knop 'toevoegen' wordt geklikt, voer de functie 'addAndReplace()' uit --}}
                            <button class="btn btn-primary" onclick="addAndReplace('{{ $risicoklasse->risicoklasse }}')">Toevoegen</button>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>

            {{-- Button onderaan pagina --}}
            {{-- Deze leidt je terug naar de hoofdpagina --}}
            <a href="{{ url('dashboard/tempimport') }}" id="terugbutton"><button class="btn btn-primary">Terug</button></a>
        </div>
    </div>
</div>

@endsection

{{-- Script --}}
{{-- Hierin worden de uit te voeren functies gedefiniëerd --}}
@section('script')
<script>

    var saneerder = @json($saneerder);

    // Updaten risicoklasse
    // Risicoklasse uit verkregen data vervangen met geselecteerde risicoklasse uit dropdown menu (Afkomstig van database waarde)
    function updateRisicoklasse(obj, risicoklasse){
        var risicoklasse_id = obj.value;
        var s = '';
        // Wanneer data van saneerder, plak '/saneerder' achter de link (>naar pagina voor saneerder)
        if(saneerder){
            s = "/saneerder";
        }
        $.ajax({
            method: "POST",
            url: "https://www.asbestregisseur.be/api/updaterisicoklasse"+s,
            data: {
                risicoklasse_id: risicoklasse_id,
                risicoklasse: risicoklasse
            }
        })
        .done(function (response){
            if(response.success == true){

            }else{
                alert('Fout bij het updaten');
            }
        })
        .fail(function (jqXHR, textStatus){
            alert("error: "+textStatus);
        });
    }

    // Toevoegen risicoklasse
    // Wanneer een risicoklasse uit de verkregen data niet in de database staat maar wel handig is, kan deze toegevoegd worden aan de database
    function addAndReplace(risicoklasse){
        var s = '';
        if(saneerder){
            s = "/saneerder";
        }
        $.ajax({
            method: "POST",
            url: "https://www.asbestregisseur.be/api/addrisicoklasse"+s,
            data: {
                risicoklasse: risicoklasse
            }
        })
        .done(function (response){
            if(response.success == true){
               location.reload();
            }else{
                alert('Fout bij het updaten');
            }
        })
        .fail(function (jqXHR, textStatus){
            alert("error: "+textStatus);
        });
    }

</script>
@endsection
