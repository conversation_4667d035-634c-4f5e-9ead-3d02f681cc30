@extends('layouts.app')

@section('content')

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-12">
            @if (session('status'))
                <div class="alert alert-success" role="alert">
                    {{ session('status') }}
                </div>
            @endif

            <h1>Vervang (Bron)Omschrijving</h1>
            <p>{{ $saneerder }}</p>

            <hr>
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Omschrijving in DB</th>
                        <th>Gekozen Omschrijving</th>
                        <th>Toevoegen</th>
                    </tr>
                </thead>
                <tbody id="tbody">
                    @foreach($omschrijvingen AS $omschrijving)
                    <tr>
                        <td>{{ $omschrijving->omschrijving }}</td>
                        <td>
                            <select class="form-control" id="omschrijving" onchange="updateOmschrijving(this, '{{ $omschrijving->omschrijving }}')">
                                <option value="0" disabled selected>Nog niet geselecteerd</option>
                                @foreach($alleomschrijvingen AS $os)
                                <option value="{{ $os->id }}" @if($omschrijving->omschrijving_id == $os->id) selected @endif>{{ $os->omschrijving }}</option>
                                @endforeach
                            </select>
                        </td>
                        <td>
                            <button class="btn btn-primary" onclick="addAndReplace('{{ $omschrijving->omschrijving }}')">Toevoegen</button>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>

            <a href="{{ url('dashboard/tempimport') }}" id="terugbutton"><button class="btn btn-primary">Terug</button></a>
        </div>
    </div>
</div>

@endsection
@section('script')
<script>
    
    function updateOmschrijving(obj, omschrijving){
        var omschrijving_id = obj.value;
        $.ajax({
            method: "POST",
            url: "https://www.asbestregisseur.be/api/updateomschrijving",
            data: {
                omschrijving_id: omschrijving_id,
                omschrijving: omschrijving
            }
        })
        .done(function (response){
            if(response.success == true){
               
            }else{
                alert('Fout bij het updaten');
            }
        })
        .fail(function (jqXHR, textStatus){
            alert("error: "+textStatus);
        });
    }

    function addAndReplace(omschrijving){
        $.ajax({
            method: "POST",
            url: "https://www.asbestregisseur.be/api/addomschrijving",
            data: {
                omschrijving: omschrijving
            }
        })
        .done(function (response){
            if(response.success == true){
               location.reload();
            }else{
                alert('Fout bij het updaten');
            }
        })
        .fail(function (jqXHR, textStatus){
            alert("error: "+textStatus);
        });
    }

</script>
@endsection
