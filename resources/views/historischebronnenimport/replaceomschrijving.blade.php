{{-- Stap 2.3: Vervangen bronomschrijvingen --}}
{{-- <PERSON><PERSON><PERSON> bronomschrijvingen uit verkregen datasets (inventariseerder en saneerder) met bronomschrijvingen uit database--}}
@extends('layouts.app')

@section('content')

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-12">
            @if (session('status'))
                <div class="alert alert-success" role="alert">
                    {{ session('status') }}
                </div>
            @endif
            {{-- Definiëren titel --}}
            <h1>Vervang (Bron)Omschrijving</h1>
            <p>{{ $saneerder }}</p>

            {{-- Definiëren tabel titels --}}
            <hr>
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Omschrijving in dataset</th>
                        <th>Gekozen Omschrijving (in DB)</th>
                        <th>Toevoegen</th>
                    </tr>
                </thead>
                <tbody id="tbody">
                    {{-- Haal alle bronomschrijvingen die voorkomen in de verkregen dataset op --}}
                    @foreach($omschrijvingen AS $omschrijving)
                    <tr>
                        {{-- Geef de zojuist opgehaalde waarden weer --}}
                        <td>{{ $omschrijving->omschrijving }}</td>
                        <td>
                            {{-- Wanneer een bronomschrijving wordt aangeklikt uit het dropdown menu, voer functie 'updateOmschrijving()' uit--}}
                            <select class="form-control" id="omschrijving" onchange="updateOmschrijving(this, '{{ $omschrijving->omschrijving }}')">
                                {{-- Weergeef 'Nog niet geselecteerd' als er nog geen optie is aangeklikt --}}
                                <option value="0" disabled selected>Nog niet geselecteerd</option>
                                {{-- Ophalen alle bronomschrijvingen die in de database staan (Geef deze in dropdown menu weer) --}}
                                @foreach($alleomschrijvingen->sortBy('omschrijving') AS $os)
                                <option value="{{ $os->id }}" @if($omschrijving->omschrijving_id == $os->id) selected @endif>{{ $os->omschrijving }}</option>
                                @endforeach
                            </select>
                        </td>
                        <td>
                            {{-- Wanneer op de knop 'toevoegen' wordt geklikt, voer de functie 'addAndReplace()' uit --}}
                            <button class="btn btn-primary" onclick="addAndReplace('{{ $omschrijving->omschrijving }}')">Toevoegen</button>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>

            {{-- Button onderaan pagina --}}
            {{-- Deze leidt je terug naar de hoofdpagina --}}
            <a href="{{ url('dashboard/tempimport') }}" id="terugbutton"><button class="btn btn-primary">Terug</button></a>
        </div>
    </div>
</div>

@endsection

{{-- Script --}}
{{-- Hierin worden de uit te voeren functies gedefiniëerd --}}
@section('script')
<script>

    var saneerder = @json($saneerder);

    // Updaten bronomschrijvingen
    // Bronomschrijving uit verkregen data vervangen met geselecteerde bronomschrijving uit dropdown menu (Afkomstig van database waarde)
    function updateOmschrijving(obj, omschrijving){
        var omschrijving_id = obj.value;
        var s = '';
        // Wanneer data van saneerder, plak '/saneerder' achter de link (>naar pagina voor saneerder)
        if(saneerder){
            s = "/saneerder";
        }
        $.ajax({
            method: "POST",
            url: "https://www.asbestregisseur.be/api/updateomschrijving"+s,
            data: {
                omschrijving_id: omschrijving_id,
                omschrijving: omschrijving
            }
        })
        .done(function (response){
            if(response.success == true){

            }else{
                alert('Fout bij het updaten');
            }
        })
        .fail(function (jqXHR, textStatus){
            alert("error: "+textStatus);
        });
    }

    // Toevoegen bronmschrijvingen
    // Wanneer een bronmschrijving uit de verkregen data niet in de database staat maar wel handig is, kan deze toegevoegd worden aan de database
    function addAndReplace(omschrijving){
        var s = '';
        if(saneerder){
            s = "/saneerder";
        }
        $.ajax({
            method: "POST",
            url: "https://www.asbestregisseur.be/api/addomschrijving"+s,
            data: {
                omschrijving: omschrijving
            }
        })
        .done(function (response){
            if(response.success == true){
               location.reload();
            }else{
                alert('Fout bij het updaten');
            }
        })
        .fail(function (jqXHR, textStatus){
            alert("error: "+textStatus);
        });
    }

</script>
@endsection
