{{-- Stap 2.2: Vervangen bronlocaties --}}
{{-- <PERSON><PERSON><PERSON> bronlocaties uit verkregen datasets (inventariseerder en saneerder) met bronlocaties uit database--}}
@extends('layouts.app')

@section('content')

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-12">
            @if (session('status'))
                <div class="alert alert-success" role="alert">
                    {{ session('status') }}
                </div>
            @endif
            {{-- Definiëren titel --}}
            <h1>Vervang (Bron)Locatie</h1>
            <p>{{ $saneerder }}</p>

            {{-- Definiëren tabel titels --}}
            <hr>
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Locatie in dataset</th>
                        <th>Gekozen Locatie (in DB)</th>
                        <th>Toevoegen</th>
                    </tr>
                </thead>
                <tbody id="tbody">
                    {{-- Haal alle bronlocaties die voorkomen in de verkregen dataset op --}}
                    @foreach($locaties AS $locatie)
                    <tr>
                        {{-- Geef de zojuist opgehaalde waarden weer --}}
                        <td>{{ $locatie->locatie }}</td>
                        <td>
                            {{-- Wanneer een bronlocatie wordt aangeklikt uit het dropdown menu, voer functie 'updateLocatie()' uit --}}
                            <select class="form-control" id="locatie" onchange="updateLocatie(this, '{{ $locatie->locatie }}')">
                                {{-- Weergeef 'Nog niet geselecteerd' als er nog geen optie is aangeklikt --}}
                                <option value="0" disabled selected>Nog niet geselecteerd</option>
                                {{-- Ophalen alle bronlocaties die in de database staan (Geef deze in dropdown menu weer) --}}
                                @foreach($allelocaties->sortBy('locatie') AS $lt)
                                <option value="{{ $lt->id }}" @if($locatie->locatie_id == $lt->id) selected @endif>{{ $lt->locatie }}</option>
                                @endforeach
                            </select>
                        </td>
                        <td>
                            {{-- Wanneer op de knop 'toevoegen' wordt geklikt, voer de functie 'addAndReplace()' uit --}}
                            <button class="btn btn-primary" onclick="addAndReplace('{{ $locatie->locatie }}')">Toevoegen</button>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>

            {{-- Button onderaan pagina --}}
            {{-- Deze leidt je terug naar de hoofdpagina --}}
            <a href="{{ url('dashboard/tempimport') }}" id="terugbutton"><button class="btn btn-primary">Terug</button></a>
        </div>
    </div>
</div>

@endsection

{{-- Script --}}
{{-- Hierin worden de uit te voeren functies gedefiniëerd --}}
@section('script')
<script>

    var saneerder = @json($saneerder);

    // Updaten bronlocaties
    // Bronlocatie uit verkregen data vervangen met geselecteerde bronlocatie uit dropdown menu (Afkomstig van database waarde)
    function updateLocatie(obj, locatie){
        var locatie_id = obj.value;
        var s = '';
        // Wanneer data van saneerder, plak '/saneerder' achter de link (>naar pagina voor saneerder)
        if(saneerder){
            s = "/saneerder";
        }
        $.ajax({
            method: "POST",
            url: "https://www.asbestregisseur.be/api/updatelocatie"+s,
            data: {
                locatie_id: locatie_id,
                locatie: locatie
            }
        })
        .done(function (response){
            if(response.success == true){

            }else{
                alert('Fout bij het updaten');
            }
        })
        .fail(function (jqXHR, textStatus){
            alert("error: "+textStatus);
        });
    }

    // Toevoegen bronlocatie
    // Wanneer een bronlocatie uit de verkregen data niet in de database staat maar wel handig is, kan deze toegevoegd worden aan de database
    function addAndReplace(locatie){
        var s = '';
        if(saneerder){
            s = "/saneerder";
        }
        $.ajax({
            method: "POST",
            url: "https://www.asbestregisseur.be/api/addlocatie"+s,
            data: {
                locatie: locatie
            }
        })
        .done(function (response){
            if(response.success == true){
               location.reload();
            }else{
                alert('Fout bij het updaten');
            }
        })
        .fail(function (jqXHR, textStatus){
            alert("error: "+textStatus);
        });
    }

</script>
@endsection
