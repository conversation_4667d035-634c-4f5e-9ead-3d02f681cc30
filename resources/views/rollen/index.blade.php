@extends('layouts.app')

@section('content')
    <section>

        <infor-table id="rollen_table" >


            <infor-table-pagination>
                <infor-table-order-option data-column="login" >Type</infor-table-order-option>
                <infor-table-order-option data-column="name" >Naam</infor-table-order-option>
            </infor-table-pagination>

            <infor-table-count data-single="Rol" data-multiple="Rollen" ></infor-table-count>

            <infor-table-header>Naam</infor-table-header>
            <infor-table-header>Type</infor-table-header>
            <infor-table-header>Aantal gebruikers</infor-table-header>


        </infor-table>

    </section>
@endsection

@section('script')
    <script>

        pageInteractive(() => {

          initInforTables({
            id: 'rollen_table',
            api: `/dashboard/api/rollen/get`,
            response_records_name: 'rollen',
            search: {
              api: 'dashboard/api/search/rol',
              content: 'name',
              placeholder: `<PERSON><PERSON>...`,
            },
            errorHandler: handleAjaxErrors,
            fillRecord: fillRecord,
            execute: [tippyInit]
          })

        })

        function fillRecord(record){
            const { id, name, login, users, contacten } = record;
            const role_users = Number(login) ? users : contacten;

            return `
                <tr class="cursor-pointer" onclick="redirect('/dashboard/rollen/edit/${id}')" >
                    <td>${name || ''}</td>
                    <td>${Number(login) ? `<span class="badge badge-primary" >Gebruiker</span>` : '<span class="badge badge-warning" >Contact</span>' }</td>
                    <td>
                      ${role_users.length
                        ? `<span class="badge badge-secondary w-px-50 tippy" data-tippy-content="${role_users.map(user => `<div>• ${user.name}</div>`).join('')}" >${role_users.length}</span>`
                        : `<span class="badge badge-light w-px-50">${role_users.length}</span>`
                      }
                    </td>
                </tr>`
        }

    </script>
@endsection
