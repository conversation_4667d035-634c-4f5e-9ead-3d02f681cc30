@extends('layouts.app')

@section('content')
    <section class="d-flex justify-content-center" >

        <div data-login-container class="d-none" >
            <div class="card login-card" >
                <div class="card-body">
                    <div class="d-flex justify-content-center my-2">
                        <img src="{{ url('dashboard/img/Logo.png') }}" style="width: 60%;">
                    </div>
                    <form method="POST" action="{{ route('login') }}" data-main-form="primary-light" >

                        <div class="text-center" >
                            <input id="username" type="username" placeholder="Gebruikersnaam" class="form-control-plaintext text-center my-2 rounded-pill px-2 bg-light" name="username" value="{{ old('username') }}" required autocomplete="username" autofocus>
                            <input id="password" type="password" placeholder="Wachtwoord" class="form-control-plaintext text-center my-2 rounded-pill px-2 bg-light" name="password" required autocomplete="current-password">
                        </div>

                        @error('*')
                            <div class="text-danger text-danger">
                                <span>{{ $message }}</span>
                            </div>
                        @enderror

                        <div class="text-center mt-4 mb-2">
                            <button type="submit" class="btn-primary-light btn-login hover-shadow-active">LOGIN</button>
                            <input class="d-none" type="checkbox" name="remember" checked >
                            @if(_get('domain'))
                                <input type="hidden" name="domain_guid" value="{{_get('domain')}}" >
                            @endif
                            @if(_get('redirect'))
                                <input type="hidden" name="redirect" value="{{urldecode(_get('redirect'))}}" >
                            @endif
                            @csrf
                        </div>

                    </form>
                </div>
                <div class="card-header border-top text-center py-4">
                    <a class="text-primary-light font-size-11" >Wachtwoord vergeten?</a>
                </div>
            </div>

            <div class="my-2 text-center">
                <a class="btn-primary-light btn-login hover-shadow-active nobr px-3 px-md-6" data-login-sso >
                    <img src="{{url('dashboard/img/icons/microsoft.png')}}" height="20" >
                    <span>LOGIN MET MICROSOFT SSO</span>
                </a>
            </div>
        </div>

    </section>
@endsection

@section('script')
    <script>

        const sso_domains = @json(getSSODomains())

        $(document).on('click', '[data-login-sso]', () => {
            confirmModal({
                text: `
                    <div data-sso-select-container >
                        <div class="form-control flex-align" >
                            <img src="${_url('dashboard/img/icons/microsoft.png')}" height="20" >
                            <div class="form-control-divider mx-2" ></div>
                            <infor-select-search id="sso-domain" class="form-control-plaintext w-100 p-0" placeholder="Selecteer domein" >
                              ${sso_domains.map(domain => `<infor-select-option data-value="${domain.domein_key}" >${domain.domein}</infor-select-option>`).join('')}
                            </infor-select-search>
                        </div>
                    </div>
                `,
                hideFooter: true,
                execute: () => {
                    initInforSelectSearch();
                    _inforSelectSearch.get('sso-domain').onchange = key => {
                        $('[data-sso-select-container]').html(`<div class="text-center" > @spinner </div>`)
                        redirectURL(_url(`dashboard/login/sso/${key}`));
                    }
                },
            })

        })

        pageInteractive(() => {
          $('[data-login-container]').toggleClass(['login-container', 'd-none']);
        })

    </script>
@endsection
