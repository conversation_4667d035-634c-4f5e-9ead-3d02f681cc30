@extends('layouts.app')

@section('content')
    <section class="d-flex justify-content-center" >
        <div class="w-px-600" >
            <div class="card login-card" >
                <div class="card-title">Reset uw wachtwoord</div>
                @if(!_user()->password_updated_at)<div class="text-muted" >Bij de eerste inlog dient u uw wachtwoord te wijzigen.</div>@endif
                <div class="card-body">

                    <form method="POST" autocomplete="off" data-main-form="primary-light" >

                        <div class="text-center" >
                            <input type="password" name="password_new" placeholder="Wachtwoord" class="form-control-plaintext text-center my-2 rounded-pill px-2 bg-light"   required autofocus>
                            <input type="password" name="password_repeat" placeholder="Wachtwoord herhalen" class="form-control-plaintext text-center my-2 rounded-pill px-2 bg-light" required >
                        </div>

                        <div class="my-2 mx-1 font-size-075" >

                            <div data-indicator-container="length" class="text-muted" >
                                <span data-indicator class="dot-glow dot-glow-sm dot-glow-secondary mr-1" ></span>
                                <span>Minimaal 8 tekens lang</span>
                            </div>
                            <div data-indicator-container="uppercase" class="text-muted" >
                                <span data-indicator class="dot-glow dot-glow-sm dot-glow-secondary mr-1" ></span>
                                <span>Minstens een hoofdletter (A-Z)</span>
                            </div>
                            <div data-indicator-container="lowercase" class="text-muted" >
                                <span data-indicator class="dot-glow dot-glow-sm dot-glow-secondary mr-1" ></span>
                                <span>Minstens een kleine letter (a-z)</span>
                            </div>
                            <div data-indicator-container="number" class="text-muted" >
                                <span data-indicator class="dot-glow dot-glow-sm dot-glow-secondary mr-1" ></span>
                                <span>Minstens een cijfer (0-9)</span>
                            </div>
                            <div data-indicator-container="special_character" class="text-muted" >
                                <span data-indicator class="dot-glow dot-glow-sm dot-glow-secondary mr-1" ></span>
                                <span>Minstens een speciaal teken (!@#$%^& etc.)</span>
                            </div>
                            <div data-indicator-container="repeat" class="text-muted" >
                                <span data-indicator class="dot-glow dot-glow-sm dot-glow-secondary mr-1" ></span>
                                <span>Wachtwoorden komen overeen</span>
                            </div>

                        </div>

                        @error('*')
                            <div class="text-danger text-danger">
                                <span>{{ $message }}</span>
                            </div>
                        @enderror

                        <div class="text-center mt-4 mb-2">
                            <button data-password-submit type="submit" class="btn-primary-light btn-login hover-shadow-active opacity-33" disabled>WIJZIGEN</button>
                            @csrf
                        </div>

                    </form>
                </div>
            </div>
        </div>
    </section>
@endsection
@section('script')
<script>

    $(document).ready(validatePassword)
    $(document).on('input', '[name=password_new], [name=password_repeat]', validatePassword)

    function validatePassword(){
        const password = $('[name=password_new]').val();
        const repeat = $('[name=password_repeat]').val();

        setIndicator('length', password.length >= 8)
        setIndicator('uppercase', password != password.toLowerCase());
        setIndicator('lowercase', password != password.toUpperCase());
        setIndicator('number', /\d/.test(password));
        setIndicator('special_character', /[!@#$%^&*(),.?":{}|<>]/.test(password));
        setIndicator('repeat', password && (password == repeat));


        const valid = !$('[data-indicator-state=false]').length;
        $('[data-password-submit]')
            .attr('disabled', !valid)
            .toggleClass('opacity-33', !valid)
    }
    function setIndicator(key, state){
        const container = $(`[data-indicator-container=${key}]`);
        const indicator = container.find('[data-indicator]');

        container
            .toggleClass('text-success', state)
            .toggleClass('text-muted', !state)

        indicator
            .toggleClass('dot-glow-success', state)
            .toggleClass('dot-glow-secondary', !state)
            .attr('data-indicator-state', state);
    }

</script>
@endsection
