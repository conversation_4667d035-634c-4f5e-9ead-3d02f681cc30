@extends('layouts.app_non_auth')

@section('content')

    <div class="position-fixed top-33 start-50 translate-middle mb-5 w-px-600 px-2 max-w" >

        <div class="text-center">
            <img class="max-w" src="{{url('dashboard/img/Logo.png')}}" width="400">
        </div>

        <div class="card p-4">

            <div class="text-center opacity-33 font-size-45 check-icon transition-05 text-regisseur" data-confirm-icon >@icon_hourglass_half</div>
            @isset($title) <div class="text-center font-size-155" data-title >{{$title}}</div> @endif
            <div class="text-center text-muted" data-message >{{$message}}</div>

            <div class="my-4">
                <div data-confirm-button-container class="w-100" >
                    <a class="btn btn-regisseur rounded-4 font-size-1 w-100" data-confirm-button  >{{ $button_text }}</a>
                </div>
                <div data-redirect-button-container class="w-100" style="display: none" >
                    <a class="d-block btn btn-regisseur rounded-4 font-size-1" href="{{url('/dashboard/login')}}"  >Terug naar Regisseur.nl @icon_arrow_right</a>
                </div>
            </div>

        </div>

    </div>

@endsection
@section('script')
<script>

    const endpoint = @json($endpoint);
    const data = @json($data);

    const new_title = @json($new_title);
    const new_message = @json($new_message);

    $('[data-confirm-button]').click(async () => {
        try{
            loader();
            await ajax(endpoint, data)
            clearLoader();

            setText('[data-title]', new_title);
            setText('[data-message]', new_message);

            $('[data-confirm-button-container]').toggle(200);
            setTimeout(() => { $('[data-redirect-button-container]').toggle(200); }, 200)

            $('[data-confirm-icon]').removeClass(['opacity-33']).addClass(['opacity-1']).html(`<i class="fa-regular fa-circle-check m-0"></i>`);
        }
        catch (e) { handleAjaxErrors(e); }
    });


    function setText(id, text){
        $(id).html(text);
    }



</script>
@endsection
