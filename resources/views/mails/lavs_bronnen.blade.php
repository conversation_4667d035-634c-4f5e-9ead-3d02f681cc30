@extends('mails.components.layout')
@section('head')
<style>

    .table{
        width: 100%;
        font-size: .8rem;
        border-collapse: collapse;
    }
    .table thead tr{
        background-color: #F0F0F0;
    }
    .table td, .table th{
        border: 1px solid #F0F0F0;
        padding: .25rem .25rem;
    }

    .button{
        display: block;
        width: 100%;
        padding: .5rem;
        border-radius: .5rem;
        text-align: center;
        margin: .5rem 0;
        background-color: #01689b;
        color: #FFF;
    }
    .button-no{
        background-color: #D9E8F0;
        color: #01689b;
    }

</style>
@endsection
@section('content')

    <p><PERSON><PERSON> saneerder,</p>
    <p>Aan project {{$project->projectnummer}} is binnen regisseur.nl via de koppeling met LAVS een begeleidingsbrief en vrijgavedocument toegevoegd. Het gaat om het volgende project:</p>
    <p>{{$project->adres->fullAddressLine()}}</p>

    {{--Bronnen--}}
    <table cellpadding="5" cellspacing="0" border="1" style="width: 100%; border-collapse: collapse; border: 1px solid #F0F0F0; font-size: 12px;">
        <thead style="background-color: #F0F0F0;">
        <tr>
            <th>Bronnummer</th>
            <th>Aantal</th>
            <th>Eenheid</th>
            <th>Omschrijving</th>
            <th>Risicoklasse</th>
            <th>Locatie</th>
            <th>Saneren</th>
            <th>Deelsanering</th>
            <th>Opmerking</th>
            <th>Asbesthoudend</th>
        </tr>
        </thead>
        <tbody>
        @foreach($project->asbest_bronnen as $bron)
            <tr>
                <td>{{$bron->bronnummer}}</td>
                <td>{{$bron->aantal}}</td>
                <td>{{$bron->eenheid}}</td>
                <td>{{$bron->bronomschrijving->omschrijving}}</td>
                <td>{{$bron->risicoklasse->klasse}}</td>
                <td>{{$bron->bronlocatie->locatie}}</td>
                <td>{{ucfirst($bron->saneeradvies)}}</td>
                <td>{{$bron->deelsanering ? 'Ja' : 'Nee'}}</td>
                <td>{{$bron->opmerking}}</td>
                <td>{{$bron->asbesthoudend ? 'Ja' : 'Nee'}}</td>
            </tr>
        @endforeach
        </tbody>
    </table>

    {{--Documents--}}
    <p>
        <img src="{{url('dashboard/img/icons/file.png')}}" width="15" alt="file-icon" style="vertical-align: middle;">
        <a href="{{$project->getFile('vrijgavedocument')->url()}}" style="color: #01689b; text-decoration: none;"><b>Bekijk hier het vrijgavedocument</b></a>
    </p>
    <p>
        <img src="{{url('dashboard/img/icons/file.png')}}" width="15" alt="file-icon" style="vertical-align: middle;">
        <a href="{{$project->getFile('begeleidingsbrief')->url()}}" style="color: #01689b; text-decoration: none;"><b>Bekijk hier het begeleidingsbrief</b></a>
    </p>

    {{--Buttons--}}
    <table cellpadding="0" cellspacing="0" border="0" align="center" style="width: 100%" >
        <tr>
            <td align="center" bgcolor="#01689b" style="border-radius: 5px;">
                <a href="{{url('dashboard/lavs/stap/sanering-opleveren/'.$project->guid)}}"
                   style="display: inline-block; width: 100%; font-size: 14px; color: #ffffff; text-decoration: none; padding: 10px 20px; border-radius: 5px; background-color: #01689b; border: 1px solid #01689b;">
                    JA deze bronnen zijn gesaneerd
                </a>
            </td>
        </tr>
    </table>

    <!--[if mso]>
    <table cellpadding="3" > <tr> <td></td> </tr> </table>
    <![endif]-->

    <table cellpadding="0" cellspacing="0" border="0" align="center" style="width: 100%; margin-top: 10px;">
        <tr>
            <td align="center" bgcolor="#D9E8F0" style="border-radius: 5px;">
                <a href="{{url('dashboard/login?domain='._domain()->guid.'&redirect='.urlencode('dashboard/projecten/project/'.$project->id.'?stap='.$project->stap_id))}}"
                   style="display: inline-block; width: 100%; font-size: 14px; color: #01689b; text-decoration: none; padding: 10px 20px; border-radius: 5px; background-color: #D9E8F0; border: 1px solid #D9E8F0;">
                    NEE er is een delsaneering of niet alle bronnen zijn gesaneerd
                </a>
            </td>
        </tr>
    </table>


@endsection
