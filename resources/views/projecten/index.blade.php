@extends('layouts.app')

@section('content')
    <section>

        <infor-table id="projecten_table" >

            <infor-table-filters >

                <infor-table-filter>
                    <label>Adres</label>
                    <infor-search id="adres" class="form-control" name="adres" placeholder="Selecteer adres" data-content="straat, huisnummer,toevoeging" data-sub-content="plaats" data-api="dashboard/api/search/adres"></infor-search>
                </infor-table-filter>
                <infor-table-filter>
                    <label>Proces</label>
                    <infor-select-search id="proces" name="proces" class="form-control" placeholder="Selecteer proces">
                        @foreach(getActiveProcessen() as $proces)
                            <infor-select-option data-value="{{$proces->id}}" data-name="{{$proces->naam}}" >{{$proces->naam}}</infor-select-option>
                        @endforeach
                    </infor-select-search>
                </infor-table-filter>
                <infor-table-filter>
                    <label>Stap</label>
                    <infor-select-search data-disabled id="stap" name="stap" class="form-control" placeholder="Selecteer stap">
                        @foreach(getActiveProcessen() as $proces)
                            @foreach($proces->activeStappen(['include_sub' => false]) as $stap)
                                <infor-select-option data-params=":proces={{$proces->id}}" data-value="{{$stap->id}}" data-name="{{$stap->stapnaam}}" >{{$stap->stapNr()}}. {{$stap->stapnaam}}</infor-select-option>
                            @endforeach
                        @endforeach
                    </infor-select-search>
                </infor-table-filter>
                <infor-table-filter>
                    <label>Status</label>
                    <infor-select-search id="status" name="status" class="form-control" placeholder="Selecteer status">
                        <infor-select-option data-value="ACTIELIJST" >Actielijst</infor-select-option>
                        @if(!hasPermission('projecten_volledige_actielijst') && (hasPermission('projecten_stappen_invullen_zelfde_rol') || hasPermission('projecten_stappen_inzien_zelfde_rol')))
                            <infor-select-option data-value="ACTIELIJST_BY_ROLE" >Actielijst van alle {{_role()->name}}s</infor-select-option>
                        @endif
                        <infor-select-option data-value="LOPEND" >Lopend</infor-select-option>
                        <infor-select-option data-value="GEPARKEERD" >Geparkeerd</infor-select-option>
                        <infor-select-option data-value="GEANNULEERD" >Geannuleerd</infor-select-option>
                        <infor-select-option data-value="AFGEROND" >Afgerond</infor-select-option>
                    </infor-select-search>
                </infor-table-filter>

            </infor-table-filters>


            <infor-table-count data-single="Project" data-multiple="Projecten" ></infor-table-count>


            <div class="flex-align" data-filter-badges ></div>

            <infor-table-pagination>
                <infor-table-order-option data-column="projectnummer" >Projectnummer</infor-table-order-option>
                <infor-table-order-option data-column="opdrachtnummer" >Opdrachtnummer</infor-table-order-option>
            </infor-table-pagination>


            <infor-table-header>Projectnummer</infor-table-header>
            <infor-table-header>Adres</infor-table-header>
            <infor-table-header>Plaats</infor-table-header>
            <infor-table-header>Stap</infor-table-header>
            <infor-table-header>Status</infor-table-header>
            <infor-table-header>Dagen tot deadline</infor-table-header>
            <infor-table-header>Uiterste opleverdatum</infor-table-header>
            <infor-table-header>Partij aan zet</infor-table-header>
            @foreach(setting('projecten_index_table_roles', []) as $role_id)
                <infor-table-header>{{getRole($role_id)->name ?? ''}}</infor-table-header>
            @endforeach
            <infor-table-header>Wacht op actie vanaf</infor-table-header>


        </infor-table>

    </section>
@endsection

@section('script')
    <script>

        const data = {
            processen: @json(getActiveProcessen()),
        }
        const settings = {
            projecten_index_table_roles: @json(setting('projecten_index_table_roles', []))
        }

        $(document).ready(() => {
            initTable()
            initFilters();
        })

        //Table
        function initTable(){
            initInforTables({
                id: 'projecten_table',
                api: `dashboard/api/projecten/get`,
                response_records_name: 'projecten',
                search: {
                    api: 'dashboard/api/search/project',
                    content: 'projectnummer',
                    sub_content: `straat, huisnummer, toevoeging`,
                    placeholder: `Zoeken...`,
                },
                errorHandler: handleAjaxErrors,
                fillRecord: fillRecord,
                execute: [tippyInit]
            })
        }
        function initFilters(){
            const proces_input = _inforSelectSearch.get('proces');
            const stap_input = _inforSelectSearch.get('stap');

            proces_input.onchange = proces_id => {
                stap_input.clear();
                stap_input.options().removeClass('d-none');

                proces_id ? stap_input.enable() : stap_input.disable();

                if(!proces_id){ return; }
                stap_input.options().not(`[data-proces=${proces_id}]`).addClass('d-none');
            }
        }
        function fillRecord(record){
            const { id, projectnummer, adres, status_data, stap, is_inzichtbaar, stap_users_invullen_visual, users, planmatig_key, is_planmatig, planmatige_adressen, planmatige_projecten_total_count } = record;
            const { straat, huisnummer, toevoeging, complexnummer, plaats} = (adres || {})

            return `
                <tr onclick="${is_planmatig ? `selectPlanmatig(${id})` : `redirect('dashboard/projecten/project/${id}')`}" class="cursor-pointer" >
                    <td> ${is_planmatig ? `<span class="badge badge-light" data-tippy-content="Aantal planmatige projecten" >${(planmatige_adressen.length == planmatige_projecten_total_count) ? planmatige_adressen.length : `${planmatige_adressen.length} / ${planmatige_projecten_total_count}`}</span>` : ''} ${projectnummer || ''}</td>
                    <td>
                      ${is_planmatig
                        ? `<div data-tippy-content="${planmatige_adressen.map(adres => `<div>${adres.straat || ''} ${adres.huisnummer || ''} ${adres.toevoeging || ''}</div>`).join('')}" >
                            ${ planmatige_adressen.filter((adres, index, self) => {
                                return index === self.findIndex(row => row.straat == adres.straat);
                            }).map((adres) => `<div>(${adres.complexnummer || ''}) ${adres.straat || ''}</div>` ).join('') }
                          </div>`
                        : `${complexnummer ? `(${complexnummer || ''})` : ''} ${straat || ''} ${huisnummer || ''} ${toevoeging || ''}`
                      }
                    </td>
                    <td>${plaats || ''}</td>
                    <td>
                        ${!is_inzichtbaar
                            ? `<div class="text-muted" > @icon_hide ${stap?.stapnaam || ''}</div>`
                            : `<div>${stap?.stapnaam || ''}</div>`
                        }
                        <div>${stap?.sub_stappen.length ? stap.sub_stappen.map( sub_stap => `<div class="text-muted font-size-08" >- ${sub_stap.stapnaam}</div>` ).join('') : ''}</div>
                    </td>
                    <td> <span class="badge" style="background-color: ${status_data.color}" >${status_data.key}</span> </td>
                    <td></td>
                    <td></td>
                    <td>
                        ${stap_users_invullen_visual.map(user => {
                          const { bedrijf } = user;
                          return `
                            <div>${bedrijf?.logo ? `<img height="35" class="tippy" src="${_file(bedrijf.logo)}" data-tippy-content="${bedrijf.naam || ''}" >` : (bedrijf?.naam || '')}</div>
                            <div class="text-muted" > ${user.name || ''} </div>
                          `
                        }).join('')}
                    </td>
                    ${settings.projecten_index_table_roles.map(role_id => {
                        const user = users.find(user => user.role?.id == role_id);
                        return `<td class="nobr" >${user?.name || ''}</td>`
                    }).join('')}
                    <td></td>
                </tr>
            `
        }

        //Planmatig
        function selectPlanmatig(project_id){
            const project = _inforTables.get('projecten_table').records.find(project => project.id == project_id);
            const { projectnummer, stap_id, planmatig_key, status_data } = project;

            $('infor-table-filters').append(`
                <infor-table-filter class="d-none" data-planmatige-filter="${planmatig_key}" >
                  <input type="hidden" name="planmatig_key" value="${planmatig_key}" >
                  <input type="hidden" name="stap" value="${stap_id}" >
                  <input type="hidden" name="status" value="${status_data.key}" >
                </infor-table-filter>
            `);
            $('[data-filter-badges]').append(`
                <div class="bg-white border btn flex-between font font-size-075 hover-mark rounded-pill" data-planmatige-filter="${planmatig_key}" onclick="deselectPlanmatig('${planmatig_key}')" >
                    <span class="mr-2" >Planmatige Groep: ${projectnummer}</span>
                    @icon_close
                  </div>
            `)

            _inforTables.get('projecten_table').refresh()
        }
        function deselectPlanmatig(planmatig_key){
            $(`[data-planmatige-filter=${planmatig_key}]`).remove();
            _inforTables.get('projecten_table').refresh()
        }

    </script>
@endsection
