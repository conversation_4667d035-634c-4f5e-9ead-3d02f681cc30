@extends('layouts.app')
@section('content')
    <section>

        <div class="d-flex my-2">

            {{--Project Info && Inputs--}}
            <div class="min-w-0 flex-grow-1 mr-3" >

                <div class="flex-between">

                    <div class="flex-center mx--1">
                        <a class="btn cursor-unset" >{{$project->projectnummer ?? $project->opdrachtnummer}}</a>
                        <div class="form-control-divider mx-1" ></div>
                        <a class="btn hover-mark" href="{{url("/dashboard/adressen/adres/{$adres->id}")}}" target="_blank" > {{$adres->addressLine()}}, {{$adres->plaats}} <span class="font-size-075" >@icon_external_link</span> </a>
                        @if($adres->complexnummer)
                            <div class="form-control-divider mx-1" ></div>
                            <a class="btn hover-mark" href="{{url("/dashboard/complexen/{$adres->complexnummer}")}}" target="_blank" >{{$adres->complexnummer}} <span class="font-size-075" >@icon_external_link</span> </a>
                        @endif
                    </div>

                    <div class="flex-center mx--1" >
                        @if($project->actief)
                            @if(hasPermission('projecten_change_status'))
                                <a class="btn text-danger" data-status="GEANNULEERD" >Annuleren</a>
                                <a class="btn text-danger" data-status="GEPARKEERD" >Parkeren</a>
                            @endif
                            @if(hasPermission('projecten_change_status') && hasPermission('projecten_revert_steps'))<div class="form-control-divider mx-1"> </div>@endif
                            @if(hasPermission('projecten_revert_steps'))<a class="btn text-primary" data-revert >Stap Terugzetten</a>@endif
                        @else
                            @if(hasPermission('projecten_change_status'))<a class="btn text-success" data-status="LOPEND" >Activeren</a>@endif
                        @endif
                    </div>

                </div>

                <livewire:projecten.stappen.info :manager="$manager" :project="$project" :adres="$adres" :bewoner="$bewoner" ></livewire:projecten.stappen.info>

                <div class="flex-center my-4">
                    <div class="w-100 border-bottom"></div>
                    <div class="mx-2 text-muted"> <h3 class="m-0" >{{$invulbaar ? 'Invullen' : 'Inzien'}}</h3> </div>
                    <div class="w-100 border-bottom"></div>
                </div>

                <livewire:projecten.stappen.inputs :manager="$manager" :project="$project" :adres="$adres" :bewoner="$bewoner" :invulbaar="$invulbaar" ></livewire:projecten.stappen.inputs>

            </div>

            {{--Project Flow--}}
            <div class="ml-3 pt-5" >
                <livewire:projecten.stappen.flow :manager="$manager" :project="$project" ></livewire:projecten.stappen.flow>
            </div>

        </div>

    </section>
@endsection
@section('script')
    <script>

        const data = {
            planmatig: @json($manager->planmatig),
            planmatige_adressen: @json($project->planmatigeAdressen(['include_project' => ['id']])),
            project: @json($project),
            stap: @json($manager->stap),
            stappen: @json(getActiveStappen($project->proces_id, ['include_sub' => false])),
        }

        $(document).on('click', '[data-status]', function () {
            const status = $(this).attr('data-status');

            confirmModal({
                text: data.planmatig
                    ? `
                            <div class="my-2" >
                              <label>Planmatige adressen selecteren</label>
                                <infor-select-multiple id="park-projecten" class="form-control" placeholder="Selecteer stap"  >
                                    ${data.planmatige_adressen.map(adres => {
                        return `<infor-select-option ${adres.project.id == data.project.id ? 'data-selected data-locked' : ''} data-name="${adres.address_line}" data-value="${adres.project.id}">${adres.address_line}</infor-select-option>`
                    }).join('')}
                                </infor-select-multiple>
                            </div>`
                    : `Weet je zeker dat je de status van dit project wilt veranderen naar <b>${status.toLowerCase()}</b>?`
                ,

            })
                .then(response => {
                    if (!response.status) {
                        return;
                    }

                    const project = data.planmatig
                        ? _inforSelectMultiple.get('park-projecten').getValues().map(option => option.value)
                        : data.project.id

                    loader();
                    ajax('dashboard/api/projecten/set/status', {
                        project: project,
                        status: status,
                    })
                        .then(() => {
                            notification('Status succesvol gewijzigd!', 'success');
                            window.location.reload();
                        })
                        .catch(handleAjaxErrors);
                })

            initInforSelectSearch();
            initInforSelectMultiple();
        })
        $(document).on('click', '[data-revert]', async function () {
            try{
                const project_stap = data.stappen.find(stap => stap.id == data.project.stap_id);
                const previous_stappen = data.stappen.filter(stap => {
                    return Number(stap.stap_order) < Number(project_stap.stap_order)
                })

                const {status, inputs} = await confirmModal({
                    execute: () => {
                        initInforSelectSearch();
                        initInforSelectMultiple();
                    },
                    text: `
                    ${data.planmatig
                        ? `
                        <div class="my-2" >
                          <label>Planmatige adressen selecteren*</label>
                            <infor-select-multiple name="projecten" id="revert-projecten" class="form-control" placeholder="Selecteer stap"  >
                                ${data.planmatige_adressen.map(adres => {
                            return `<infor-select-option ${adres.project.id == data.project.id ? 'data-selected data-locked' : ''} data-name="${adres.address_line}" data-value="${adres.project.id}">${adres.address_line}</infor-select-option>`
                        }).join('')}
                            </infor-select-multiple>
                        </div>`
                        : ''
                    }
                    <div class="my-2" >
                      <label>Stap Selecteren*</label>
                        <infor-select-search name="stap" id="revert-stap" class="form-control" placeholder="Selecteer stap"  >
                            ${previous_stappen.map(stap => {
                            return `<infor-select-option data-name="${stap.stapnaam}" data-value="${stap.id}">${stap.stapnaam}</infor-select-option>`
                        }).join('')}
                        </infor-select-search>
                    </div>
                    <div class="my-2" >
                      <label>Reden*</label>
                      <textarea name="reason" class="form-control" rows="4" placeholder="Reden"></textarea>
                    </div>`,

                });
                if (!status) { return; }

                let {projecten, stap, reason} = inputs;
                if (!stap || !reason) {
                    notification('Vul alle verplichte velden in!');
                    return;
                }

                if(data.planmatig){
                    projecten = projecten.filter(project => project.checked);
                    projecten = projecten.map(project => project.value);
                }

                await internalMessageSend('revert-stap', {
                    projecten: data.planmatig ? projecten : [ data.project.id ],
                    stap: stap,
                    reason: reason,
                })

                notification('Project succesvol teruggezet!', 'success');
                window.location.reload();
            }
            catch (e) { handleAjaxErrors(e) }
        });
        $(document).on('click', '[data-stap]', function () {
            $('main').html(`@page_loader`)
            const id = $(this).attr('data-stap');
            redirect(`/dashboard/projecten/project/${data.project.id}?stap=${id}`);
        });

    </script>
@endsection
