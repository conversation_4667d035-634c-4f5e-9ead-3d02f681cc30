<section>

    @if($invulbaar)
        <div>
            @if($input->dataValue('deelsaneren'))
                <div>- Klik op <b class="text-primary p-1" >@icon_arrows_split_right_and_down</b> als je de bron wilt laten deelsaneren.</div>
                <div>- Klik op <b class="text-danger p-1" >@icon_close</b> als je de deelsanering wilt annuleren.</div>
            @endif
            @if($livewire->inputExists('historische_bronnen'))
                <div>- Klik op <b class="text-warning p-1" >@icon_arrow_up</b> als je de historisch toegevoegde bron wilt terugzetten.</div>
            @endif
        </div>
    @endif

    <div class="overflow-auto">
        <table class="table">
            <thead>
            <tr>
                @if($invulbaar) <th class="w-0" ></th> @endif
                @foreach($input->sub_inputs as $asbest_input)
                    <th>{!! $asbest_input->HTMLLabel() !!}</th>
                @endforeach
            </tr>
            </thead>
            <tbody>
            @forelse($livewire->asbest_bronnen as $index => $bron)
                <tr>

                    @if($invulbaar)
                        <td class="nobr" >
                            @if($input->dataValue('deelsaneren') && !$livewire->planmatig)
                                @if(!$bron->deelsanering && !$bron->deelsanering_child)
                                    <a class="btn btn-td-primary" wire:click="deelsaneringAsbestBron({{$bron->id}})" data-tippy-content="Deelsanering" >@icon_arrows_split_right_and_down</a>
                                @endif
                                @if($bron->deelsanering_child)
                                    <a class="btn btn-td-danger" wire:click="deleteDeelsaneringAsbestBron({{$bron->id}})" data-tippy-content="Deelsanering verwjderen" >@icon_close</a>
                                @endif
                            @endif
                            @if($input->dataValue('edit'))
                                <a class="btn btn-td-danger" wire:click="deleteAsbestBron({{$bron->id}})" >@icon_close</a>
                            @endif
                            @if($bron->recentlyTransferdToProject($livewire->planmatig ? $livewire->project->planmatig_key : $livewire->project->id) && !$bron->deelsanering_child && $livewire->inputExists('historische_bronnen'))
                                <a class="btn btn-td-warning" wire:click="revertHistorischeBron({{$bron->id}})" data-tippy-content="Terugzetten naar historische bronnen" >@icon_arrow_up</a>
                            @endif
                        </td>
                    @endif

                    @foreach($input->sub_inputs as $asbest_input)
                        <td class="min-w-150">
                            {!! $asbest_input->HTMLInput([
                                    'livewire' => $livewire,
                                    'index' => $index,
                                    'data' => ($bron->deelsanering_child ? 'disabled' : '')
                            ]) !!}

                            @if($asbest_input->db_input->veldnaam == 'aantal' && $bron->deelsanering_child)
                                <input type="number" class="form-control mt-2" placeholder="Hoeveel moet er gesaneerd worden?"
                                       data-tippy-content="Hoeveel moet er gesaneerd worden?"
                                       value="{{$bron->deelsanering_child->aantal}}"
                                       data-deelsanering_aantal="{{$bron->id}}"
                                       @if($invulbaar) wire:change="setDeelsaneringAantal({{$bron->id}}, $event.target.value)" @else disabled @endif
                                >
                            @endif

                        </td>
                    @endforeach
                </tr>
            @empty
                <tr>
                    <td colspan="100" class="text-center text-muted" >Klik op de onderstaande knop om een bron toe te voegen.</td>
                </tr>
            @endforelse
            </tbody>
        </table>
    </div>
    @if($invulbaar && $input->dataValue('edit'))
        <div class="text-center py-2" >
            <a class="btn btn-inverse-primary" wire:click="addAsbestBron">Bron toevoegen @icon_plus</a>
        </div>
    @endif
</section>
