<input
        class="form-control"
        type="file"
        placeholder="{{$db_input->placeholder}}"
        multiple
        wire:model="{{$model}}"
        {{$data}}
>
@foreach($livewire->project->getFiles($db_input->veldnaam) as $file)
    <div class="flex-between cursor-pointer my-1 p-1 rounded-5 hover-mark">
        <div class="w-100" onclick="redirectURL(_file('{{$file->guid}}'), 0, true)" >
            <img height="40" src="{{$file->icon()}}">
            <span class="text-muted"  >{{ $file->name }}</span>
        </div>

        @if($livewire->invulbaar)
            <a class="btn text-danger" wire:click="removeProjectenFile('{{$db_input->veldnaam}}', {{$file->id}})" >@icon_close</a>
        @endif
    </div>
@endforeach
