<section>

    @if($livewire->historische_bronnen->count() && $invulbaar)
        <div>
            <div><b>Op dit adres zijn nog niet gesaneerde bronnen @if($input_data->valid_report ?? false) ( met een geldig rapport ) @endif uit eerdere projecten aanwezig!</b></div>
            @if($livewire->getStapSettingValue('HISTORISCHE_BRONNEN_GESANEERD'))
                <div>- Klik op <b class="text-success p-1" >@icon_check</b> als de bron al eerder gesaneerd is</div>
            @endif
            <div>- Klik op <b class="text-primary p-1" >@icon_arrow_down</b> om de bron mee te nemen in dit project om te saneren</div>
        </div>
    @endif

    <div class="overflow-auto">
        <table class="table">
            <thead>
                <tr>
                    @if($invulbaar) <th class="w-0" ></th> @endif
                    @foreach($input->sub_inputs as $sub_input)
                        <th>{!! $sub_input->HTMLLabel() !!}</th>
                    @endforeach
                </tr>
            </thead>
            <tbody>
            @forelse($livewire->historische_bronnen as $index => $bron)
                <tr>
                    @if($invulbaar)
                        <td class="nobr" >
                            <a class="btn btn-td-primary" wire:click="transferHistorischeBron({{$bron->id}}, false)" data-tippy-content="Toevoegen aan het huidige project" >@icon_arrow_down</a>
                            @if($livewire->getStapSettingValue('HISTORISCHE_BRONNEN_GESANEERD'))
                                <a class="btn btn-td-success" onclick="confirmHistorischeBronGesaneerd({{$bron->id}})" data-tippy-content="Bron gesaneerd" >@icon_check</a>
                            @endif
                        </td>
                    @endif
                    @foreach($input->sub_inputs as $sub_input)
                        <td>
                            {!!
                                $sub_input->HTMLInput([
                                    'livewire' => $livewire,
                                    'index' => $index,
                                ])
                           !!}
                        </td>
                    @endforeach
                </tr>
            @empty
                <tr>
                    <td colspan="100" class="py-3 text-center" >Geen historische bronnen gevonden!</td>
                </tr>
            @endforelse
            </tbody>
        </table>
    </div>

    <script>

        function confirmHistorischeBronGesaneerd(bron_id){
            confirmModal({
                text: 'Weet je zeker dat deze bron gesaneerd is?'
            })
                .then(response => {
                   if(!response.status){ return; }
                   @this.compleetHistorischeBron(bron_id);
                });
        }

    </script>

</section>
