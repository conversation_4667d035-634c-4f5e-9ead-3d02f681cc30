<div class="overflow-auto">
    <table class="table">
        <thead>
            <tr>
                <th></th>
                @foreach($livewire->asbest_bronnen as $bron)
                    <th>{{$bron->bronnummer}}</th>
                @endforeach
            </tr>
        </thead>
        <tbody>
            @foreach($invulbaar ? $livewire->planmatige_projecten : $livewire->project->planmatigeProjecten() as $planmatig_project)
                <tr>
                    <td class="w-100" >{{$planmatig_project->adres->addressLine()}}</td>
                    @foreach($livewire->asbest_bronnen as $bron)
                        @php $planmatig_bron = $livewire->getPlanmatigeAsbestBron($bron->id, $planmatig_project->id) @endphp
                        <td>
                            <div class="form-control flex-between w-px-150 @if(!$planmatig_bron || !$invulbaar) bg-inverse-secondary @endif">
                                @if($invulbaar)
                                    <input type="number" step="0.01" class="form-control-plaintext p-0" placeholder="0.00" wire:change="setPlanatigAsbestBron({{$planmatig_project->id}}, {{$bron->id}}, {{$planmatig_bron->id ?? 'null'}}, $event.target.value)" value="{{optional($planmatig_bron)->aantal}}" >
                                @else
                                    <div class="w-100">{{$planmatig_bron->aantal ?? null}}</div>
                                @endif
                                <div class="form-control-divider mx-2"></div>
                                <span class="@if(!$planmatig_bron) opacity-50 @endif" >{{$bron->eenheid}}</span>
                            </div>
                        </td>
                    @endforeach
                </tr>
            @endforeach
        </tbody>
    </table>
</div>
