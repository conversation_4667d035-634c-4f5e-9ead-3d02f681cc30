<section>
  <div class="table-overflow">

    <table class="table">
      <thead>
        <tr>
          <th colspan="{{$invulbaar ? 2 : 1}}" >Adres</th>
          @foreach($livewire->asbest_bronnen->where('referentie', 1) as $referentiebron)
            <th class="w-0" >
              @if($referentiebron->bronnummer)
                {{$referentiebron->bronnummer}}
              @else
                <span class="opacity-33 nobr" >Geen Nr</span>
              @endif
            </th>
          @endforeach
        </tr>
      </thead>
      <tbody>

      {{--Complex Adressen--}}
      @if($livewire->adres->complexAdressen()->count())
        <tr class="bg-inverse-secondary font-weight-bold" >
          <td colspan="100" >Complex adressen</td>
        </tr>
      @endif
      @foreach($livewire->adres->complexAdressen() as $adres)
        <tr>
          <td colspan="{{$invulbaar ? 2 : 1}}" >
              <div class="py-2" >{{$adres->addressLine()}}</div>
          </td>
          @foreach($livewire->asbest_bronnen->where('referentie', 1) as $bron)
            <td>
              <label class="m-0 p-2 rounded-5 hover-mark cursor-pointer" >
                <input
                    @if(!$invulbaar) disabled @endif
                    @if($livewire->hasReferentiebron($adres->id, $bron->id)) checked @endif
                    @if($invulbaar) wire:change="setReferentieBron({{$adres->id}}, {{$bron->id}})" @endif
                    type="checkbox" class="form-switch-custom"
                >
              </label>
            </td>
          @endforeach
        </tr>
      @endforeach

      {{--Losse Adressen--}}
      @if($livewire->referentie_bronnen_adressen->count())
        <tr class="bg-inverse-secondary font-weight-bold" >
          <td colspan="100" >Losse adressen</td>
        </tr>
      @endif
      @foreach($livewire->referentie_bronnen_adressen as $referentie_adres)
        <tr>
          @if($invulbaar)
            <td class="w-0" >
              <a class="btn btn-td-danger" wire:click="deleteReferentieBronAdres({{$referentie_adres->id}})" >@icon_close</a>
            </td>
          @endif
          <td>
            <div class="ml--25" >
              @if(!$referentie_adres->adres)
                <infor-search
                  data-livewire="setReferentieBronAdres" data-livewire-params=":referentie_adres_id={{$referentie_adres->id}}"
                  class="form-control" placeholder="Selecteer adres"
                  data-content="straat, huisnummer, toevoeging" data-sub-content="plaats"
                  data-api="dashboard/api/search/adres"
                >
                </infor-search>
              @else
                <div class="form-control hover-mark cursor-pointer border-0" @if($invulbaar) wire:click="resetReferentieBronAdres({{$referentie_adres->id}})" @endif >{{$referentie_adres->adres->addressLine()}}</div>
              @endif
            </div>
          </td>
          @foreach($livewire->asbest_bronnen->where('referentie', 1) as $bron)
            <td>
              <label class="form-switch-label" >
                <input
                    @if(!$referentie_adres->adres_id || !$invulbaar) disabled @endif
                    @if($livewire->hasReferentiebron($referentie_adres->adres_id, $bron->id)) checked @endif
                    @if($invulbaar) wire:change="setReferentieBron({{$referentie_adres->adres_id}}, {{$bron->id}}, {{$referentie_adres->id}})" @endif
                    type="checkbox" class="form-switch-custom" >
              </label>
            </td>
          @endforeach
        </tr>
      @endforeach

      </tbody>
    </table>

    @if($invulbaar)
      <div class="text-center">
        <a class="btn btn-inverse-primary" wire:click="addReferentieBronAdres()" >Adres toevoegen @icon_plus</a>
      </div>
    @endif

  </div>
</section>
