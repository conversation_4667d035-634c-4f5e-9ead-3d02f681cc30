<section>

    <div class="row">
        @foreach($input->sub_inputs as $sub_input)
            <div class="col-md-6 col-12 my-2 @if(!$livewire->inputVisible($sub_input)) d-none @endif ">
                {!! $sub_input->HTMLLabel() !!}
                {!! $sub_input->HTMLInput(['livewire' => $livewire]) !!}
            </div>
        @endforeach
    </div>

    <div class="my-2">
        <div class="overflow-auto">
            <table class="table">

                <thead>
                <tr>
                    @if($invulbaar)
                        <th class="w-0"></th>
                    @endif
                    <th class="min-w-200">Locatie</th>
                    <th class="min-w-200">Opmerking</th>
                    @foreach(getBronOmschrijvingen(['where' => ['opdrachtformulier' => 1]]) as $omschrijving)
                        <th class="nobr">{{$omschrijving->omschrijving}}</th>
                    @endforeach
                </tr>
                </thead>
                <tbody>
                @forelse($livewire->opdrachtformulier_locaties_koppeling as $l => $locatie_koppeling)
                    <tr>
                        @if($invulbaar)
                            <td>
                                <a class="btn btn-td-danger"
                                   wire:click="deleteOpdrachtformulierLocatie({{$locatie_koppeling->id}})">@icon_close</a>
                            </td>
                        @endif
                        <td>
                            <infor-select-search class="form-control" placeholder="Selecteer locatie" data-livewire="setOpdrachtformulierLocatie" data-livewire-params=":locatie_koppeling_id={{$locatie_koppeling->id}}" @if(!$invulbaar) disabled @endif >
                                @foreach(getBronLocaties(['where' => ['opdrachtformulier' => 1]]) as $locatie)
                                    <infor-select-option @if($locatie->id == $locatie_koppeling->locatie_id) data-selected @endif data-value="{{$locatie->id}}">{{$locatie->locatie}}</infor-select-option>
                                @endforeach
                            </infor-select-search>
                        </td>
                        <td>
                            <input type="text" class="form-control"
                                   wire:model="opdrachtformulier_locaties_koppeling.{{$l}}.opmerking"
                                   placeholder="Opmerking" @if(!$invulbaar) disabled @endif >
                        </td>
                        @foreach(getBronOmschrijvingen(['where' => ['opdrachtformulier' => 1]]) as $bron_omschrijving)
                            <td>
                                <label class="form-switch-label">
                                    <input type="checkbox" class="form-switch-custom"
                                           wire:change="setOpdrachtformulierBron({{$locatie_koppeling->locatie_id}}, {{$bron_omschrijving->id}})"
                                           @if($livewire->hasOpdrachtformulierBronLocatie($locatie_koppeling->locatie_id, $bron_omschrijving->id)) checked
                                           @endif
                                           @if(!$invulbaar) disabled @endif
                                    >
                                </label>
                            </td>
                        @endforeach
                    </tr>
                @empty
                    <tr>
                       <td colspan="100" class="text-muted text-center py-3" >Geen locaties gevonden</td>
                    </tr>
                @endforelse
                </tbody>
            </table>
        </div>

        @if($invulbaar)
            <div class="text-center mb-2 mt-3">
                <a class="btn btn-inverse-primary" wire:click="addOpdrachtformulierLocatie()">Locatie @icon_plus</a>
            </div>
        @endif

    </div>

</section>
