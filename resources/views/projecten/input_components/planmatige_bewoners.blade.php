<div>

    @if(!$livewire->planmatige_bewoners->count())
        <div class="text-muted" ><PERSON><PERSON> <PERSON><PERSON><PERSON> geselecteerd</div>
    @else
        @foreach($livewire->planmatige_bewoners as $index => $bewoner)
            <div class="border rounded-5 p-2 my-2">
                <div>Adres: {{$bewoner->project->adres->addressLine()}}</div>
                @foreach($input->sub_inputs as $bewoner_input)
                    <div class="my-2">
                        {!! $bewoner_input->HTMLLabel() !!}
                        {!! $bewoner_input->HTMLInput([
                                'livewire' => $livewire,
                                'index' => $index,
                        ]) !!}
                    </div>
                @endforeach

            </div>
        @endforeach
    @endif


</div>
