@extends('layouts.app')

@section('content')
<section>
    <infor-table id="complexen_table">


        <infor-table-pagination>
            <infor-table-order-option data-column="complexnummer">{{label('Complex')}}nummer</infor-table-order-option>
            <infor-table-order-option data-column="Plaats">Plaats</infor-table-order-option>
        </infor-table-pagination>

        <infor-table-count data-single="{{label('Complex')}}" data-multiple="{{label('Complexen')}}"></infor-table-count>

        <infor-table-header>{{label('Complex')}}nummer</infor-table-header>
        <infor-table-header>Plaats</infor-table-header>
        <infor-table-header>Adressen</infor-table-header>
        <infor-table-header>Aantal Adressen</infor-table-header>


    </infor-table>
</section>
@endsection

@section('script')
    <script>

      pageInteractive(() => {
        initInforTables({
          id: 'complexen_table',
          api: `/dashboard/api/complexen/get`,
          response_records_name: 'complexen',
          search: {
            api: 'dashboard/api/search/complex',
            content: 'complexnummer',
            sub_content: 'plaats',
            placeholder: `Zoeken...`,
          },
          errorHandler: handleAjaxErrors,
          fillRecord: fillRecord
        })
      })

      $(document).on('click', '[data-complex]', function () {
        const complexnummer = $(this).attr('data-complex');
        window.location.href = `/dashboard/complexen/${complexnummer}`;
      })

      function fillRecord(record) {
        const {complexnummer, plaats, postcodes, adressen} = record;
        return `
                <tr class="cursor-pointer" data-complex="${complexnummer}" >
                    <td>${complexnummer || ''}</td>
                    <td> ${plaats || ''}</td>
                    <td>${postcodes.length ? `
                            ${postcodes.map(adres => {
                                return `<div> ${adres.postcode || ''}, ${adres.straat || ''} </div>`
                            }).join('')}` : ''}
                    </td>
                    <td> ${adressen.length || ''}</td>
                </tr>
            `
      }
    </script>
@endsection
