@extends('layouts.app')

@section('content')

    {{--Complex info--}}
    <livewire:complexen.info :complex="$complex" ></livewire:complexen.info>

    {{--Documenten--}}
    @foreach(getActiveProcessen()->unique('asbest') as $proces)
        @if(!$complex->getInstellingenFiles($proces->type)->count()) @continue @endif

        <section class="card" data-toggle-container >
            <div class="flex-between hover-mark rounded-3 px-2 py-1 cursor-pointer" data-toggle-btn >
                <div class="card-title flex-align">
                    <span>Documenten</span>
                    <span class="badge mx-2" style="background-color: {{$proces->statusInfo('color')}}" >{{$proces->statusInfo('title')}}</span>
                </div>
                <a class="btn btn-sm" >@icon_down</a>
            </div>
            <div  data-toggle-content class="w-100 px-2" style="display: none" >
                <livewire:complexen.documenten :complex="$complex" :proces_type="$proces->type" wire:key="proces-{{$proces->type}}" ></livewire:complexen.documenten>
            </div>
        </section>
    @endforeach

    {{--Matrix--}}
    <livewire:complexen.asbest-bronnen-matrix :complex="$complex" ></livewire:complexen.asbest-bronnen-matrix>

    {{--Asbest Bronnen--}}
    <section class="card" data-toggle-container >
        <div class="flex-between hover-mark rounded-3 px-2 py-1 cursor-pointer" data-toggle-btn >
            <div class="card-title">Aangetroffen Bronnen</div>
            <a class="btn btn-sm" >@icon_down</a>
        </div>
        <div  data-toggle-content class="overflow w-100" style="display: none" >
            <livewire:asbest.bronnen-table :bronnen="$complex->asbestBronnen()" :single_adres="false" ></livewire:asbest.bronnen-table>
        </div>
    </section>

    {{--Projecten--}}
    <section class="card" data-toggle-container >
        <div class="flex-between hover-mark rounded-3 px-2 py-1 cursor-pointer" data-toggle-btn >
            <div class="card-title">Projecten</div>
            <a class="btn btn-sm" >@icon_down</a>
        </div>
        <div  data-toggle-content class="w-100" style="display: none" >
            <livewire:projecten.projecten-table :projecten="$complex->projecten()" :single_adres="false" ></livewire:projecten.projecten-table>
        </div>
    </section>

    {{--Projecten--}}
    <section class="card" data-toggle-container >
        <div class="flex-between hover-mark rounded-3 px-2 py-1 cursor-pointer" data-toggle-btn >
            <div class="card-title">{{label('Adressen')}}</div>
            <a class="btn btn-sm" >@icon_down</a>
        </div>
        <div  data-toggle-content class="w-100" style="display: none" >
            <livewire:adressen.adressen-table :adressen="$complex->adressen()" :single_adres="false" ></livewire:adressen.adressen-table>
        </div>
    </section>



@endsection
@section('script')
    <script>


    </script>
@endsection
