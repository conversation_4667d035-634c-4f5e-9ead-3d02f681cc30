@extends('layouts.app')

@section('content')
    <section>

        <infor-table id="projecten_table" >

            <infor-table-filters >

                <infor-table-filter>
                    <label>Locatie</label>
                    <infor-select-multiple class="form-control" name="locatie" placeholder="Selecteer locaties" >
                        @foreach(getBronLocaties(['where' => ['bronnen' => 1]]) as $locatie)
                            <infor-select-option data-value="{{$locatie->id}}" >{{$locatie->locatie}}</infor-select-option>
                        @endforeach
                    </infor-select-multiple>
                </infor-table-filter>
                <infor-table-filter>
                    <label>Omschrijving</label>
                    <infor-select-multiple class="form-control" name="omschrijving" placeholder="Selecteer omschrijving" >
                        @foreach(getBronOmschrijvingen(['where' => ['bronnen' => 1]]) as $locatie)
                            <infor-select-option data-value="{{$locatie->id}}" >{{$locatie->omschrijving}}</infor-select-option>
                        @endforeach
                    </infor-select-multiple>
                </infor-table-filter>
                <infor-table-filter>
                    <label>Risicoklasse</label>
                    <infor-select-multiple class="form-control" name="risicoklasse" placeholder="Selecteer risicoklasse" >
                        @foreach(getBronRisicoKlassen(['where' => ['bronnen' => 1]]) as $locatie)
                            <infor-select-option data-value="{{$locatie->id}}" >{{$locatie->klasse}}</infor-select-option>
                        @endforeach
                    </infor-select-multiple>
                </infor-table-filter>
                <infor-table-filter>
                    <label>ZAV</label>
                    <infor-select-multiple class="form-control" name="zav" placeholder="Selecteer risicoklasse" >
                        <infor-select-option data-value="ja" >Ja</infor-select-option>
                        <infor-select-option data-value="nee" >Nee</infor-select-option>
                        <infor-select-option data-value="onbekend" >Onbekend</infor-select-option>
                    </infor-select-multiple>
                </infor-table-filter>
                <infor-table-filter>
                    <label>Asbesthoudend</label>
                    <infor-select-multiple class="form-control" name="asbesthoudend" placeholder="Selecteer asbesthoudend" >
                        <infor-select-option data-value="1" >Ja</infor-select-option>
                        <infor-select-option data-value="0" >Nee</infor-select-option>
                    </infor-select-multiple>
                </infor-table-filter>
                <infor-table-filter>
                    <label>Gesaneerd</label>
                    <infor-select-multiple class="form-control" name="gesaneerd" placeholder="Selecteer gesaneerd" >
                        <infor-select-option data-value="1" >Ja</infor-select-option>
                        <infor-select-option data-value="0" >Nee</infor-select-option>
                    </infor-select-multiple>
                </infor-table-filter>

            </infor-table-filters>

            <infor-table-count data-single="Asbest Bron" data-multiple="Asbest Bronnen" ></infor-table-count>

            <infor-table-pagination>
                <infor-table-order-option data-column="bronnummer" >Bronnummer</infor-table-order-option>
                <infor-table-order-option data-column="aantal" >Aantal</infor-table-order-option>
                <infor-table-order-option data-column="eenheid" >Eenheid</infor-table-order-option>
                <infor-table-order-option data-column="asbesthoudend" >Asbesthoudend</infor-table-order-option>
                <infor-table-order-option data-column="saneeradvies" >Saneren</infor-table-order-option>
                <infor-table-order-option data-column="gesaneerd" >Gesaneerd</infor-table-order-option>
            </infor-table-pagination>

            <infor-table-header>Locatie</infor-table-header>
            <infor-table-header>Omschrijving</infor-table-header>
            <infor-table-header>Risicoklasse</infor-table-header>
            <infor-table-header>ZAV</infor-table-header>
            <infor-table-header>Bronnummer</infor-table-header>
            <infor-table-header>Aantal</infor-table-header>
            <infor-table-header>Eenheid</infor-table-header>
            <infor-table-header>Asbesthoudend</infor-table-header>
            <infor-table-header>Saneren</infor-table-header>
            <infor-table-header>Gesaneerd</infor-table-header>
            <infor-table-header>Complex</infor-table-header>
            <infor-table-header>Adres</infor-table-header>
            <infor-table-header>Postcode</infor-table-header>
            <infor-table-header>Plaats</infor-table-header>
            <infor-table-header>Project</infor-table-header>

        </infor-table>

    </section>
@endsection

@section('script')
    <script>

        const data = {
            processen: @json(getActiveProcessen()),
        }
        const settings = {
            projecten_index_table_roles: @json(setting('projecten_index_table_roles', []))
        }

        $(document).ready(() => {
            initInforTables({
                id: 'projecten_table',
                api: `/dashboard/api/asbest/bronnen/get`,
                response_records_name: 'asbest_bronnen',
                search: {
                    api: 'dashboard/api/search/asbest_bronnen',
                    content: 'bronnummer',
                    sub_content: 'locatie, omschrijving',
                    placeholder: `Zoeken...`,
                },
                errorHandler: handleAjaxErrors,
                fillRecord: fillRecord,
                execute: [tippyInit]
            })
        })

        function fillRecord(record){
            const { project, adres, bronlocatie, bronomschrijving, risicoklasse, zav, bronnummer, aantal, eenheid, asbesthoudend, saneeradvies, gesaneerd } = record;
            const { complex, straat, huisnummer, toevoeging, postcode, plaats } = adres || {};

            return `
                <tr>

                    <td>${bronlocatie?.locatie || ''}</td>
                    <td>${bronomschrijving?.omschrijving || ''}</td>
                    <td>${risicoklasse?.klasse || ''}</td>
                    <td>${zav || ''}</td>
                    <td>${bronnummer || ''}</td>
                    <td>${aantal || ''}</td>
                    <td>${eenheid || ''}</td>
                    <td>${asbesthoudend !== null ? (Number(asbesthoudend) ? 'Ja' : 'Nee') : ''}</td>
                    <td>${saneeradvies || ''}</td>
                    <td>${Number(gesaneerd) ? 'Ja' : 'Nee' }</td>
                    <td>${complex || ''}</td>
                    <td>${straat || '' } ${huisnummer || ''} ${toevoeging || ''}</td>
                    <td>${postcode || ''}</td>
                    <td>${plaats || ''}</td>
                    <td>${project?.projectnummer || ''}</td>
                </tr>
            `
        }


    </script>
@endsection
