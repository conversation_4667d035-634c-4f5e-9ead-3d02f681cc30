@extends('layouts.app')

@section('content')
    <section>

        <infor-table id="gebruikers_table" >

            <infor-table-pagination>
                <infor-table-order-option data-column="name" >Naam</infor-table-order-option>
                <infor-table-order-option data-column="email" >Email</infor-table-order-option>
            </infor-table-pagination>

            <infor-table-count data-single="Gebruiker" data-multiple="Gebruikers" ></infor-table-count>

            <infor-table-header>Naam</infor-table-header>
            <infor-table-header>Email</infor-table-header>
            <infor-table-header>Telefoon</infor-table-header>
            <infor-table-header>Role</infor-table-header>
            <infor-table-header>Bedrijfsnaam</infor-table-header>
            <infor-table-header>Logo</infor-table-header>


        </infor-table>

    </section>
@endsection

@section('script')
    <script>

        pageInteractive(() => {
          initInforTables({
            id: 'gebruikers_table',
            api: `/dashboard/api/gebruikers/get`,
            response_records_name: 'gebruikers',
            search: {
              api: 'dashboard/api/search/gebruiker',
              content: 'name',
              sub_content: `email`,
              placeholder: `Zoeken...`,
            },
            errorHandler: handleAjaxErrors,
            fillRecord: fillRecord,
            execute: [ tippyInit ],
          })
        })

        function fillRecord(record){
            const { id, name, email, role, bedrijf, telefoon} = record;

            return `
                <tr class="cursor-pointer hover-mark" onclick="redirect('/gebruikers/edit/${id}')" >
                    <td>${name || ''}</td>
                    <td>${email || ''}</td>
                    <td>${telefoon || ''}</td>
                    <td>${role?.name || ''}</td>
                    <td>${bedrijf?.naam || ''}</td>
                    <td>${bedrijf?.logo ? `<img height="35" src="${_file(bedrijf.logo)}" >` : ''}</td>
                </tr>
            `
        }

    </script>
@endsection
