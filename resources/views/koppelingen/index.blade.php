@extends('layouts.app')

@section('content')
    <section>

        <infor-table id="koppelingen_table" >

            <infor-table-filters >

                <infor-table-filter>
                    <label><PERSON>ppeling</label>
                    <infor-select-search id="koppeling" name="koppeling" class="form-control" placeholder="Selecteer Koppeling">
                        @foreach(getKoppelingen() as $koppeling)
                            <infor-select-option data-value="{{$koppeling}}" >{{$koppeling}}</infor-select-option>
                        @endforeach
                    </infor-select-search>
                </infor-table-filter>
                <infor-table-filter >
                    <label>Stap</label>
                    <infor-select-search data-disabled id="stap" name="stap" class="form-control" placeholder="Selecteer stap">
                        @foreach(getKoppelingenApis() as $api)
                            <infor-select-option data-params=":koppeling={{$api->koppeling}}" data-value="{{$api->stap_id}}" >{{$api->stap->stapnaam}}</infor-select-option>
                        @endforeach
                    </infor-select-search>
                </infor-table-filter>
                <infor-table-filter>
                    <label>Type</label>
                    <infor-select-search id="type" name="type" class="form-control" placeholder="Selecteer type">
                        <infor-select-option data-value="inbound" >Inkomend</infor-select-option>
                        <infor-select-option data-value="outbound" >Uitgaand</infor-select-option>
                    </infor-select-search>
                </infor-table-filter>
                <infor-table-filter>
                    <label>Van</label>
                    <input type="date" class="form-control" name="start_date" value="{{Carbon()->firstOfMonth()->format('Y-m-d')}}" >
                </infor-table-filter>
                <infor-table-filter>
                    <label>Tot</label>
                    <input type="date" class="form-control" name="end_date" value="{{Carbon()->lastOfMonth()->format('Y-m-d')}}" >
                </infor-table-filter>

            </infor-table-filters>

            <infor-table-count data-single="Request" data-multiple="Requests" ></infor-table-count>

            <infor-table-pagination>
                <infor-table-order-option data-column="created_at" >Datum</infor-table-order-option>
            </infor-table-pagination>

            <infor-table-header>Datum</infor-table-header>
            <infor-table-header>Koppeling</infor-table-header>
            <infor-table-header>Stap</infor-table-header>
            <infor-table-header>Medewerker</infor-table-header>
            <infor-table-header>Type</infor-table-header>
            <infor-table-header>Endpoint</infor-table-header>
            <infor-table-header>Code</infor-table-header>
            <infor-table-header>Data</infor-table-header>
            <infor-table-header>Response</infor-table-header>
            <infor-table-header>Error</infor-table-header>

        </infor-table>

    </section>
@endsection

@section('script')
    <script>

        const data = {
            apis: @json(getKoppelingenApis()),
        }

        $(document).ready(() => {
            initTable();
            initFilters()
        })

        //Table
        function initTable(){
            initInforTables({
                id: 'koppelingen_table',
                api: `/dashboard/api/koppelingen/get`,
                response_records_name: 'requests',
                search: {
                    api: 'dashboard/api/search/stappen_api_log',
                    content: 'searchContent',
                    sub_content: `searchSubContent`,
                    placeholder: `Zoeken...`,
                },
                errorHandler: handleAjaxErrors,
                fillRecord: fillRecord,
                execute: [tippyInit]
            })
        }
        function initFilters(){
            const koppeling_input = _inforSelectSearch.get('koppeling');
            const stap_input = _inforSelectSearch.get('stap');

            koppeling_input.onchange = api_id => {

                console.log(api_id);

                stap_input.clear();
                stap_input.options().removeClass('d-none');

                api_id ? stap_input.enable() : stap_input.disable();

                if(!api_id){ return; }
                stap_input.options().not(`[data-koppeling=${api_id}]`).addClass('d-none');
            }
        }
        function fillRecord(record){
            const { id, api, url, code, data, response, error, created_at  } = record;
            const { type, koppeling, stap, user } = api;

            return `
                <tr class="nobr" >
                    <td>${parseDate(created_at).date.eu}</td>
                    <td>${koppeling}</td>
                    <td>${stap?.stapnaam}</td>
                    <td>${user?.name || ''}</td>
                    <td>${type == 'inbound' ? `<span class="badge badge-primary" >INKOMEND</span>` : `<span class="badge badge-success" >UITGAAND</span>`}</td>
                    <td> <span data-tippy-content="<span class='nobr' >${url}</span>" >${url.length > 35 ? `${url.slice(0, 35)}...` : url}</span> </td>
                    <td> <span class="w-px-50 badge ${code.slice(0, 1) == 2 ? `badge-success` : 'badge-danger'}" >${code}</span> </td>
                    <td> <a class="btn btn-sm btn-inverse-primary" onclick="showData(${id}, 'data')" >${data.slice(0, 15)}...</a> </td>
                    <td> ${response ? `<a class="btn btn-sm btn-inverse-primary" onclick="showData(${id}, 'response')" >${htmlEncode(response).slice(0, 15)}...</a>` : ''} </td>
                    <td> ${error ? `<a class="btn btn-sm btn-inverse-primary" onclick="showData(${id}, 'error')" >${htmlEncode(error).slice(0, 15)}...</a>` : ''} </td>
                    <td></td>
                </tr>
            `
        }

        //Data
        function showData(id, target){
            const request = _inforTables.get('koppelingen_table').records.find(row => row.id == id);
            const value = request[target];

            viewJson(value);
        }

        //inforSearch
        function searchContent(row){
            const { koppeling, created_at} = row;
            const date = parseDate(created_at);

            return `<div class="flex-between" > <span>${koppeling}</span> <span class="font-size-08 text-muted" >${date.day_full} ${date.month_short_name} ${date.year}</span> </div>`
        }
        function searchSubContent(row){
            const {type, code, stapnaam} = row;
            return `${type == 'inbound' ? `Inkomend` : 'Uitgaand'}, ${code}, ${stapnaam}`
        }

    </script>
@endsection
