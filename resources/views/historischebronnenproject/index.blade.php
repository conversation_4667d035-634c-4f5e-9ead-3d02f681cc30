@extends('layouts.app')

@section('content')

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-12">
            @if (session('status'))
                <div class="alert alert-success" role="alert">
                    {{ session('status') }}
                </div>
            @endif

            <h1>Projectnummers toewijzen</h1>
            <p>Per combinatie van adresid + datum</p>

            <hr>
            
            <div id="adressen"></div>

            <button class="btn btn-primary" id="volgendebutton" onclick="clearAndGetAdressen()" style="display: none">Akkoord, Volgende 100</button>

            <a href="{{ url('dashboard/tempimport') }}" id="terugbutton"><button class="btn btn-primary" style="display: none">Terug</button></a>
        </div>
    </div>
</div>


@endsection
@section('script')
<script>
    $( document ).ready(function() {
        getAdressen();
    });

    function clear() {
        $('#adressen').empty();
    }

    var i = 0;

    function clearAndGetAdressen(){
        i++;
        clear();
        getAdressen();
    }

    function getAdressen(){
        $.ajax({
            method: "POST",
            url: "https://www.asbestregisseur.be/api/get100adressen",
            data: {
                i: i
            },
        })
        .done(function (response){
            if(response.adressen.length == 0){
                $('#volgendebutton').css("display", "none");
                $('#terugbutton').css("display", "block");
            } else {
                $('#volgendebutton').css("display", "block");
                $('#terugbutton').css("display", "none");
                $.each(response.adressen, function (index, value){
                    $('#adressen').append('<h3>('+value.complexnummer+') '+value.straat+' '+value.huisnummer+value.toevoeging+' '+value.postcode+' '+value.plaats+'</h3><table class="table"><tr><td id="inventarisatie'+value.id+'" style="width: 50%"></td><td id="sanering'+value.id+'" style="width: 50%"></td></tr></table>');
                    
                    $('#inventarisatie'+value.id).append('<p>Geïnventariseerd</p><table class="table" id="san'+value.id+'">');
                    $.each(value.temp_hist_bronnen_inventariseerder, function (index2, value2){
                        $('#san'+value.id).append('<tr><td>'+value2.inventarisatiedatum+'</td><td>'+value2.aantal+' '+value2.eenheid+'</td><td>'+value2.omschrijving?.omschrijving+'</td><td>'+value2.risicoklasse?.klasse+'</td><td>'+value2.asbesthoudend+'</td><td>'+value2.locatie?.locatie+'</td><td><input type="checkbox" class="form-check-input" id="gesaneerdcheck'+value2.id+'" onchange="setGesaneerd('+value2.id+')"></td></tr>');
                    });
                    $('#inventarisatie'+value.id).append('</table>');

                    $('#sanering'+value.id).append('<p>Gesaneerd</p><table class="table" id="inv'+value.id+'">');
                    $.each(value.temp_hist_bronnen_saneerder, function (index3, value3){
                        $('#inv'+value.id).append('<tr><td>'+value3.saneerdatum+'</td><td>'+value3.aantal+' '+value3.eenheid+'</td><td>'+value3.omschrijving?.omschrijving+'</td><td>'+value3.risicoklasse?.klasse+'</td><td>'+value3.asbesthoudend+'</td><td>'+value3.locatie?.locatie+'</td><td><input type="checkbox" class="form-check-input" id="geinventariseerdcheck'+value3.id+'" onchange="setGeinventariseerd('+value3.id+')"></td></tr>');
                    });
                    $('#sanering'+value.id).append('</table>');
                });
                window.scrollTo(0,0);
            }
        })
        .fail(function (jqXHR, textStatus){
            alert("error: "+textStatus);
        });
    }

    function setProject(id){
        $.ajax({
            method: "POST",
            url: "https://www.asbestregisseur.be/api/setprojectid",
            data: {
                'id': id,
                'gesaneerd': $(`#gesaneerdcheck${id}`).prop('checked') ? 1 : 0,
            }
        })
        .done(function (response){
            if(!response.success){
                alert("DOE KINS ECHT NIEKS");
                return;
            }
        })
        .fail(function (jqXHR, textStatus, errorThrown){
            alert("Er is een fout opgetreden: "+errorThrown);
        });
    }

</script>
@endsection