@extends('layouts.app')

@section('content')
    <section>

        <infor-table id="logs_table" >

            <infor-table-filters >
                <infor-table-filter>
                    <label>Gebruiker</label>
                    <infor-select-search name="user" class="form-control" placeholder="Selecteer gebruiker" >
                        @foreach(getUsers() as $user)
                            <infor-select-option data-value="{{$user->id}}" >{{$user->name}}</infor-select-option>
                        @endforeach
                    </infor-select-search>
                </infor-table-filter>
                <infor-table-filter>
                    <label>Project</label>
                    <infor-search id="project" class="form-control" name="project" placeholder="Selecteer project" data-content="projectnummer" data-sub-content="straat, huisnummer,toevoeging" data-api="dashboard/api/search/project"></infor-search>
                </infor-table-filter>
                <infor-table-filter>
                    <label>Level</label>
                    <infor-select-search name="level" class="form-control" placeholder="Selecteer level" >
                        <infor-select-option data-value="100" > <span class="dot-glow bg-logs-debug" ></span> Debug</infor-select-option>
                        <infor-select-option data-value="200" > <span class="dot-glow bg-logs-info" ></span> Info</infor-select-option>
                        <infor-select-option data-value="250" > <span class="dot-glow bg-logs-notice" ></span> Notice</infor-select-option>
                        <infor-select-option data-value="300" > <span class="dot-glow bg-logs-warning" ></span> Warning</infor-select-option>
                        <infor-select-option data-value="550" > <span class="dot-glow bg-logs-alert" ></span> Alert</infor-select-option>
                    </infor-select-search>
                </infor-table-filter>
                <infor-table-filter>
                    <label>Datum van</label>
                    <input type="date" name="start_date" class="form-control" value="{{Carbon()->startOfWeek()->format('Y-m-d')}}" >
                </infor-table-filter>
                <infor-table-filter>
                    <label>Datum tot</label>
                    <input type="date" name="end_date" class="form-control" value="{{Carbon()->endOfWeek()->format('Y-m-d')}}" >
                </infor-table-filter>
            </infor-table-filters>


            <infor-table-count data-single="Activiteit" data-multiple="Activiteiten" ></infor-table-count>

            <infor-table-pagination>
                <infor-table-order-option data-column="created_at" >Datum</infor-table-order-option>
                <infor-table-order-option data-column="level" >Level</infor-table-order-option>
            </infor-table-pagination>


            <infor-table-header>Datum</infor-table-header>
            <infor-table-header>Level</infor-table-header>
            <infor-table-header>Log</infor-table-header>
            <infor-table-header>Gebruiker</infor-table-header>
            <infor-table-header>Project</infor-table-header>
            <infor-table-header>Stap</infor-table-header>
            <infor-table-header>Browser</infor-table-header>
            <infor-table-header>Land</infor-table-header>
            <infor-table-header>Data</infor-table-header>


        </infor-table>

    </section>
@endsection

@section('script')
    <script>

        $(document).ready(() => {
            initInforTables({
                id: 'logs_table',
                api: `/dashboard/api/logs/get`,
                response_records_name: 'logs',
                search: {
                    api: 'dashboard/api/search/logs',
                    content: 'searchContent',
                    placeholder: `Zoeken...`,
                },
                errorHandler: handleAjaxErrors,
                fillRecord: fillRecord,
                execute: [tippyInit]
            })
        })

        //Table
        function fillRecord(record){
            const { id, user, project, stap, message, level_name, country, browser, created_at, data } = record

            return `
                <tr>
                  <td class="nobr w-0" >${parseDate(created_at).day} ${parseDate(created_at).month_short_name} ${parseDate(created_at).year} ${parseDate(created_at).time}</td>
                  <td class="w-0" > <span class="w-100 badge bg-logs-${level_name.toLowerCase()}" >${level_name}</span> </td>
                  <td>${message}</td>
                  <td class="nobr" >${user?.name || ''}</td>
                  <td class="nobr" >${project?.projectnummer || ''}</td>
                  <td>${stap?.stapnaam || ''}</td>
                  <td>${browser || ''}</td>
                  <td>${country || ''}</td>
                  <td> <a class="btn btn-inverse-primary" onclick="viewData(${id})" >${data.slice(0, 35)}${data.length > 35 ? '...' : ''}</a> </td>
                </tr>
            `

        }

        //Data
        function viewData(id){
            const log = _inforTables.get('logs_table').records.find(row => row.id == id);
            viewJson(log.data);
        }

        //Search
        function searchContent(record){
            const { level_name, message } = record;
            return  `<div class="flex-align" >
                      <div class="dot-glow bg-logs-${level_name.toLowerCase()} mr-1" ></div>
                      <div>
                        <div>${message.slice(0, 30)}${message.length > 30 ? '...' : ''}</div>
                      </div>
                    </div>
            `
        }

    </script>
@endsection
