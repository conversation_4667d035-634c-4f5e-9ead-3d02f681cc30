{{-- Stap 3: <PERSON><PERSON><PERSON> gesaneerd? --}}
{{-- Voor ieder adres de geïnventariseerde- en gesaneerde bronnen langs elkaar zetten--}}
@extends('layouts.app')

@section('content')

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-12">
            @if (session('status'))
                <div class="alert alert-success" role="alert">
                    {{ session('status') }}
                </div>
            @endif
            {{-- Definiëren titel en subtitel --}}
            <h1>Gesaneerd</h1>
            <p>Per adres.</p>

            <hr>
            <div id="adressen"></div>
            {{-- Button die je de volgende 100 adressen geeft, middels het uitvoeren van de functie 'clearAndGetAdressen()' --}}
            <button class="btn btn-primary" id="volgendebutton" onclick="clearAndGetAdressen()" style="display: none"><PERSON><PERSON><PERSON><PERSON>, Volgende 100</button>
            {{-- Button die je terug leidt naar de hoofdpagina (wanneer alle bronnen doorlopen zijn) --}}
            <a href="{{ url('dashboard/tempimport') }}" id="terugbutton"><button class="btn btn-primary" style="display: none">Terug</button></a>
        </div>
    </div>
</div>


@endsection

{{-- Script --}}
{{-- Hierin worden de uit te voeren functies gedefiniëerd --}}
@section('script')
<script>
    // Wanneer de pagina is geladen, geef adressen weer
    $( document ).ready(function() {
        getAdressen();
    });
    // Legen van de weergaven adressen
    function clear() {
        $('#adressen').empty();
    }

    // Aanmaken variabele 'i' (Deze is benodigd om bij te houden welke set adressen moet worden opgehaald )
    var i = 0;

    // Verhogen van 'i' met 1
    // Uitvoeren functie voor legen pagina, vervolgens uitvoeren functie voor volgende 100 adressen op te halen
    function clearAndGetAdressen(){
        i++;
        clear();
        getAdressen();
    }

    // Verkrijgen 100 adressen
    function getAdressen(){
        $.ajax({
            method: "POST",
            url: "https://www.asbestregisseur.be/api/get100adressen",
            data: {
                i: i
            },
        })
        // Wanneer het bovenstaande verzoek succesvol is:
        .done(function (response){
            // Als er geen adressen zijn gevonden (allemaal doorlopen), geef 'terug button' weer > Welke je terugleidt naar de hoofdpagina
            if(response.adressen.length == 0){
                $('#volgendebutton').css("display", "none");
                $('#terugbutton').css("display", "block");
            // Wanneer er wel adressen zijn gevonden, geef 'volgende button' weer > Waarmee de volgende 100 adressen ingeladen worden
            } else {
                $('#volgendebutton').css("display", "block");
                $('#terugbutton').css("display", "none");
                // Ook moet door ieder adres gelooped worden en zijn adresinformatie worden opgehaald
                $.each(response.adressen, function (index, value){
                    $('#adressen').append('<h3>('+value.complexnummer+') '+value.straat+' '+value.huisnummer+value.toevoeging+' '+value.postcode+' '+value.plaats+'</h3><table class="table"><tr><td id="inventarisatie'+value.id+'" style="width: 50%"></td><td id="sanering'+value.id+'" style="width: 50%"></td></tr></table>');
                    // Ophalen van inventarisatie
                    // Maak een checkbox die de volgende functie activeert: setGesaneerd())
                    $('#inventarisatie'+value.id).append('<p>Geïnventariseerd</p><table class="table" id="san'+value.id+'">');
                    $.each(value.temp_hist_bronnen_inventariseerder, function (index2, value2){
                        $('#san'+value.id).append('<tr><td>'+value2.inventarisatiedatum+'</td><td>'+value2.aantal+' '+value2.eenheid+'</td><td>'+value2.omschrijving?.omschrijving+'</td><td>'+value2.risicoklasse?.klasse+'</td><td>'+value2.asbesthoudend+'</td><td>'+value2.locatie?.locatie+'</td><td><input type="checkbox" class="form-check-input" id="gesaneerdcheck'+value2.id+'" onchange="setGesaneerd('+value2.id+')"></td></tr>');
                    });
                    $('#inventarisatie'+value.id).append('</table>');
                    // Ophalen van sanering
                    // Maak een checkbox die de volgende functie activeert: setGeinventariseerd
                    $('#sanering'+value.id).append('<p>Gesaneerd</p><table class="table" id="inv'+value.id+'">');
                    $.each(value.temp_hist_bronnen_saneerder, function (index3, value3){
                        $('#inv'+value.id).append('<tr><td>'+value3.saneerdatum+'</td><td>'+value3.aantal+' '+value3.eenheid+'</td><td>'+value3.omschrijving?.omschrijving+'</td><td>'+value3.risicoklasse?.klasse+'</td><td>'+value3.asbesthoudend+'</td><td>'+value3.locatie?.locatie+'</td><td><input type="checkbox" class="form-check-input" id="geinventariseerdcheck'+value3.id+'" onchange="setGeinventariseerd('+value3.id+')"></td></tr>');
                    });
                    $('#sanering'+value.id).append('</table>');
                });
                // Scroll automatisch terug naar begin pagina
                window.scrollTo(0,0);
            }
        })
        // Wanneer het verzoek niet succesvol is
        // Geef pop=up melding weer met foutmelding
        .fail(function (jqXHR, textStatus){
            alert("error: "+textStatus);
        });
    }

    // functie 'setGesaneerd', wanneer aan de linkerzijde (inventarisatie dataset) op de checkbox wordt geklikt
    // Als deze wordt aangevinkt, moet de desbetreffende bron als 'gesaneerd' worden gezet (gesaneerd=1)
    function setGesaneerd(id){
        $.ajax({
            method: "POST",
            url: "https://www.asbestregisseur.be/api/setgesaneerd",
            data: {
                'id': id,
                'gesaneerd': $(`#gesaneerdcheck${id}`).prop('checked') ? 1 : 0,
            }
        })
        .done(function (response){
            if(!response.success){
                alert("DOE KINS ECHT NIEKS");
                return;
            }
        })
        .fail(function (jqXHR, textStatus, errorThrown){
            alert("Er is een fout opgetreden: "+errorThrown);
        });
    }

    // functie 'setGeinventariseerd', wanneer aan de rechterzijde (saneerder dataset) op de checkbox wordt geklikt
    // Als deze wordt aangevinkt, moet de bron worden toegevoegd aan de inventarisatie dataset
    // (De inventarisatie dataset is de dataset die uiteindelijk wordt gebruikt als final dataset en moet dus compleet zijn)
    function setGeinventariseerd(id){
        $.ajax({
            method: "POST",
            url: "https://www.asbestregisseur.be/api/setgeinventariseerd",
            data: {
                'id': id,
                'geinventariseerd': $(`#geinventariseerdcheck${id}`).prop('checked') ? 1 : 0,
            }
        })
        .done(function (response){
            if($(`#geinventariseerdcheck${id}`).prop('checked')){
                // alert('Toegevoegd');
            } else {
                // alert('Verwijderd');
            }
        })
        .fail(function (jqXHR, textStatus, errorThrown){
            alert("DOE KINS ECHT NIEKS");
        });
    }

</script>
@endsection
