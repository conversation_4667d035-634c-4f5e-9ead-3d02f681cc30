@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row pb-3" style="border-bottom: 2px dashed #ddd; padding: 10px;">
        <h1>Statistieken</h1>
        <h5 class="text-muted">Bekijk alle verzamelde data live in overzichtelijke grafieken</h5>
    </div>
    {{-- Algemeen --}}
    <div class="row pt-3">
        <div class="row" style="border-bottom: 2px dashed #ddd; padding: 10px;">
            <h4 onclick="toggleAccordion('algemeen')">▿ Algemeen</h4>
            <div class="row pt-3" id="algemeen" style="display: none;">
                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-6">
                            <div style="max-width: 80%; margin: auto;">
                                <canvas id="aantalprojectenperjaar" width="100%" height="100%"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div style="max-width: 80%; margin: auto;">
                                <canvas id="aantalprojectenpermaand" width="100%" height="100%"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div style="max-width: 80%; margin: auto;">
                                <canvas id="gemiddeldeprojectduur" width="100%" height="100%"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div style="max-width: 80%; margin: auto;">
                                <canvas id="aantaldagentussenprojectaanmaakenvooropnamedatum" width="100%" height="100%"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- Proces --}}
    <div class="row pt-3">
        <div class="row" style="border-bottom: 2px dashed #ddd; padding: 10px;">
            <h4 onclick="toggleAccordion('proces')">▿ Proces</h4>
            <div class="row pt-3" id="proces" style="display: none;">
                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-4">
                            <div style="max-width: 80%; margin: auto;">
                                <canvas id="doorlooptijd1" width="100%" height="100%"></canvas>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div style="max-width: 80%; margin: auto;">
                                <canvas id="doorlooptijdinventarisatie" width="100%" height="100%"></canvas>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div style="max-width: 80%; margin: auto;">
                                <canvas id="doorlooptijdsanering" width="100%" height="100%"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- Inventarisatie --}}
    <div class="row pt-3">
        <div class="row" style="border-bottom: 2px dashed #ddd; padding: 10px;">
            <h4 onclick="toggleAccordion('inventarisatie')">▿ Inventarisatie</h4>
            <div class="row pt-3" id="inventarisatie" style="display: none;">
                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-6">
                            <div style="max-width: 80%; margin: auto;">
                                <canvas id="geinventariseerd" width="100%" height="100%"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div style="max-width: 80%; margin: auto;">
                                <canvas id="statusnainventarisatie" width="100%" height="100%"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div style="max-width: 80%; margin: auto;">
                                <canvas id="inventarisatiespermaand" width="100%" height="100%"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div style="max-width: 80%; margin: auto;">
                                <canvas id="gemiddeldeduurinventarisatie" width="100%" height="100%"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div style="max-width: 80%; margin: auto;">
                                <canvas id="inventarisatietijdiguitgevoerd" width="100%" height="100%"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div style="max-width: 80%; margin: auto;">
                                <canvas id="redenlateruitgevoerdinventarisatie" width="100%" height="100%"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div style="max-width: 80%; margin: auto;">
                                <canvas id="inventarisatierapporttijdig" width="100%" height="100%"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div style="max-width: 80%; margin: auto;">
                                <canvas id="gemiddeldeduuraanleveringrapport" width="100%" height="100%"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div style="max-width: 80%; margin: auto;">
                                <canvas id="meestvoorkomendebronlocaties" width="100%" height="100%"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div style="max-width: 80%; margin: auto;">
                                <canvas id="meestvoorkomendebronnamen" width="100%" height="100%"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div style="max-width: 80%; margin: auto;">
                                <canvas id="meestgesaneerdebronnamen" width="100%" height="100%"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div style="max-width: 80%; margin: auto;">
                                <canvas id="zav" width="100%" height="100%"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- Aannemers --}}
    <div class="row pt-3">
        <div class="row" style="border-bottom: 2px dashed #ddd; padding: 10px;">
            <h4 onclick="toggleAccordion('aannemers')">▿ Aannemers</h4>
            <div class="row pt-3" id="aannemers" style="display: none;">
                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-6">
                            <div style="max-width: 80%; margin: auto;">
                                <canvas id="aantalprojectenpermaandperaannemer" width="100%" height="100%"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div style="max-width: 80%; margin: auto;">
                                <canvas id="aantalprojectenperjaarperaannemer" width="100%" height="100%"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {{-- Saneerders --}}
    <div class="row pt-3">
        <div class="row" style="border-bottom: 2px dashed #ddd; padding: 10px;">
            <h4 onclick="toggleAccordion('saneerders')">▿Saneerders</h4>
            <div class="row pt-3" id="saneerders" style="display: none;">
                <!-- Content of the "Saneerders" section -->
            </div>
        </div>
    </div>
    {{-- Eindcontrole --}}
    <div class="row pt-3">
        <div class="row" style="border-bottom: 2px dashed #ddd; padding: 10px;">
            <h4 onclick="toggleAccordion('eindcontrole')">▿Eindcontrole</h4>
            <div class="row pt-3" id="eindcontrole" style="display: none;">
                <!-- Content of the "Eindcontrole" section -->
            </div>
        </div>
    </div>
    {{-- Kosten --}}
    <div class="row pt-3">
        <div class="row" style="border-bottom: 2px dashed #ddd; padding: 10px;">
            <h4 onclick="toggleAccordion('kosten')">▿ Kosten</h4>
            <div class="row pt-3" id="kosten" style="display: none;">
                <div class="col-md-12">
                    <div class="row">
                        <div class="col-md-6">
                            <div style="max-width: 80%; margin: auto;">
                                <canvas id="totalefactuurwaarde" width="100%" height="100%"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div style="max-width: 80%; margin: auto;">
                                <canvas id="gemiddeldefactuurwaarde" width="100%" height="100%"></canvas>
                            </div>
                        </div>
                        <div class="col-md-6">

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
@section('script')
<script>

    function toggleAccordion(sectionId) {
        var section = document.getElementById(sectionId);
        if (section.style.display === "none") {
            section.style.display = "block";
        } else {
            section.style.display = "none";
        }
    }

    $(document).ready(function(){
        aantalprojectenpermaand();
        aantalprojectenperjaar();
        gemiddeldeprojectduur();
        aantaldagentussenprojectaanmaakenvooropnamedatum();
        doorlooptijd1();
        doorlooptijdinventarisatie();
        doorlooptijdsanering();
        geinventariseerd();
        statusnainventarisatie();
        inventarisatiespermaand();
        gemiddeldeduurinventarisatie();
        inventarisatietijdiguitgevoerd();
        redenlateruitgevoerdinventarisatie();
        inventarisatierapporttijdig();
        gemiddeldeduuraanleveringrapport();
        meestvoorkomendebronlocaties();
        meestvoorkomendebronnamen();
        meestgesaneerdebronnamen();
        zav();
        aantalprojectenpermaandperaannemer();
        aantalprojectenperjaarperaannemer();
        totalefactuurwaarde();
        gemiddeldefactuurwaarde();
    })
// Algemeen
    // Aantal projecten per maand
    function aantalprojectenpermaand(){
        $.ajax({
            method:"POST",
            url: "{{ url('/dashboard/api/statistiek') }}",
            data: {
                statistiek: "aantalprojectenpermaand",
                _token: @json(csrf_token())
            },
            success:function(response){
                var Content = response.data;
                var Labels = new Array();
                var Data = new Array();
                Content.forEach(function(data){
                    Labels.push(data.datum);
                    Data.push(data.aantal);
                });
                let tc = {
                    type: 'bar',
                    data: {
                        labels: Labels,
                        datasets: [{
                            data: Data,
                            backgroundColor: ['#01689b'],
                            label: 'Projecten',
                        }]
                    },
                    options: {
                        indexAxis: 'y',
                        plugins: {
                            title: {
                                display: true,
                                text: 'Aantal projecten aangemaakt per maand',
                                align: 'start',
                                font: {
                                    size: 15
                                }
                            },
                            tooltips: {
                                enabled: false
                            },
                        }
                    }
                };
                ctx = new Chart(document.getElementById('aantalprojectenpermaand'),tc);
            },
            error:function(error){
                console.log(error);
            }
        });
    }
    // Aantal projecten per jaar
    function aantalprojectenperjaar(){
        $.ajax({
            method:"POST",
            url: "{{ url('/dashboard/api/statistiek') }}",
            data: {
                statistiek: "aantalprojectenperjaar",
                _token: @json(csrf_token())
            },
            success:function(response){
                var Content = response.data;
                var Labels = new Array();
                var Data = new Array();
                Content.forEach(function(data){
                    Labels.push(data.datum);
                    Data.push(data.aantal);
                });
                let tc = {
                    type: 'bar',
                    data: {
                        labels: Labels,
                        datasets: [{
                            data: Data,
                            backgroundColor: ['#01689b'],
                            label: 'Projecten',
                        }]
                    },
                    options: {
                        indexAxis: 'y',

                    plugins: {

                    title: {
                        display: true,
                        text: 'Aantal projecten aangemaakt per jaar',
                        align: 'start',
                                font: {
                                    size: 15
                                }
                    },
                    tooltips: {
                        enabled: false
                    },
                    }
                }
                };
                ctx = new Chart(document.getElementById('aantalprojectenperjaar'),tc);
            },
            error:function(error){
                console.log(error);
            }
        });
    }
    // Gemiddelde projectduur per maand (en jaar)
    function gemiddeldeprojectduur(){
        $.ajax({
            method:"POST",
            url: "{{ url('/dashboard/api/statistiek') }}",
            data: {
                statistiek: "gemiddeldeprojectduur",
                _token: @json(csrf_token())
            },
            success:function(response){
                var Content = response.data;
                var Labels = new Array();
                var Data = new Array();
                Content.forEach(function(data){
                    Labels.push(data.datum);
                    Data.push(data.gemiddeldeduur);
                });
                let tc = {
                    type: 'bar',
                    data: {
                        labels: Labels,
                        datasets: [{
                            data: Data,
                            backgroundColor: ['#01689b'],
                            label: 'Projectduur (dagen)',
                        }]
                    },
                    options: {
                        indexAxis: 'y',
                        plugins: {
                            title: {
                                display: true,
                                text: 'Gemiddelde projectduur',
                                align: 'start',
                                font: {
                                    size: 15
                                }
                            },
                            tooltips: {
                                enabled: false
                            },
                        }
                    }
                };
                ctx = new Chart(document.getElementById('gemiddeldeprojectduur'),tc);
            },
            error:function(error){
                console.log(error);
            }
        });
    }
    // Aantal dagen tussen aanmaak project en vooropnamedatum (Exclusief planmatige projecten)
    function aantaldagentussenprojectaanmaakenvooropnamedatum(){
        $.ajax({
            method:"POST",
            url: "{{ url('/dashboard/api/statistiek') }}",
            data: {
                statistiek: "aantaldagentussenprojectaanmaakenvooropnamedatum",
                _token: @json(csrf_token())
            },
            success:function(response){
                var Labels = ['0 dagen', '1 dag', '2 dagen', '3 dagen', '4 dagen', '5 dagen', 'meer dan 5 dagen'];
                var Data = [response?.data?.dagen0? response.data.dagen0.map(item => item.aantal) : '0', response?.data?.dagen1? response.data.dagen1.map(item => item.aantal) : '0', response?.data?.dagen2? response.data.dagen2.map(item => item.aantal) : '0', response?.data?.dagen3? response.data.dagen3.map(item => item.aantal) : '0', response?.data?.dagen4? response.data.dagen4.map(item => item.aantal) : '0', response?.data?.dagen5? response.data.dagen5.map(item => item.aantal) : '0', response?.data?.dagen5meer? response.data.dagen5meer.map(item => item.aantal) : '0'];
                var JaarLabels = response.data.dagen0.map(item => item.jaar);

                let tc = {
                    type: 'bar',
                    data: {
                        labels: JaarLabels,
                        datasets: [{
                            label: Labels[0],
                            data: Data[0],
                            backgroundColor: '#007000'
                        }, {
                            label: Labels[1],
                            data: Data[1],
                            backgroundColor: '#238823'
                        }, {
                            label: Labels[2],
                            data: Data[2],
                            backgroundColor: '#2db12d'
                        }, {
                            label: Labels[3],
                            data: Data[3],
                            backgroundColor: '#33c533'
                        }, {
                            label: Labels[4],
                            data: Data[4],
                            backgroundColor: '#FFBF00'
                        }, {
                            label: Labels[5],
                            data: Data[5],
                            backgroundColor: '#f48718'
                        }, {
                            label: Labels[6],
                            data: Data[6],
                            backgroundColor: '#D2222D'
                        }]
                    },
                    options: {
                        indexAxis: 'y',
                        plugins: {
                            title: {
                                display: true,
                                text: 'Aantal dagen tussen aanmaak project en vooropnamedatum',
                                align: 'start',
                                font: {
                                    size: 15
                                }
                            },
                            tooltips: {
                                position: 'top'
                            },
                        },
                        // scales: {
                        //     x: {
                        //         stacked: true,
                        //         min: 0,
                        //         max: 100,
                        //         ticks: {
                        //             callback: function(value) {
                        //                 return value + '%';
                        //             },
                        //             stepSize: 20
                        //         }
                        //     },
                        //     y: {
                        //         stacked: true
                        //     }
                        // }
                    }
                };
                ctx = new Chart(document.getElementById('aantaldagentussenprojectaanmaakenvooropnamedatum'), tc);
            },
            error:function(error){
                console.log(error);
            }
        });
    }
// Proces
    // Doorlooptijd opdrachtverstrekking tot inventarisatiedatum
    function doorlooptijd1(){
    $.ajax({
        method:"POST",
        url: "{{ url('/dashboard/api/statistiek') }}",
        data: {
            statistiek: "doorlooptijd1",
            _token: @json(csrf_token())
        },
        success:function(response){
            var Labels = ['Gehaald', 'Niet gehaald'];
            var Data = [response.data.gehaald.map(item => item.aantal), response.data.nietgehaald.map(item => item.aantal)];
            var JaarLabels = response.data.gehaald.map(item => item.jaar);

            let tc = {
                type: 'bar',
                data: {
                    labels: JaarLabels,
                    datasets: [{
                        label: Labels[0],
                        data: Data[0],
                        backgroundColor: '#258D07'
                    }, {
                        label: Labels[1],
                        data: Data[1],
                        backgroundColor: '#D20000'
                    }]
                },
                options: {
                    indexAxis: 'y',
                    plugins: {
                        title: {
                            display: true,
                            text: ['Doorlooptijd opdrachtverstrekking tot inventarisatiedatum', '(KPI: 2 dagen)'],
                            align: 'start',
                            font: {
                                size: 15
                            }
                        },
                        tooltips: {
                            position: 'top'
                        },
                    },
                }
            };
            ctx = new Chart(document.getElementById('doorlooptijd1'), tc);
        },
        error:function(error){
            console.log(error);
        }
    });
}
    // Doorlooptijd inventarisatie
    function doorlooptijdinventarisatie(){
        $.ajax({
            method:"POST",
            url: "{{ url('/dashboard/api/statistiek') }}",
            data: {
                statistiek: "doorlooptijdinventarisatie",
                _token: @json(csrf_token())
            },
            success:function(response){
                var Labels = ['Gehaald', 'Niet gehaald'];
                var Data = [response?.data?.gehaald? response.data.gehaald.map(item => item.aantal) : '0', response?.data?.nietgehaald? response.data.nietgehaald.map(item => item.aantal) : '0'];
                var JaarLabels = response.data.gehaald.map(item => item.jaar);

                let tc = {
                    type: 'bar',
                    data: {
                        labels: JaarLabels,
                        datasets: [{
                            label: Labels[0],
                            data: Data[0],
                            backgroundColor: '#258D07'
                        }, {
                            label: Labels[1],
                            data: Data[1],
                            backgroundColor: '#D20000'
                        }]
                    },
                    options: {
                        indexAxis: 'y',
                        plugins: {
                            title: {
                                display: true,
                                text: ['Doorlooptijd inventarisatie', '(KPI: 5 dagen)'],
                                align: 'start',
                                font: {
                                    size: 15
                                }
                            },
                            tooltips: {
                                position: 'top'
                            },
                        },
                    }
                };
                ctx = new Chart(document.getElementById('doorlooptijdinventarisatie'), tc);
            },
            error:function(error){
                console.log(error);
            }
        });
    }
    // Doorlooptijd sanering
    function doorlooptijdsanering(){
        $.ajax({
            method:"POST",
            url: "{{ url('/dashboard/api/statistiek') }}",
            data: {
                statistiek: "doorlooptijdsanering",
                _token: @json(csrf_token())
            },
            success:function(response){
                var Labels = ['Gehaald', 'Niet gehaald'];
                var Data = [response?.data?.gehaald? response.data.gehaald.map(item => item.aantal) : '0', response?.data?.nietgehaald? response.data.nietgehaald.map(item => item.aantal) : '0'];
                var JaarLabels = response.data.gehaald.map(item => item.jaar);

                let tc = {
                    type: 'bar',
                    data: {
                        labels: JaarLabels,
                        datasets: [{
                            label: Labels[0],
                            data: Data[0],
                            backgroundColor: '#258D07'
                        }, {
                            label: Labels[1],
                            data: Data[1],
                            backgroundColor: '#D20000'
                        }]
                    },
                    options: {
                        indexAxis: 'y',
                        plugins: {
                            title: {
                                display: true,
                                text: ['Doorlooptijd sanering', '(KPI: 7 dagen)'],
                                align: 'start',
                                font: {
                                    size: 15
                                }
                            },
                            tooltips: {
                                position: 'top'
                            },
                        },
                    }
                };
                ctx = new Chart(document.getElementById('doorlooptijdsanering'), tc);
            },
            error:function(error){
                console.log(error);
            }
        });
    }

// Inventarisatie
    // Woningbezit geïnventariseerd (%)
    function geinventariseerd(){
        $.ajax({
            method:"POST",
            url: "{{ url('/dashboard/api/statistiek') }}",
            data: {
                statistiek: "inventarisatie",
                _token: @json(csrf_token())
            },
            success:function(response){
                var total = response.data.aantal_geinventariseerd + response.data.aantal_niet_geinventariseerd;
                var geinventariseerd_pct = ((response.data.aantal_geinventariseerd / total) * 100).toFixed(2);
                var niet_geinventariseerd_pct = ((response.data.aantal_niet_geinventariseerd / total) * 100).toFixed(2);
                var Labels = ['Geïnventariseerd (' + geinventariseerd_pct + '%)', 'Niet geïnventariseerd (' + niet_geinventariseerd_pct + '%)'];
                var Data = [response.data.aantal_geinventariseerd, response.data.aantal_niet_geinventariseerd];
                let tc = {
                    type: 'pie',
                    data: {
                        labels: Labels,
                        datasets: [{
                            data: Data,
                            backgroundColor: ['#258D07', '#D20000']
                        }]
                    },
                    options: {
                        plugins: {
                            title: {
                                display: true,
                                text: 'Woningbezit geïnventariseerd',
                                align: 'start',
                                    font: {
                                        size: 15
                                    }
                            },
                            legend: {
                                position: 'top'
                            }
                        }
                    }
                };
                ctx = new Chart(document.getElementById('geinventariseerd'), tc);
            },
            error:function(error){
                console.log(error);
            }
        });
    }
    // Status na inventarisatie (%)
    function statusnainventarisatie(){
        $.ajax({
            method:"POST",
            url: "{{ url('/dashboard/api/statistiek') }}",
            data: {
                statistiek: "statusnainventarisatie",
                _token: @json(csrf_token())
            },
            success:function(response){
                var total = response.data.wel_asbest + response.data.geen_asbest + response.data.niet_saneren;
                var welasbest_pct = ((response.data.wel_asbest / total) * 100).toFixed(2);
                var geenasbest_pct= ((response.data.geen_asbest / total) * 100).toFixed(2);
                var nietsaneren_pct= ((response.data.niet_saneren / total) * 100).toFixed(2);
                var Labels = ['Wel asbest > saneren (' + welasbest_pct + '%)', 'Geen asbest > niet saneren (' + geenasbest_pct + '%)', 'Wel asbest > Niet saneren (' + nietsaneren_pct + '%)'];
                var Data = [response.data.wel_asbest, response.data.geen_asbest, response.data.niet_saneren];
                let tc = {
                    type: 'pie',
                    data: {
                        labels: Labels,
                        datasets: [{
                            data: Data,
                            backgroundColor: ['#01689b','#FF9900','#165504']
                        }]
                    },
                    options: {
                        plugins: {
                            title: {
                                display: true,
                                text: 'Status na inventarisatie',
                                align: 'start',
                                    font: {
                                        size: 15
                                    }
                            },
                            legend: {
                                position: 'top'
                            },
                            datalabels: {
                                formatter: (value, ctx) => {
                                    let sum = 0;
                                    let dataArr = ctx.chart.data.datasets[0].data;
                                    dataArr.map(data => {
                                        sum += data;
                                    });
                                    let percentage = (value*100 / sum).toFixed(2)+"%";
                                    return percentage;
                                },
                                color: 'white',
                                font: {
                                    size: 12
                                }
                            }
                        }
                    }
                };
                ctx = new Chart(document.getElementById('statusnainventarisatie'), tc);
            },
            error:function(error){
                console.log(error);
            }
        });
    }
    // Aantal inventarisaties per maand
    function inventarisatiespermaand(){
        $.ajax({
            method:"POST",
            url: "{{ url('/dashboard/api/statistiek') }}",
            data: {
                statistiek: "inventarisatiespermaand",
                _token: @json(csrf_token())
            },
            success:function(response){
                var Content = response.data;
                var Labels = new Array();
                var Data = new Array();
                Content.forEach(function(data){
                    Labels.push(data.datum);
                    Data.push(data.aantal);
                });
                let tc = {
                    type: 'bar',
                    data: {
                        labels: Labels,
                        datasets: [{
                            data: Data,
                            backgroundColor: ['#01689b'],
                            label: 'Inventarisaties',
                        }]
                    },
                    options: {
                        indexAxis: 'y',
                        plugins: {
                            title: {
                                display: true,
                                text: 'Aantal inventarisaties',
                                align: 'start',
                                font: {
                                    size: 15
                                }
                            },
                            tooltips: {
                                enabled: false
                            },
                        }
                    }
                };
                ctx = new Chart(document.getElementById('inventarisatiespermaand'),tc);
            },
            error:function(error){
                console.log(error);
            }
        });
    }
    //Gemidelde duur inventarisaties
    function gemiddeldeduurinventarisatie(){
        $.ajax({
            method:"POST",
            url: "{{ url('/dashboard/api/statistiek') }}",
            data: {
                statistiek: "gemiddeldeduurinventarisatie",
                _token: @json(csrf_token())
            },
            success:function(response){
                var Content = response.data;
                var Labels = new Array();
                var Data = new Array();
                Content.forEach(function(data){
                    Labels.push(data.datum);
                    Data.push(data.gemiddeldeduur);
                });
                let tc = {
                    type: 'bar',
                    data: {
                        labels: Labels,
                        datasets: [{
                            data: Data,
                            backgroundColor: ['#01689b'],
                            label: 'Duur inventarisaties (dagen)',
                        }]
                    },
                    options: {
                        indexAxis: 'y',
                        plugins: {
                            title: {
                                display: true,
                                text: 'Gemiddelde duur inventarisaties',
                                align: 'start',
                                font: {
                                    size: 15
                                }
                            },
                            tooltips: {
                                enabled: false
                            },
                        }
                    }
                };
                ctx = new Chart(document.getElementById('gemiddeldeduurinventarisatie'),tc);
            },
            error:function(error){
                console.log(error);
            }
        });
    }
    // Inventarisatie tijdig uitgevoerd (%)
    function inventarisatietijdiguitgevoerd(){
        $.ajax({
            method:"POST",
            url: "{{ url('/dashboard/api/statistiek') }}",
            data: {
                statistiek: "inventarisatietijdiguitgevoerd",
                _token: @json(csrf_token())
            },
            success:function(response){
                var Labels = ['Tijdig uitgevoerd', 'Niet tijdig uitgevoerd'];
                var Data = [response.data.tijdiguitgevoerd.map(item => item.aantal), response.data.niet_tijdiguitgevoerd.map(item => item.aantal)];
                var JaarLabels = response.data.tijdiguitgevoerd.map(item => item.jaar);

                let tc = {
                    type: 'bar',
                    data: {
                        labels: JaarLabels,
                        datasets: [{
                            label: Labels[0],
                            data: Data[0],
                            backgroundColor: '#258D07'
                        }, {
                            label: Labels[1],
                            data: Data[1],
                            backgroundColor: '#D20000'
                        }]
                    },
                    options: {
                        indexAxis:'y',
                        plugins: {
                            title: {
                                display: true,
                                text: 'Inventarisatie tijdig uitgevoerd per jaar',
                                align: 'start',
                                font: {
                                    size: 15
                                }
                            },
                            legend: {
                                position: 'top'
                            }
                        },
                    }
                };
                ctx = new Chart(document.getElementById('inventarisatietijdiguitgevoerd'), tc);
            },
            error:function(error){
                console.log(error);
            }
        });
    }

    // Reden inventarisatie later uitgevoerd
    function redenlateruitgevoerdinventarisatie() {
    $.ajax({
        method: "POST",
        url: "{{ url('/dashboard/api/statistiek') }}",
        data: {
            statistiek: "redenlateruitgevoerdinventarisatie",
            _token: @json(csrf_token())
        },
        success: function(response) {
            var Content = response.data;
            var Labels = new Array();
            var Data = new Array();
            var total = 0;
            Content.forEach(function(data) {
                Labels.push(data.redeninventarisatielater || "Onbekend");
                Data.push(data.aantalkeer);
            });

            total = Data.reduce(function(sum, value) {
                return sum + value;
            }, 0);

            var percentageData = Data.map(function(d) {
                return ((d / total) * 100).toFixed(2);
            });

            var percentageLabels = Labels.map(function(label, index) {
                return label + ' (' + percentageData[index] + '%)';
            });

            let tc = {
                type: 'pie',
                data: {
                    labels: percentageLabels,
                    datasets: [{
                        data: Data,
                        backgroundColor: ['#01689b', '#FF9900', '#165504', '#990099', '#0099c6', '#dd4477', '#66aa00', '#b82e2e', '#bea413', '#974499'],
                    }]
                },
                options: {
                    plugins: {
                        title: {
                            display: true,
                            text: 'Reden later uitvoeren van inventarisatie',
                            align: 'start',
                            font: {
                                size: 15
                            }
                        }
                    }
                }
            };
            ctx = new Chart(document.getElementById('redenlateruitgevoerdinventarisatie'), tc);
        },
        error: function(error) {
            console.log(error);
        }
    });
}

    // Inventarisatierapport tijdig opgeleverd (%)
    function inventarisatierapporttijdig(){
        $.ajax({
            method:"POST",
            url: "{{ url('/dashboard/api/statistiek') }}",
            data: {
                statistiek: "inventarisatierapporttijdig",
                _token: @json(csrf_token())
            },
            success:function(response){
                var Labels = ['Tijdig uitgevoerd', 'Niet tijdig uitgevoerd'];
                var Data = [response.data.rapporttijdig.map(item => item.aantal), response.data.niet_rapporttijdig.map(item => item.aantal)];
                var JaarLabels = response.data.rapporttijdig.map(item => item.jaar);

                let tc = {
                    type: 'bar',
                    data: {
                        labels: JaarLabels,
                        datasets: [{
                            label: Labels[0],
                            data: Data[0],
                            backgroundColor: '#258D07'
                        }, {
                            label: Labels[1],
                            data: Data[1],
                            backgroundColor: '#D20000'
                        }]
                    },
                    options: {
                        indexAxis:'y',
                        plugins: {
                            title: {
                                display: true,
                                text: 'Inventarisatierapport tijdig opgeleverd per jaar',
                                align: 'start',
                                font: {
                                    size: 15
                                }
                            },
                            legend: {
                                position: 'top'
                            }
                        },
                    }
                };
                ctx = new Chart(document.getElementById('inventarisatierapporttijdig'), tc);
            },
            error:function(error){
                console.log(error);
            }
        });
    }

    // Gemiddelde duur aanlevering rapport
    function gemiddeldeduuraanleveringrapport(){
        $.ajax({
            method:"POST",
            url: "{{ url('/dashboard/api/statistiek') }}",
            data: {
                statistiek: "gemiddeldeduuraanleveringrapport",
                _token: @json(csrf_token())
            },
            success:function(response){
                var Content = response.data;
                var Labels = new Array();
                var Data = new Array();
                Content.forEach(function(data){
                    Labels.push(data.datum);
                    Data.push(data.gemiddeldeduur);
                });
                let tc = {
                    type: 'bar',
                    data: {
                        labels: Labels,
                        datasets: [{
                            data: Data,
                            backgroundColor: ['#01689b'],
                            label: 'Gemiddelde projectduur (dagen)',
                        }]
                    },
                    options: {
                        indexAxis: 'y',
                        plugins: {
                            title: {
                                display: true,
                                text: 'Gemiddelde duur aanlevering rapport',
                                align: 'start',
                                font: {
                                    size: 15
                                }
                            },
                            tooltips: {
                                enabled: false
                            },
                        }
                    }
                };
                ctx = new Chart(document.getElementById('gemiddeldeduuraanleveringrapport'),tc);
            },
            error:function(error){
                console.log(error);
            }
        });
    }

    // Meest voorkomende bronlocaties (top 10)
    function meestvoorkomendebronlocaties(){
        $.ajax({
            method:"POST",
            url: "{{ url('/dashboard/api/statistiek') }}",
            data: {
                statistiek: "meestvoorkomendebronlocaties",
                _token: @json(csrf_token())
            },
            success:function(response){
                var Content = response.data;
                var Labels = new Array();
                var Data = new Array();
                var total = 0;
                Content.forEach(function(data){
                    total += data.aantalkeer;
                });
                Content.forEach(function(data){
                    var percentage = Math.round(data.aantalkeer / total * 100);
                    Labels.push(data.locatie + " (" + percentage + "%)");
                    Data.push(data.aantalkeer);
                });
                let tc = {
                    type: 'pie',
                    data: {
                        labels: Labels,
                        datasets: [{
                            data: Data,
                            backgroundColor: ['#01689b','#FF9900','#165504', '#990099', '#0099c6', '#dd4477', '#66aa00', '#b82e2e', '#bea413', '#974499'],
                        }]
                    },
                    options: {
                        plugins: {
                            title: {
                                display: true,
                                text: 'Meest voorkomende bronlocaties (top 10)',
                                align: 'start',
                                font: {
                                    size: 15
                                }
                            }
                        }
                    }
                };
                ctx = new Chart(document.getElementById('meestvoorkomendebronlocaties'),tc);
            },
            error:function(error){
                console.log(error);
            }
        });
    }

    // Meest voorkomende bronnamen (top 10)
    function meestvoorkomendebronnamen(){
        $.ajax({
            method:"POST",
            url: "{{ url('/dashboard/api/statistiek') }}",
            data: {
                statistiek: "meestvoorkomendebronnamen",
                _token: @json(csrf_token())
            },
            success:function(response){
                var Content = response.data;
                var Labels = new Array();
                var Data = new Array();
                Content.forEach(function(data){
                    Labels.push(data.omschrijving);
                    Data.push(data.aantalkeer);
                });
                let tc = {
                    type: 'pie',
                    data: {
                        labels: Labels,
                        datasets: [{
                            data: Data,
                            backgroundColor: ['#01689b','#FF9900','#165504', '#990099', '#0099c6', '#dd4477', '#66aa00', '#b82e2e', '#bea413', '#974499'],
                        }]
                    },
                    options: {
                    plugins: {
                    title: {
                        display: true,
                        text: 'Meest voorkomende bronnamen (top 10)',
                        align: 'start',
                                font: {
                                    size: 15
                                }
                    },
                    }
                }
                };
                ctx = new Chart(document.getElementById('meestvoorkomendebronnamen'),tc);
            },
            error:function(error){
                console.log(error);
            }
        });
    }

    // Meest voorkomende bronnamen (top 10)
    function meestgesaneerdebronnamen(){
        $.ajax({
            method:"POST",
            url: "{{ url('/dashboard/api/statistiek') }}",
            data: {
                statistiek: "meestgesaneerdebronnamen",
                _token: @json(csrf_token())
            },
            success:function(response){
                var Content = response.data;
                var Labels = new Array();
                var Data = new Array();
                Content.forEach(function(data){
                    Labels.push(data.omschrijving);
                    Data.push(data.aantalkeer);
                });
                let tc = {
                    type: 'pie',
                    data: {
                        labels: Labels,
                        datasets: [{
                            data: Data,
                            backgroundColor: ['#01689b','#FF9900','#165504', '#990099', '#0099c6', '#dd4477', '#66aa00', '#b82e2e', '#bea413', '#974499'],
                        }]
                    },
                    options: {
                    plugins: {
                    title: {
                        display: true,
                        text: 'Meest gesaneerde bronnamen (top 10)',
                        align: 'start',
                                font: {
                                    size: 15
                                }
                    },
                    }
                }
                };
                ctx = new Chart(document.getElementById('meestgesaneerdebronnamen'),tc);
            },
            error:function(error){
                console.log(error);
            }
        });
    }

    // Zelf aangebrachte voorzieningen (%)
    function zav(){
        $.ajax({
            method:"POST",
            url: "{{ url('/dashboard/api/statistiek') }}",
            data: {
                statistiek: "zav",
                _token: @json(csrf_token())
            },
            success:function(response){
                var Content = response.data;
                var Labels = new Array();
                var Data = new Array();
                Content.forEach(function(data){
                    Labels.push(data.zav);
                    Data.push(data.aantalkeer);
                });
                let tc = {
                    type: 'pie',
                    data: {
                        labels: Labels,
                        datasets: [{
                            data: Data,
                            backgroundColor: ['#01689b','#FF9900','#165504'],
                        }]
                    },
                    options: {
                    plugins: {
                    title: {
                        display: true,
                        text: 'Zelf aangebrachte voorzieningen (z.a.v)',
                        align: 'start',
                                font: {
                                    size: 15
                                }
                    },
                    tooltips: {
                        enabled: false
                    },
                    }
                }
                };
                ctx = new Chart(document.getElementById('zav'),tc);
            },
            error:function(error){
                console.log(error);
            }
        });
    }
// Aannemers
// WANNEER ER GEBRUIKERS(/AANNEMERS) IN DE HOOFDDB ZITTEN, MOET ID VERVANGEN WORDEN MET DE NAAM
    // Aantal projecten per aannemer per maand
    function aantalprojectenpermaandperaannemer(){
        $.ajax({
            method:"POST",
            url: "{{ url('/dashboard/api/statistiek') }}",
            data: {
                statistiek: "aantalprojectenpermaandperaannemer",
                _token: @json(csrf_token())
            },
            success:function(response){
                var Content = response.data;
                var Labels = new Array();
                var Datasets = new Array();
                var Colors = ['#01689b','#FF9900','#165504', '#990099', '#0099c6', '#dd4477', '#66aa00', '#b82e2e', '#bea413', '#974499', '#22aa99', '#aaaa11', '#6633cc', '#e67300', '#8b0707'];
                var ColorIndex = 0;
                Content.forEach(function(data){
                    var aannemerIndex = data.aannemerid - 1;
                    var labelIndex = Labels.indexOf(data.datum);
                    if(labelIndex == -1){
                        Labels.push(data.datum);
                        labelIndex = Labels.length - 1;
                    }
                    if(Datasets[aannemerIndex] == undefined){
                        Datasets[aannemerIndex] = {
                            data: new Array(),
                            backgroundColor: new Array()
                        };
                    }
                    Datasets[aannemerIndex].data[labelIndex] = data.aantal;
                    Datasets[aannemerIndex].backgroundColor = Colors[ColorIndex];
                    Datasets[aannemerIndex].label = 'Aannemer ' + data.aannemerid;
                    ColorIndex = (ColorIndex + 1) % Colors.length;
                });
                var datasets = new Array();
                for(var i = 0; i < Datasets.length; i++){
                    datasets.push(Datasets[i]);
                }
                let tc = {
                    type: 'bar',
                    data: {
                        labels: Labels,
                        datasets: datasets
                    },
                    options: {
                        indexAxis: 'y',
                        plugins: {
                            title: {
                                display: true,
                                text: 'Aantal projecten aangemaakt per maand per aannemer',
                                align: 'start',
                                font: {
                                    size: 15
                                }
                            },
                            tooltips: {
                                enabled: false
                            },
                        }
                    }
                };
                ctx = new Chart(document.getElementById('aantalprojectenpermaandperaannemer'),tc);
            },
            error:function(error){
                console.log(error);
            }
        });
    }
    // Aantal projecten per aannemer per jaar
    function aantalprojectenperjaarperaannemer(){
        $.ajax({
            method:"POST",
            url: "{{ url('/dashboard/api/statistiek') }}",
            data: {
                statistiek: "aantalprojectenperjaarperaannemer",
                _token: @json(csrf_token())
            },
            success:function(response){
                var Content = response.data;
                var Labels = new Array();
                var Datasets = new Array();
                var Colors = ['#01689b','#FF9900','#165504', '#990099', '#0099c6', '#dd4477', '#66aa00', '#b82e2e', '#bea413', '#974499', '#22aa99', '#aaaa11', '#6633cc', '#e67300', '#8b0707'];
                var ColorIndex = 0;
                Content.forEach(function(data){
                    var aannemerIndex = data.aannemerid - 1;
                    var labelIndex = Labels.indexOf(data.datum);
                    if(labelIndex == -1){
                        Labels.push(data.datum);
                        labelIndex = Labels.length - 1;
                    }
                    if(Datasets[aannemerIndex] == undefined){
                        Datasets[aannemerIndex] = {
                            data: new Array(),
                            backgroundColor: new Array()
                        };
                    }
                    Datasets[aannemerIndex].data[labelIndex] = data.aantal;
                    Datasets[aannemerIndex].backgroundColor = Colors[ColorIndex];
                    Datasets[aannemerIndex].label = 'Aannemer ' + data.aannemerid;
                    ColorIndex = (ColorIndex + 1) % Colors.length;
                });
                var datasets = new Array();
                for(var i = 0; i < Datasets.length; i++){
                    datasets.push(Datasets[i]);
                }
                let tc = {
                    type: 'bar',
                    data: {
                        labels: Labels,
                        datasets: datasets
                    },
                    options: {
                        indexAxis: 'y',
                        plugins: {
                            title: {
                                display: true,
                                text: 'Aantal projecten aangemaakt per jaar per aannemer',
                                align: 'start',
                                font: {
                                    size: 15
                                }
                            },
                            tooltips: {
                                enabled: false
                            },
                        }
                    }
                };
                ctx = new Chart(document.getElementById('aantalprojectenperjaarperaannemer'),tc);
            },
            error:function(error){
                console.log(error);
            }
        });
    }
// Kosten
    // Totale factuurwaarde projecten per maand
    function totalefactuurwaarde(){
        $.ajax({
            method:"POST",
            url: "{{ url('/dashboard/api/statistiek') }}",
            data: {
                statistiek: "totalefactuurwaarde",
                _token: @json(csrf_token())
            },
            success:function(response){
                var Content = response.data;
                var Labels = new Array();
                var Data = new Array();
                Content.forEach(function(data){
                    Labels.push(data.datum);
                    Data.push(data.totaal);
                });
                let tc = {
                    type: 'bar',
                    data: {
                        labels: Labels,
                        datasets: [{
                            data: Data,
                            backgroundColor: ['#01689b'],
                            label: 'Totale kosten',
                        }]
                    },
                    options: {
                        indexAxis: 'y',
                        plugins: {
                            title: {
                                display: true,
                                text: 'Totale factuurwaarde projecten per maand',
                                align: 'start',
                                font: {
                                    size: 15
                                }
                            },
                            tooltips: {
                                enabled: false
                            },
                        }
                    }
                };
                ctx = new Chart(document.getElementById('totalefactuurwaarde'),tc);
            },
            error:function(error){
                console.log(error);
            }
        });
    }
    // Gemiddelde factuurwaarde
    function gemiddeldefactuurwaarde(){
        $.ajax({
            method:"POST",
            url: "{{ url('/dashboard/api/statistiek') }}",
            data: {
                statistiek: "gemiddeldefactuurwaarde",
                _token: @json(csrf_token())
            },
            success:function(response){
                var Content = response.data;
                var Labels = new Array();
                var Data = new Array();
                Content.forEach(function(data){
                    Labels.push(data.datum);
                    Data.push(data.totaal);
                });
                let tc = {
                    type: 'bar',
                    data: {
                        labels: Labels,
                        datasets: [{
                            data: Data,
                            backgroundColor: ['#01689b'],
                            label: 'Gemiddelde kosten',
                        }]
                    },
                    options: {
                        indexAxis: 'y',
                        plugins: {
                            title: {
                                display: true,
                                text: 'Gemiddelde factuurwaarde projecten per maand',
                                align: 'start',
                                font: {
                                    size: 15
                                }
                            },
                            tooltips: {
                                enabled: false
                            },
                        }
                    }
                };
                ctx = new Chart(document.getElementById('gemiddeldefactuurwaarde'),tc);
            },
            error:function(error){
                console.log(error);
            }
        });
    }
</script>
@endsection

