@extends('layouts.app')

@section('content')

    <section>

      <div class="flex-end">

            <div>
                <infor-select-search id="stap" class="form-control rounded-pill min-w-250" placeholder="Selecteer stap" >
                    @foreach($manager->proces->activeStappen(['include_parent' => false]) as $proces_stap)
                        <infor-select-option data-name="{{$proces_stap->stapNr()}}. {{$proces_stap->stapnaam}}" data-value="{{$proces_stap->id}}" @if($proces_stap->id == $stap->id) data-selected @endif >
                            <div class="nobr">{{$proces_stap->stapNr()}}. {{$proces_stap->stapnaam}}</div>
                        </infor-select-option>
                    @endforeach
                </infor-select-search>
            </div>

      </div>

      <div class="row">
        <livewire:instellingen.stappen.gebruikers :manager="$manager"></livewire:instellingen.stappen.gebruikers>
        <livewire:instellingen.stappen.inputs :manager="$manager"></livewire:instellingen.stappen.inputs>
        <livewire:instellingen.stappen.settings :manager="$manager" ></livewire:instellingen.stappen.settings>
        <livewire:instellingen.stappen.kpi :manager="$manager" ></livewire:instellingen.stappen.kpi>
        <livewire:instellingen.stappen.acties :manager="$manager"></livewire:instellingen.stappen.acties>
        <livewire:instellingen.stappen.mails :manager="$manager" ></livewire:instellingen.stappen.mails>
      </div>

    </section>

@endsection
@section('script')
<script>

    $(document).ready(() => {
       _inforSelectSearch.get('stap').onchange = id => redirect(`instellingen/stappen/${id}`);
    });

</script>
@endsection
