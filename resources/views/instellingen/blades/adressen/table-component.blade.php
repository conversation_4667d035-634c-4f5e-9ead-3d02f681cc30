<div>

    <div class="card">
        <div class="card-title">Project Bestanden</div>
        <div class="row">
            @foreach(getActiveProcessen() as $proces)
                <div class="flex-center my-2">
                    <div class="full-divider"></div>
                    <div class="mx-2 nobr text-muted font-size-1" >{{$proces->naam}}</div>
                    <div class="full-divider"></div>
                </div>

                @foreach($proces->fileKeys() as $file_key)
                    <div class="col-md-4 col-12">
                        <label class="form-switch-label">
                            @if($livewire->getValue("checkbox.adressen_table_component_file.{$file_key}"))
                                <span class="dot-glow dot-glow-success mr-2" ></span>
                            @else
                                <span class="dot-glow dot-glow-danger mr-2" ></span>
                            @endif
                            <input type="checkbox" class="d-none" wire:model="checkbox.adressen_table_component_file.{{$file_key}}"  >
                            <span>{{snakeToSpaceCase($file_key)}}</span>
                        </label>
                    </div>
                @endforeach
            @endforeach
        </div>
    </div>

</div>
