<div>
    @foreach(getActiveProcessen() as $proces)
        <div class="card my-2 border rounded-5">
            <div class="card-title" >{{$proces->naam}}</div>
            <div class="row">

                <div class="flex-center my-2">
                    <div class="full-divider"></div>
                    <div class="mx-2 nobr text-muted font-size-1" >Gebruikers</div>
                    <div class="full-divider"></div>
                </div>
                @foreach(getRoles(1) as $role)
                    <div class="col-md-4 col-12">
                        <label class="form-switch-label">
                            @if($livewire->getValue("checkbox.proces_{$proces->id}_overview_roles.{$role->id}"))
                                <span class="dot-glow dot-glow-success mr-2" ></span>
                            @else
                                <span class="dot-glow dot-glow-danger mr-2" ></span>
                            @endif
                            <input type="checkbox" class="d-none" wire:model="checkbox.proces_{{$proces->id}}_overview_roles.{{$role->id}}">
                            <span>{{$role->name}}</span>
                        </label>
                    </div>
                @endforeach
                @foreach($proces->activeInputs()->groupBy('context') as $context => $inputs)
                    <div class="flex-center my-2">
                        <div class="full-divider"></div>
                        <div class="mx-2 nobr text-muted font-size-1" >{{ ProjectStapContext::get($context, 'label') }}</div>
                        <div class="full-divider"></div>
                    </div>

                    @foreach($inputs as $input)
                        <div class="col-md-4 col-12">
                            <label class="form-switch-label">
                                @if($livewire->getValue("checkbox.proces_{$proces->id}_overview_inputs.{$input->id}"))
                                    <span class="dot-glow dot-glow-success mr-2" ></span>
                                @else
                                    <span class="dot-glow dot-glow-danger mr-2" ></span>
                                @endif
                                <input type="checkbox" class="d-none" wire:model="checkbox.proces_{{$proces->id}}_overview_inputs.{{$input->id}}" >
                                <span>{{$input->titel}}</span>
                            </label>
                        </div>
                    @endforeach
                @endforeach

            </div>
        </div>
    @endforeach
</div>
