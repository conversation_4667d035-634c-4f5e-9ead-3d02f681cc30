@extends('layouts.app')

@section('content')
    <section>

        <infor-table id="bedrijven_table" >

            <infor-table-pagination>
                <infor-table-order-option data-column="naam" >Bedrijfsnaam</infor-table-order-option>
                <infor-table-order-option data-column="email" >Email</infor-table-order-option>
                <infor-table-order-option data-column="telefoon" >Telefoonnummer</infor-table-order-option>
            </infor-table-pagination>

            <infor-table-count data-single="Bedrijf" data-multiple="Bedrijven" ></infor-table-count>

            <infor-table-header>Logo</infor-table-header>
            <infor-table-header>Bedrijfsnaam</infor-table-header>
            <infor-table-header>Email</infor-table-header>
            <infor-table-header>Telefoonnummer</infor-table-header>
            <infor-table-header></infor-table-header>


        </infor-table>

    </section>
@endsection

@section('script')
    <script>

        pageInteractive(() => {
          initInforTables({
            id: 'bedrijven_table',
            api: `/dashboard/api/bedrijven/get`,
            response_records_name: 'bedrijven',
            search: {
              api: 'dashboard/api/search/bedrijf',
              content: 'naam',
              sub_content: `email`,
              placeholder: `Zoeken...`,
            },
            errorHandler: handleAjaxErrors,
            fillRecord: fillRecord,
            execute: [ tippyInit ],
          })
        })

        function fillRecord(record){
            const { id, logo_guid, naam, email, telefoon} = record;

            return `
                <tr  >
                    <td class="w-0" >${logo_guid ? `<img height="35" src="${_file(logo_guid)}" >` : ''}</td>
                    <td>${naam || ''}</td>
                    <td>${email || ''}</td>
                    <td>${telefoon || ''}</td>
                    <td class="text-right" >
                        <a class="btn btn-light" href="${_url(`/dashboard/bedrijven/edit/${id}`)}" >Wijzigen @icon_edit</a>
                    </td>
                </tr>
            `
        }

    </script>
@endsection
