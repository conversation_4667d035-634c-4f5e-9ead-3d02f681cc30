@extends('layouts.app')

@section('content')
    <section>

        <infor-table id="contacten_table" >

            <infor-table-pagination>
                <infor-table-order-option data-column="name" >Naam</infor-table-order-option>
                <infor-table-order-option data-column="email" >Email</infor-table-order-option>
                <infor-table-order-option data-column="telefoon" >Telefoon</infor-table-order-option>
                <infor-table-order-option data-column="partijnaam" >Partij</infor-table-order-option>
            </infor-table-pagination>

            <infor-table-count data-single="Gebruiker" data-multiple="Gebruikers" ></infor-table-count>

            <infor-table-header>Naam</infor-table-header>
            <infor-table-header>Email</infor-table-header>
            <infor-table-header>Telefoon</infor-table-header>
            <infor-table-header>Role</infor-table-header>
            <infor-table-header>Partij</infor-table-header>
            <infor-table-header></infor-table-header>


        </infor-table>

    </section>
@endsection

@section('script')
    <script>

        pageInteractive(() => {
          initInforTables({
            id: 'contacten_table',
            api: `/dashboard/api/contacten/get`,
            response_records_name: 'contacten',
            search: {
              api: 'dashboard/api/search/contact',
              content: 'name',
              sub_content: `email`,
              placeholder: `Zoeken...`,
            },
            errorHandler: handleAjaxErrors,
            fillRecord: fillRecord,
            execute: [ tippyInit ],
          })
        })

        function fillRecord(record){
            const { id, name, email, telefoon, role, partijnaam } = record;

            return `
                <tr>
                    <td>${name || ''}</td>
                    <td>${email || ''}</td>
                    <td>${telefoon || ''}</td>
                    <td>${role?.name || ''}</td>
                    <td>${partijnaam || ''}</td>
                    <td class="text-right" >
                        <a class="btn btn-light" href="${_url(`/dashboard/contacten/edit/${id}`)}" >Wijzigen @icon_edit</a>
                    </td>
                </tr>
            `
        }

    </script>
@endsection
